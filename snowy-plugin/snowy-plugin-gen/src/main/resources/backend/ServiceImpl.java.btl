/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package ${packageName}.${moduleName}.modular.${busName}.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.data.annotation.Tran;
import ${packageName}.common.enums.CommonSortOrderEnum;
import ${packageName}.common.exception.CommonException;
import ${packageName}.common.page.CommonPageRequest;
import ${packageName}.${moduleName}.modular.${busName}.entity.${className};
import ${packageName}.${moduleName}.modular.${busName}.mapper.${className}Mapper;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}AddParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}EditParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}IdParam;
import ${packageName}.${moduleName}.modular.${busName}.param.${className}PageParam;
import ${packageName}.${moduleName}.modular.${busName}.service.${className}Service;

import java.util.List;

/**
 * ${functionName}Service接口实现类
 *
 * <AUTHOR>
 * @date ${genTime}
 **/
@Component
public class ${className}ServiceImpl extends ServiceImpl<${className}Mapper, ${className}> implements ${className}Service {

    @Override
    public Page<${className}> page(${className}PageParam ${classNameFirstLower}PageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(configList[i].needPage) { %>
        <% if(configList[i].effectType == 'datepicker') { %>
        if(ObjectUtil.isNotEmpty(${classNameFirstLower}PageParam.getStart${configList[i].fieldNameCamelCaseFirstUpper}()) && ObjectUtil.isNotEmpty(${classNameFirstLower}PageParam.getEnd${configList[i].fieldNameCamelCaseFirstUpper}())) {
            queryWrapper.between(${className}::get${configList[i].fieldNameCamelCaseFirstUpper}, ${classNameFirstLower}PageParam.getStart${configList[i].fieldNameCamelCaseFirstUpper}(), ${classNameFirstLower}PageParam.getEnd${configList[i].fieldNameCamelCaseFirstUpper}());
        }
        <% } else { %>
        if(ObjectUtil.isNotEmpty(${classNameFirstLower}PageParam.get${configList[i].fieldNameCamelCaseFirstUpper}())) {
            queryWrapper.${configList[i].needPageType}(${className}::get${configList[i].fieldNameCamelCaseFirstUpper}, ${classNameFirstLower}PageParam.get${configList[i].fieldNameCamelCaseFirstUpper}());
        }
        <% } %>
        <% } %>
        <% } %>
        if(ObjectUtil.isAllNotEmpty(${classNameFirstLower}PageParam.getSortField(), ${classNameFirstLower}PageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(${classNameFirstLower}PageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(${classNameFirstLower}PageParam.getSortField()),${classNameFirstLower}PageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            <% if(hasSortCodeField) { %>
            queryWrapper.orderBy(${className}::getSortCode);
            <% } else { %>
            queryWrapper.orderBy(${className}::get${dbTableKeyFirstUpper});
            <% } %>
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public void add(${className}AddParam ${classNameFirstLower}AddParam) {
        ${className} ${classNameFirstLower} = BeanUtil.toBean(${classNameFirstLower}AddParam, ${className}.class);
        this.save(${classNameFirstLower});
    }

    @Tran
    @Override
    public void edit(${className}EditParam ${classNameFirstLower}EditParam) {
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(configList[i].needTableId) { %>
        ${className} ${classNameFirstLower} = this.queryEntity(${classNameFirstLower}EditParam.get${configList[i].fieldNameCamelCaseFirstUpper}());
        <% } %>
        <% } %>
        BeanUtil.copyProperties(${classNameFirstLower}EditParam, ${classNameFirstLower});
        this.updateById(${classNameFirstLower});
    }

    @Tran
    @Override
    public void delete(List<${className}IdParam> ${classNameFirstLower}IdParamList) {
        // 执行删除
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(configList[i].needTableId) { %>
        this.removeByIds(CollStreamUtil.toList(${classNameFirstLower}IdParamList, ${className}IdParam::get${configList[i].fieldNameCamelCaseFirstUpper}));
        <% } %>
        <% } %>
    }

    @Override
    public ${className} detail(${className}IdParam ${classNameFirstLower}IdParam) {
        <% for(var i = 0; i < configList.~size; i++) { %>
        <% if(configList[i].needTableId) { %>
        return this.queryEntity(${classNameFirstLower}IdParam.get${configList[i].fieldNameCamelCaseFirstUpper}());
        <% } %>
        <% } %>
    }

    @Override
    public ${className} queryEntity(String id) {
        ${className} ${classNameFirstLower} = this.getById(id);
        if(ObjectUtil.isEmpty(${classNameFirstLower})) {
            throw new CommonException("${functionName}不存在，id值为：{}", id);
        }
        return ${classNameFirstLower};
    }
}
