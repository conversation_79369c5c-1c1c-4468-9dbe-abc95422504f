package vip.xiaonuo.sys.modular.resource.provider;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import vip.xiaonuo.sys.api.SysModuleApi;
import vip.xiaonuo.sys.modular.resource.param.module.SysModulePageParam;
import vip.xiaonuo.sys.modular.resource.service.SysModuleService;

@Component
public class SysModuleApiProvider implements SysModuleApi {

    @Inject
    private SysModuleService sysModuleService;


    @Override
    public Page<JSONObject> moduleSelector() {
        SysModulePageParam sysModulePageParam = new SysModulePageParam();
        return BeanUtil.toBean(sysModuleService.page(sysModulePageParam), Page.class);
    }
}
