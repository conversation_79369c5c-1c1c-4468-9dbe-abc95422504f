/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.biz.modular.realty.param.*;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReParkLedgerInfo;
import vip.xiaonuo.biz.modular.realty.service.ReParkLedgerInfoService;

/**
 * 车位台账信息控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 */
@Api(tags = "车位台账信息控制器")
@Controller
@Valid
public class ReParkLedgerInfoController {

    @Inject
    private ReParkLedgerInfoService reParkLedgerInfoService;

    /**
     * 获取车位台账信息分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取车位台账信息分页")
    @SaCheckPermission("/biz/reparkledgerinfo/page")
    @Get
    @Mapping("/biz/reparkledgerinfo/page")
    public CommonResult<Page<ReParkLedgerInfo>> page(ReParkLedgerInfoPageParam reParkLedgerInfoPageParam) {
        return CommonResult.data(reParkLedgerInfoService.page(reParkLedgerInfoPageParam));
    }

    /**
     * 添加车位台账信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("添加车位台账信息")
    @CommonLog("添加车位台账信息")
    @SaCheckPermission("/biz/reparkledgerinfo/add")
    @Post
    @Mapping("/biz/reparkledgerinfo/add")
    public CommonResult<String> add(ReParkLedgerInfoAddParam reParkLedgerInfoAddParam) {
        reParkLedgerInfoService.add(reParkLedgerInfoAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑车位台账信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("编辑车位台账信息")
    @CommonLog("编辑车位台账信息")
    @SaCheckPermission("/biz/reparkledgerinfo/edit")
    @Post
    @Mapping("/biz/reparkledgerinfo/edit")
    public CommonResult<String> edit(ReParkLedgerInfoEditParam reParkLedgerInfoEditParam) {
        reParkLedgerInfoService.edit(reParkLedgerInfoEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除车位台账信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("删除车位台账信息")
    @CommonLog("删除车位台账信息")
    @SaCheckPermission("/biz/reparkledgerinfo/delete")
    @Post
    @Mapping("/biz/reparkledgerinfo/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReParkLedgerInfoIdParam> reParkLedgerInfoIdParamList) {
        reParkLedgerInfoService.delete(reParkLedgerInfoIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取车位台账信息详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取车位台账信息详情")
    @SaCheckPermission("/biz/reparkledgerinfo/detail")
    @Get
    @Mapping("/biz/reparkledgerinfo/detail")
    public CommonResult<ReParkLedgerInfo> detail(ReParkLedgerInfoIdParam reParkLedgerInfoIdParam) {
        return CommonResult.data(reParkLedgerInfoService.detail(reParkLedgerInfoIdParam));
    }


    /**
     * 通过销控新增车位台账信息
     *
     * <AUTHOR>
     * @date 2024/8/20 15:16
     */
    @ApiOperation("通过销控新增车位台账信息")
    @CommonLog("通过销控新增车位台账信息")
    @Post
    @Mapping("/biz/reparkledgerinfo/addBySaleControl")
    public CommonResult<String> addBySaleControl(@Body ReParkLedgerInfoXiaoKongParam reParkLedgerInfoXiaoKongParam) {
        reParkLedgerInfoService.addBySaleControl(reParkLedgerInfoXiaoKongParam);
        return CommonResult.ok();
    }

    /**
     * 通过销控查询车位台账详情
     *
     * <AUTHOR>
     * @date 2024/8/20 15:59
     */
    @ApiOperation("通过销控查询车位台账详情")
    @Get
    @Mapping("/biz/reparkledgerinfo/detailBySaleControl")
    public CommonResult<ReParkLedgerInfoXiaoKongParamVo> detailBySaleControl(ReParkLedgerInfoIdParam reParkLedgerInfoIdParam) {
        return CommonResult.data(reParkLedgerInfoService.detailBySaleControl(reParkLedgerInfoIdParam));
    }

}
