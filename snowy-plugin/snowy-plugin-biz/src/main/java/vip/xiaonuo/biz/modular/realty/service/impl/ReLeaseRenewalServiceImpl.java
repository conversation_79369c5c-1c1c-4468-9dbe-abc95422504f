/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.constants.LedgerConstants;
import vip.xiaonuo.biz.modular.realty.dto.DuplicateCheckResult;
import vip.xiaonuo.biz.modular.realty.entity.ReLedgerInfo;
import vip.xiaonuo.biz.modular.realty.entity.ReLeaseContract;
import vip.xiaonuo.biz.modular.realty.param.ReLeaseRenewalParam;
import vip.xiaonuo.biz.modular.realty.service.LedgerDuplicateCheckService;
import vip.xiaonuo.biz.modular.realty.service.ReLeaseRenewalService;
import vip.xiaonuo.biz.modular.realty.service.ReLedgerInfoService;
import vip.xiaonuo.biz.modular.realty.service.ReLeaseContractService;
import vip.xiaonuo.common.exception.CommonException;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商业租赁续签服务实现类
 * 
 * <AUTHOR> 4.0 sonnet
 * @date 2024/12/19
 */
@Slf4j
@Component
public class ReLeaseRenewalServiceImpl implements ReLeaseRenewalService {

    @Inject
    private LedgerDuplicateCheckService duplicateCheckService;

    @Inject
    private ReLedgerInfoService reLedgerInfoService;

    @Inject
    private ReLeaseContractService reLeaseContractService;

    @Override
    @Tran
    public String renewLease(ReLeaseRenewalParam renewalParam) {
        log.info("开始处理商业租赁续签：房源ID={}, 客户={}, 交款日期={}", 
                renewalParam.getHouseId(), renewalParam.getCustomerName(), renewalParam.getPaymentDate());

        // 1. 参数验证
        validateRenewalParam(renewalParam);

        // 2. 重复性检查
        checkRenewalDuplicate(
                renewalParam.getHouseId(), 
                renewalParam.getCustomerName(), 
                renewalParam.getPaymentDate(), 
                renewalParam.getProjectId()
        );

        // 3. 将原台账标记为历史
        if (StrUtil.isNotEmpty(renewalParam.getOriginalLedgerId())) {
            updatePreviousLedgerToHistory(renewalParam.getOriginalLedgerId());
        }

        // 4. 创建新台账
        String newLedgerId = createNewLedger(renewalParam);

        // 5. 创建新的租赁合同
        createNewLeaseContract(renewalParam, newLedgerId);

        log.info("商业租赁续签完成，新台账ID：{}", newLedgerId);
        return newLedgerId;
    }

    @Override
    public void checkRenewalDuplicate(String houseId, String customerName, Date paymentDate, String projectId) {
        if (duplicateCheckService == null) {
            log.warn("重复检查服务未初始化，跳过重复检查");
            return;
        }

        DuplicateCheckResult result = duplicateCheckService.checkLeaseHouseLedgerDuplicate(
                houseId, customerName, paymentDate, projectId);

        if (result.isDuplicate()) {
            String errorMessage = String.format(
                    "续签失败：该房源在此交款日期已存在相同客户的台账记录。房源ID=%s, 客户=%s, 交款日期=%s", 
                    houseId, customerName, paymentDate);
            log.warn(errorMessage);
            throw new CommonException(errorMessage);
        }
    }

    @Override
    public Map<Integer, Boolean> batchCheckRenewalDuplicate(List<ReLeaseRenewalParam> renewalParams) {
        Map<Integer, Boolean> results = new HashMap<>();
        
        for (int i = 0; i < renewalParams.size(); i++) {
            ReLeaseRenewalParam param = renewalParams.get(i);
            try {
                checkRenewalDuplicate(
                        param.getHouseId(), 
                        param.getCustomerName(), 
                        param.getPaymentDate(), 
                        param.getProjectId()
                );
                results.put(i, false); // 无重复
            } catch (CommonException e) {
                results.put(i, true); // 有重复
                log.warn("批量检查发现重复：索引={}, 错误={}", i, e.getMessage());
            }
        }
        
        return results;
    }

    /**
     * 验证续签参数
     */
    private void validateRenewalParam(ReLeaseRenewalParam renewalParam) {
        if (renewalParam == null) {
            throw new CommonException("续签参数不能为空");
        }
        if (StrUtil.isEmpty(renewalParam.getHouseId())) {
            throw new CommonException("房源ID不能为空");
        }
        if (StrUtil.isEmpty(renewalParam.getCustomerName())) {
            throw new CommonException("客户姓名不能为空");
        }
        if (renewalParam.getPaymentDate() == null) {
            throw new CommonException("交款日期不能为空");
        }
        if (StrUtil.isEmpty(renewalParam.getProjectId())) {
            throw new CommonException("项目ID不能为空");
        }
    }

    /**
     * 将原台账标记为历史
     */
    private void updatePreviousLedgerToHistory(String originalLedgerId) {
        try {
            ReLedgerInfo originalLedger = reLedgerInfoService.getById(originalLedgerId);
            if (originalLedger != null) {
                originalLedger.setIsHistory(true);
                reLedgerInfoService.updateById(originalLedger);
                log.info("已将原台账标记为历史：{}", originalLedgerId);
            } else {
                log.warn("未找到原台账记录：{}", originalLedgerId);
            }
        } catch (Exception e) {
            log.error("更新原台账历史状态失败：{}", originalLedgerId, e);
            throw new CommonException("更新原台账历史状态失败", e);
        }
    }

    /**
     * 创建新台账
     */
    private String createNewLedger(ReLeaseRenewalParam renewalParam) {
        try {
            ReLedgerInfo newLedger = new ReLedgerInfo();
            
            // 基本信息
            newLedger.setHouseId(renewalParam.getHouseId());
            newLedger.setProjectId(renewalParam.getProjectId());
            newLedger.setName(renewalParam.getCustomerName());
            newLedger.setPhone(renewalParam.getCustomerPhone());
            newLedger.setIdCard(renewalParam.getCustomerIdCard());
            
            // 租赁相关信息
            newLedger.setContractType(LedgerConstants.CONTRACT_TYPE_LEASE); // 租赁类型
            newLedger.setHouseType(LedgerConstants.HOUSE_TYPE_COMMERCIAL); // 商业房屋
            newLedger.setSubscribeTime(renewalParam.getPaymentDate()); // 交款日期存储在认购时间字段
            newLedger.setContractTime(renewalParam.getContractDate());
            newLedger.setContractPrice(renewalParam.getContractPrice());
            newLedger.setTotalPayment(renewalParam.getTotalPayment());
            newLedger.setArea(renewalParam.getArea());
            newLedger.setDealUnitPrice(renewalParam.getUnitPrice());
            
            // 状态设置
            newLedger.setStatus(LedgerConstants.STATUS_SOLD_UNPAID); // 默认未结清
            newLedger.setIsHistory(false);
            
            // 其他信息
            newLedger.setBuildCode(renewalParam.getBuildCode());
            newLedger.setFloor(renewalParam.getFloor());
            newLedger.setUnit(renewalParam.getUnit());
            newLedger.setHouseNumber(renewalParam.getHouseNumber());
            
            reLedgerInfoService.save(newLedger);
            log.info("创建新台账成功：{}", newLedger.getId());
            return newLedger.getId();
            
        } catch (Exception e) {
            log.error("创建新台账失败", e);
            throw new CommonException("创建新台账失败", e);
        }
    }

    /**
     * 创建新的租赁合同
     */
    private void createNewLeaseContract(ReLeaseRenewalParam renewalParam, String newLedgerId) {
        try {
            ReLeaseContract newContract = new ReLeaseContract();
            
            newContract.setLedgerId(newLedgerId);
            newContract.setLeaseStartTime(renewalParam.getLeaseStartTime());
            newContract.setLeaseEndTime(renewalParam.getLeaseEndTime());
            newContract.setLeaseTerm(renewalParam.getLeaseTerm());
            newContract.setLeaseUnitPrice(renewalParam.getUnitPrice());
            newContract.setLeaseTotalPrice(renewalParam.getContractPrice());
            newContract.setLeaseDeposit(renewalParam.getDeposit());
            newContract.setPaymentMethod(renewalParam.getPaymentMethod());
            newContract.setRentFreePeriod(renewalParam.getRentFreePeriod());
            newContract.setDiscountRemark(renewalParam.getDiscountRemark());
            
            reLeaseContractService.save(newContract);
            log.info("创建新租赁合同成功：{}", newContract.getId());
            
        } catch (Exception e) {
            log.error("创建新租赁合同失败", e);
            throw new CommonException("创建新租赁合同失败", e);
        }
    }
}
