/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service;

import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import org.noear.solon.core.handle.Context;
import org.noear.solon.core.handle.UploadedFile;
import vip.xiaonuo.biz.modular.realty.entity.ReHouse;
import vip.xiaonuo.biz.modular.realty.param.*;
import vip.xiaonuo.common.pojo.CommonValidList;

import java.io.IOException;
import java.util.List;

/**
 * 房屋管理Service接口
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
public interface ReHouseService extends IService<ReHouse> {

    /**
     * 获取房屋管理分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    Page<ReHouse> page(ReHousePageParam reHousePageParam);

    /**
     * 添加房屋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void add(ReHouseAddParam reHouseAddParam);

    /**
     * 编辑房屋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void edit(ReHouseEditParam reHouseEditParam);

    /**
     * 删除房屋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void delete(List<ReHouseIdParam> reHouseIdParamList);

    /**
     * 获取房屋管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    ReHouse detail(ReHouseIdParam reHouseIdParam);

    /**
     * 获取房屋管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     **/
    ReHouse queryEntity(String id);


    /**
     * 通过楼栋获取房屋管理列表
     *
     * <AUTHOR>
     * @date 2024/8/20 9:17
     */
    List<JSONObject> listByBuild(ReBuildingIdParam reBuildingIdParam);

    /**
     * 导入房屋管理信息
     *
     * <AUTHOR>
     * @date 2024/8/21 17:06
     */
    void importHouseInfo(UploadedFile file, String projectId);


    /**
     * 导出房屋管理导入模板
     *
     * <AUTHOR>
     * @date 2024/8/22 18:40
     */
    void downloadImportRehouseTemplate(Context response,String type) throws IOException;

    /**
     * 退房操作
     *
     * <AUTHOR>
     * @date 2024/8/31 17:07
     */
    void checkout(CommonValidList<ReHouseIdParam> reHouseIdParamList);

    /**
     * 导出住宅台账信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2024/10/11 15:45
     */
    void exportHouseLedgerInfo(Context response, ReHousePageParam reHousePageParam);

    /**
     * 导出住宅税务信息
     *
     * @param
     * @return
     */
    void exportHouseTaxInfo(Context response, ReHousePageParam reHousePageParam);

    /**
     * 导出储藏间台账信息
     *
     * <AUTHOR>
     * @date 2024/10/12 15:08
     */
    void exportStoreRoomLedgerInfo(Context response, ReHousePageParam reHousePageParam);

    /**
     * 导入房屋台账信息
     *
     * <AUTHOR>
     * @date 2024/10/29 11:04
     */
    void importHouseLedgerInfo(UploadedFile file, String projectId);

    void resetHouse(String houseId);
}
