/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.afterturn.easypoi.cache.manager.POICacheManager;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryMethods;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.core.handle.Context;
import org.noear.solon.core.handle.UploadedFile;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.async.ReProjectDetailServiceAsync;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.listener.ImportHouseLedgerListener;
import vip.xiaonuo.biz.modular.realty.listener.ImportHouseListener;
import vip.xiaonuo.biz.modular.realty.mapper.ReHouseMapper;
import vip.xiaonuo.biz.modular.realty.param.*;
import vip.xiaonuo.biz.modular.realty.service.ReHouseService;
import vip.xiaonuo.biz.modular.realty.service.ReLedgerInfoService;
import vip.xiaonuo.biz.modular.realty.service.ReProjectDetailService;
import vip.xiaonuo.biz.modular.realty.service.ReProjectService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.common.util.CommonDownloadUtil;
import vip.xiaonuo.common.util.CommonResponseUtil;
import vip.xiaonuo.dev.api.DevDictApi;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static vip.xiaonuo.biz.modular.realty.entity.table.ReBuildingTableDef.RE_BUILDING;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReContractInfoTableDef.RE_CONTRACT_INFO;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReCustomerTableDef.RE_CUSTOMER;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReHouseTableDef.RE_HOUSE;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReLeaseContractTableDef.RE_LEASE_CONTRACT;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReLedgerInfoTableDef.RE_LEDGER_INFO;
import static vip.xiaonuo.biz.modular.realty.entity.table.RePlacementContractTableDef.RE_PLACEMENT_CONTRACT;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReSalesContractTableDef.RE_SALES_CONTRACT;

/**
 * 房屋管理Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/08/17 14:34
 **/
@Slf4j
@Component
public class ReHouseServiceImpl extends ServiceImpl<ReHouseMapper, ReHouse> implements ReHouseService {

    @Inject
    private ReProjectDetailService reProjectDetailService;

    @Inject
    private ReProjectService reProjectService;

    @Inject
    private ReProjectDetailServiceAsync reProjectDetailServiceAsync;

    @Inject
    private ReLedgerInfoService reLedgerInfoService;

    @Inject
    private DevDictApi dictApi;

    @Override
    public Page<ReHouse> page(ReHousePageParam reHousePageParam) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        RE_HOUSE.ALL_COLUMNS,
                        RE_LEDGER_INFO.CONTRACT_TYPE.as("contractStatus")
                ).from(RE_HOUSE)
                .leftJoin(RE_LEDGER_INFO).on(RE_LEDGER_INFO.HOUSE_ID.eq(RE_HOUSE.ID).and(RE_LEDGER_INFO.IS_HISTORY.eq(false)));
        queryWrapper.eq(ReHouse::getProjectId, reHousePageParam.getProjectId(), StringUtil.isNotBlank(reHousePageParam.getProjectId()));
        queryWrapper.eq(ReHouse::getBuildId, reHousePageParam.getBuildId(), StringUtil.isNotBlank(reHousePageParam.getBuildId()));
        queryWrapper.eq(ReHouse::getBuildCode, reHousePageParam.getBuildCode(), StringUtil.isNotBlank(reHousePageParam.getBuildCode()));
        queryWrapper.eq(ReHouse::getStatus, reHousePageParam.getHouseStatus(), StringUtil.isNotBlank(reHousePageParam.getHouseStatus()));
        queryWrapper.eq(ReHouse::getHouseNumber, reHousePageParam.getHouseCode(), StringUtil.isNotBlank(reHousePageParam.getHouseCode()));
        queryWrapper.like(ReHouse::getCustomerName, reHousePageParam.getCustomerName(), StringUtil.isNotBlank(reHousePageParam.getCustomerName()));
        queryWrapper.like(ReHouse::getCustomerPhone, reHousePageParam.getCustomerPhone(), StringUtil.isNotBlank(reHousePageParam.getCustomerPhone()));
        if (ObjectUtil.isAllNotEmpty(reHousePageParam.getSortField(), reHousePageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reHousePageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reHousePageParam.getSortField()), reHousePageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReHouse::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public void add(ReHouseAddParam reHouseAddParam) {
        this.getObjOpt(QueryWrapper.create()
                .eq(ReHouse::getFloor, reHouseAddParam.getFloor(), ObjectUtil.isNotEmpty(reHouseAddParam.getFloor()))
                .eq(ReHouse::getUnit, reHouseAddParam.getUnit())
                .eq(ReHouse::getHouseNumber, reHouseAddParam.getHouseNumber())
                .eq(ReHouse::getBuildCode, reHouseAddParam.getBuildCode())
                .eq(ReHouse::getProjectId, reHouseAddParam.getProjectId())
        ).ifPresent(s -> {
            throw new CommonException("楼层单元房号已存在");
        });
        ReHouse reHouse = BeanUtil.toBean(reHouseAddParam, ReHouse.class);
        /** 每次都进行修改 **/
        this.save(reHouse);
        reProjectDetailServiceAsync.updateOrInsertDetail(reHouse, reHouseAddParam.getProjectId());
    }

    @Tran
    @Override
    public void edit(ReHouseEditParam reHouseEditParam) {
        ReHouse reHouse = this.queryEntity(reHouseEditParam.getId());
        BeanUtil.copyProperties(reHouseEditParam, reHouse);
        this.updateById(reHouse);
        reProjectDetailServiceAsync.updateOrInsertDetail(reHouse, reHouse.getProjectId());
    }

    @Tran
    @Override
    public void delete(List<ReHouseIdParam> reHouseIdParamList) {
        Set<String> collect = reHouseIdParamList.stream().map(ReHouseIdParam::getId).collect(Collectors.toSet());
        List<ReHouse> reHouses = this.list(QueryWrapper.create().in(ReHouse::getId, collect));
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(reHouseIdParamList, ReHouseIdParam::getId));
        reHouses.forEach(s -> {
            reProjectDetailServiceAsync.updateOrInsertDetail(s, s.getProjectId());
        });
        //将台账置为删除
        reLedgerInfoService.updateChain().set(ReLedgerInfo::getIsHistory, true).set(ReLedgerInfo::getDeleteFlag, "DELETED").in(ReLedgerInfo::getHouseId, collect).update();
    }

    @Override
    public ReHouse detail(ReHouseIdParam reHouseIdParam) {
        return this.queryEntity(reHouseIdParam.getId());
    }

    @Override
    public ReHouse queryEntity(String id) {
        ReHouse reHouse = this.getById(id);
        if (ObjectUtil.isEmpty(reHouse)) {
            throw new CommonException("房屋管理不存在，id值为：{}", id);
        }
        return reHouse;
    }

    @Override
    public List<JSONObject> listByBuild(ReBuildingIdParam reBuildingIdParam) {
        Optional.ofNullable(reBuildingIdParam.getHouseType()).orElseThrow(() -> new CommonException("房屋类型不能为空"));
        Optional.ofNullable(reBuildingIdParam.getId()).orElseThrow(() -> new CommonException("请先选择楼栋"));
        /** 通过楼栋id查询当前楼栋下的所有房屋信息 **/
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ReHouse::getBuildId, reBuildingIdParam.getId());
        queryWrapper.eq(ReHouse::getHouseType, reBuildingIdParam.getHouseType());
        queryWrapper.eq(ReHouse::getBuildId, reBuildingIdParam.getBuildId(), StringUtil.isNotBlank(reBuildingIdParam.getBuildId()));
        queryWrapper.eq(ReHouse::getBuildCode, reBuildingIdParam.getBuildCode(), StringUtil.isNotBlank(reBuildingIdParam.getBuildCode()));
        queryWrapper.eq(ReHouse::getStatus, reBuildingIdParam.getHouseStatus(), StringUtil.isNotBlank(reBuildingIdParam.getHouseStatus()));
        queryWrapper.eq(ReHouse::getHouseNumber, reBuildingIdParam.getHouseCode(), StringUtil.isNotBlank(reBuildingIdParam.getHouseCode()));
        queryWrapper.like(ReHouse::getCustomerName, reBuildingIdParam.getCustomerName(), StringUtil.isNotBlank(reBuildingIdParam.getCustomerName()));
        queryWrapper.like(ReHouse::getCustomerPhone, reBuildingIdParam.getCustomerPhone(), StringUtil.isNotBlank(reBuildingIdParam.getCustomerPhone()));
        queryWrapper
                .select(
                        RE_HOUSE.ALL_COLUMNS,
                        RE_LEDGER_INFO.CONTRACT_TYPE.as("contractStatus")
                ).from(RE_HOUSE)
                .leftJoin(RE_LEDGER_INFO).on(RE_LEDGER_INFO.HOUSE_ID.eq(RE_HOUSE.ID).and(RE_LEDGER_INFO.IS_HISTORY.eq(false))).orderByUnSafely("re_house.build_code",
                        "re_house.unit",
                        "CONVERT(re_house.floor,SIGNED)",
                        "re_house.house_number");
        List<ReHouse> list = this.list(queryWrapper);
        if (reBuildingIdParam.getHouseType().equals("1")) {
            /** 拿到所有房屋信息通过楼栋和单元排序 **/
            List<String> units = list.stream()
                    .filter(s -> null != s.getUnit())
                    .map(ReHouse::getUnit)
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());

            List<String> floors = list.stream()
                    .filter(s -> null != s.getFloor())
                    .sorted(Comparator.comparing(ReHouse::getFloor))
                    .map(ReHouse::getFloor)
                    .distinct()
                    .collect(Collectors.toList());

            List<ReHouse> sortedHouses = list.stream()
                    .filter(s -> null != s.getHouseNumber())
                    .sorted(Comparator.comparing(ReHouse::getHouseNumber))
                    .collect(Collectors.toList());
            List<JSONObject> arrayList = new ArrayList<>();

            units.forEach(s -> {
                JSONObject entries = new JSONObject();
                entries.set("unit", s);
                for (String floor : floors) {
                    List<ReHouse> houseList = sortedHouses.stream()
                            .filter(r -> r.getFloor() != null && r.getUnit() != null)
                            .filter(reHouse -> reHouse.getFloor().equals(floor) && reHouse.getUnit().equals(s))
                            .collect(Collectors.toList());
                    entries.set(floor, houseList);
                }
                arrayList.add(entries);
            });
            return arrayList;
        } else {
            return list.stream().map(JSONUtil::parseObj).collect(Collectors.toList());
        }
//        JSONObject entries = new JSONObject();
//
//        for (ReHouse reHouse : sortedHouses) {
//            String key = reHouse.getFloor() + "-" + reHouse.getUnit();
//            List<ReHouse> houseList = entries.getBeanList(key, ReHouse.class);
//            if (houseList == null) {
//                houseList = new ArrayList<>();
//                entries.set(key, houseList);
//            }
//            houseList.add(reHouse);
//        }
//
//        JSONObject set = JSONUtil.createObj().set("houses", entries).set("units", units).set("floors", floors);
    }

    @Override
    public void importHouseInfo(UploadedFile file, String projectId) {
        //TODO 导入房屋管理信息 通用 通过类型区分
        File tempFile = FileUtil.writeBytes(IoUtil.readBytes(file.getContent()), FileUtil.file(FileUtil.getTmpDir() +
                FileUtil.FILE_SEPARATOR + "userImportTemplate.xlsx"));
        // 读取excel
        EasyExcel.read(tempFile).head(ReHouseImportParam.class).registerReadListener(new ImportHouseListener(projectId)).headRowNumber(1).doReadAll();
    }

    @Override
    public void downloadImportRehouseTemplate(Context response, String type) throws IOException {
        if (null == type || (!"1".equals(type) && !"2".equals(type) && !"3".equals(type))) {
            CommonResponseUtil.renderError(response, "下载模板导入模板失败");
            return;
        }
        try {
            InputStream inputStream = POICacheManager.getFile("houseImportTemplate" + type + ".xlsx");
            byte[] bytes = IoUtil.readBytes(inputStream);
            String excelName = "1".equals(type) ? "住宅房源导入模板.xlsx" : "2".equals(type) ? "商业房源导入模板.xlsx" : "3".equals(type) ? "储藏间房源导入模板.xlsx" : "";
            CommonDownloadUtil.download(excelName, bytes, response);
        } catch (Exception e) {
            CommonResponseUtil.renderError(response, "下载模板导入模板失败");
        }
    }

    @Override
    @Tran
    public void checkout(CommonValidList<ReHouseIdParam> reHouseIdParamList) {
        reHouseIdParamList.forEach(reHouseIdParam -> {
            // 同时置空一些字段
            this.updateChain().set(ReHouse::getStatus, "1").set(ReHouse::getCustomerName, "").set(ReHouse::getCustomerPhone, "").set(ReHouse::getSalesTotalPrice, BigDecimal.ZERO).set(ReHouse::getSalesUnitPrice, BigDecimal.ZERO).eq(ReHouse::getId, reHouseIdParam.getId()).update();
            // 同时 修改台账信息
            reLedgerInfoService.getOneOpt(QueryWrapper.create().eq(ReLedgerInfo::getHouseId, reHouseIdParam.getId()).eq(ReLedgerInfo::getIsHistory, false)).ifPresent(s -> {
                reLedgerInfoService.updateChain().set(ReLedgerInfo::getIsHistory, true).eq(ReLedgerInfo::getId, s.getId()).update();
            });
            ReHouse reHouse = this.queryEntity(reHouseIdParam.getId());
            reProjectDetailServiceAsync.updateOrInsertDetail(reHouse, reHouse.getProjectId());
        });

    }


    @Override
    public void exportHouseLedgerInfo(Context response, ReHousePageParam reHousePageParam) {
        Optional.ofNullable(reHousePageParam.getProjectId()).orElseThrow(() -> new CommonException("项目id不能为空"));
        // 通过参数查询房屋管理信息 判断是否有参数
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select("ROW_NUMBER() OVER (ORDER BY re_house.house_number) AS index1")
                .select("(SELECT MAX(re_payment_info.PAYMENT_TIME) FROM re_payment_info WHERE re_payment_info.ledger_id = re_ledger_info.id AND re_payment_info.delete_flag = 'NOT_DELETE') AS lastPaymentTime")
                .select(
                        RE_HOUSE.BUILD_CODE,
                        RE_LEDGER_INFO.IS_HISTORY,
                        RE_HOUSE.FLOOR,
                        RE_HOUSE.UNIT,
                        RE_HOUSE.HOUSE_NUMBER,
                        RE_HOUSE.ACTUAL_BUILD_AREA,
                        RE_HOUSE.ACTUAL_HOUSE_AREA.as("area"),
                        RE_HOUSE.ACTUAL_HOUSE_AREA.as("enableArea"),
                        RE_CUSTOMER.QUOTA_IDS,
                        RE_CUSTOMER.ADDRESS,
                        RE_CUSTOMER.SUBSCRIBE_TIME,
                        RE_CUSTOMER.VILLAGE_ID,
                        RE_CUSTOMER.CODE,
                        RE_LEDGER_INFO.NAME,
                        RE_LEDGER_INFO.PHONE,
                        RE_LEDGER_INFO.ID_CARD,
                        RE_CUSTOMER.ENABLE_AREA,
                        RE_SALES_CONTRACT.TOTAL_HOUSE_PRICE.as("totalHousePrice1"),
                        RE_SALES_CONTRACT.PAYMENT_METHOD.as("paymentMethod1"),
                        RE_SALES_CONTRACT.UNIT_PRICE,
                        RE_SALES_CONTRACT.TOTAL_PRICE,
                        RE_SALES_CONTRACT.DISCOUNT.as("contractDiscount"),
                        RE_SALES_CONTRACT.DISCOUNT_REMARK.as("contractDiscountRemark"),
                        RE_SALES_CONTRACT.CONTRACT_UNIT_PRICE,
                        RE_SALES_CONTRACT.MAINTENANCE_FUND_UNIT_PRICE,
                        RE_SALES_CONTRACT.MAINTENANCE_FUND,
                        RE_LEASE_CONTRACT.TOTAL_HOUSE_PRICE.as("totalHousePrice2"),
                        RE_LEASE_CONTRACT.PAYMENT_METHOD.as("paymentMethod2"),
                        RE_LEASE_CONTRACT.LEASE_UNIT_PRICE,
                        RE_LEASE_CONTRACT.LEASE_DEPOSIT,
                        RE_LEASE_CONTRACT.LEASE_START_TIME,
                        RE_LEASE_CONTRACT.LEASE_END_TIME,
                        RE_LEASE_CONTRACT.EXPIRE_REMIND,
                        RE_LEASE_CONTRACT.DISCOUNT.as("leaseDiscount"),
                        RE_LEASE_CONTRACT.DISCOUNT_REMARK.as("leaseDiscountRemark"),
                        RE_LEASE_CONTRACT.LEASE_TERM,
                        RE_LEASE_CONTRACT.RENT_FREE_PERIOD,
                        RE_LEASE_CONTRACT.LEASE_TOTAL_PRICE,
                        RE_PLACEMENT_CONTRACT.TOTAL_HOUSE_PRICE.as("totalHousePrice3"),
                        RE_PLACEMENT_CONTRACT.PAYMENT_METHOD.as("paymentMethod3"),
                        RE_PLACEMENT_CONTRACT.BENCHMARK_PRICE,
                        RE_PLACEMENT_CONTRACT.AREA,
                        RE_PLACEMENT_CONTRACT.SUBTOTAL,
                        RE_PLACEMENT_CONTRACT.SUBSIDY_PRICE,
                        RE_PLACEMENT_CONTRACT.SUBSIDY_AREA,
                        RE_PLACEMENT_CONTRACT.SUBSIDY_SUBTOTAL,
                        RE_PLACEMENT_CONTRACT.MARKET_PRICE,
                        RE_PLACEMENT_CONTRACT.MARKET_AREA,
                        RE_PLACEMENT_CONTRACT.MAINTENANCE_FUND.as("maintenanceFundPrice"),
                        RE_PLACEMENT_CONTRACT.MARKET_SUBTOTAL,
                        RE_PLACEMENT_CONTRACT.DISCOUNT.as("placementDiscount"),
                        RE_PLACEMENT_CONTRACT.DISCOUNT_REMARK.as("placementDiscountRemark"),
                        RE_PLACEMENT_CONTRACT.MAINTENANCE_FUND.as("placementMaintenanceFund"),
                        RE_PLACEMENT_CONTRACT.TOTAL_HOUSE_PRICE.as("placementTotal"),
                        RE_PLACEMENT_CONTRACT.CONTRACT_TIME.as("placementContractTime"),
                        RE_PLACEMENT_CONTRACT.SUBTOTAL.as("benchmarkSubtotal"),
                        RE_PLACEMENT_CONTRACT.CONTRACT_PRICE.as("placementContractPrice"),
                        RE_LEDGER_INFO.TOTAL_PAYMENT,
                        RE_LEDGER_INFO.CONTRACT_TYPE,
                        RE_LEDGER_INFO.CONTRACT_PRICE,
                        RE_LEDGER_INFO.AREA.as("maintenanceFundArea"),
                        RE_CONTRACT_INFO.PROPERTY_CONSULTANT,
                        QueryMethods.dateFormat(RE_CONTRACT_INFO.CONTRACT_RECORD_TIME, "%Y-%m-%d").as("contractRecordTime"),
                        RE_CONTRACT_INFO.CONTRACT_NUMBER,
                        RE_CONTRACT_INFO.PROMISE_SETTLEMENT_DATE,
                        RE_CONTRACT_INFO.PARKING_STATUS,
                        RE_CONTRACT_INFO.INVOICE_STATUS,
                        RE_CONTRACT_INFO.CERTIFICATE_APPLICATION
                )
                .from(RE_HOUSE)
                .leftJoin(RE_LEDGER_INFO).on(RE_LEDGER_INFO.HOUSE_ID.eq(RE_HOUSE.ID).and(RE_LEDGER_INFO.DELETE_FLAG.eq("NOT_DELETE")))
                .leftJoin(RE_CUSTOMER).on(RE_CUSTOMER.LEDGER_ID.eq(RE_LEDGER_INFO.ID).and(RE_CUSTOMER.DELETE_FLAG.eq("NOT_DELETE")))
                .leftJoin(RE_SALES_CONTRACT).on(RE_SALES_CONTRACT.LEDGER_ID.eq(RE_LEDGER_INFO.ID).and(RE_SALES_CONTRACT.DELETE_FLAG.eq("NOT_DELETE")))
                .leftJoin(RE_LEASE_CONTRACT).on(RE_LEASE_CONTRACT.LEDGER_ID.eq(RE_LEDGER_INFO.ID).and(RE_LEASE_CONTRACT.DELETE_FLAG.eq("NOT_DELETE")))
                .leftJoin(RE_PLACEMENT_CONTRACT).on(RE_PLACEMENT_CONTRACT.LEDGER_ID.eq(RE_LEDGER_INFO.ID).and(RE_PLACEMENT_CONTRACT.DELETE_FLAG.eq("NOT_DELETE")))
                .leftJoin(RE_CONTRACT_INFO).on(RE_CONTRACT_INFO.LEDGER_ID.eq(RE_LEDGER_INFO.ID).and(RE_CONTRACT_INFO.DELETE_FLAG.eq("NOT_DELETE")));
        queryWrapper.eq(ReHouse::getProjectId, reHousePageParam.getProjectId(), StringUtil.isNotBlank(reHousePageParam.getProjectId()));
        queryWrapper.eq(ReHouse::getBuildId, reHousePageParam.getBuildId(), StringUtil.isNotBlank(reHousePageParam.getBuildId()));
        queryWrapper.eq(ReHouse::getBuildCode, reHousePageParam.getBuildCode(), StringUtil.isNotBlank(reHousePageParam.getBuildCode()));
        queryWrapper.eq(ReHouse::getStatus, reHousePageParam.getHouseStatus(), StringUtil.isNotBlank(reHousePageParam.getHouseStatus()));
        queryWrapper.eq(ReHouse::getHouseNumber, reHousePageParam.getHouseCode(), StringUtil.isNotBlank(reHousePageParam.getHouseCode()));
        queryWrapper.like(ReHouse::getCustomerName, reHousePageParam.getCustomerName(), StringUtil.isNotBlank(reHousePageParam.getCustomerName()));
        queryWrapper.like(ReHouse::getCustomerPhone, reHousePageParam.getCustomerPhone(), StringUtil.isNotBlank(reHousePageParam.getCustomerPhone()));
        queryWrapper.eq(ReHouse::getHouseType, reHousePageParam.getHouseType(), StringUtil.isNotBlank(reHousePageParam.getHouseType()));
        // 改进排序逻辑：对楼层为空的情况进行特殊处理，避免聚集在一起
        queryWrapper.orderByUnSafely("re_house.build_code",
                "re_house.unit",
                "CASE WHEN re_house.floor IS NULL OR re_house.floor = '' THEN 999999 ELSE CONVERT(re_house.floor,SIGNED) END",
                "re_house.house_number");
        List<ReHouseLedgerExport> list = this.listAs(queryWrapper, ReHouseLedgerExport.class);
 run dev
        // 数据完整性检查：记录楼层信息缺失的情况
        long emptyFloorCount = list.stream().filter(s -> StrUtil.isEmpty(s.getFloor())).count();
        if (emptyFloorCount > 0) {
            log.warn("住宅台账导出发现 {} 条楼层信息缺失的记录，项目ID: {}", emptyFloorCount, reHousePageParam.getProjectId());
        }

        // 处理一下字典 和 未查出来的字段需要计算
        list.forEach(s -> {
            // 处理楼层信息缺失的情况，添加标识便于用户识别
            if (StrUtil.isEmpty(s.getFloor())) {
                s.setFloor("未设置楼层");
            }

            String quotaIds = s.getQuotaIds();
            if (null != quotaIds) {
                s.setQuotaIds(quotaIds.split(",").length + "");
            } else {
                s.setQuotaIds("0");
            }
            s.setParkingStatus(s.getParkingStatus() != null ? s.getParkingStatus().equals("1") ? "已购买" : "未购买" : "");
            s.setInvoiceStatus(s.getInvoiceStatus() != null ? s.getInvoiceStatus().equals("1") ? "已开具" : "未开具" : "");
            // 车位状态
            // 发票状态
            String contractType = s.getContractType();
            if (null != contractType) {
                //销售、租赁、安置
                if (contractType.equals("1")) {
                    // 销售合同
                    s.setContractType("销售签约");
                    s.setPaymentMethod(s.getPaymentMethod1());
                    s.setTotalHousePrice(s.getTotalHousePrice1());
                    s.setContractPrice(s.getTotalPrice());
                    s.setPlacementContractPrice(null);
                }
                if (contractType.equals("2")) {
                    // 租赁合同
                    s.setContractType("租赁签约");
                    s.setPaymentMethod(s.getPaymentMethod2());
                    s.setTotalHousePrice(s.getTotalHousePrice2());
                }
                if (contractType.equals("3")) {
                    // 安置合同
                    s.setContractType("安置签约");
                    s.setPaymentMethod(s.getPaymentMethod3());
                    s.setTotalHousePrice(s.getTotalHousePrice3());
                    s.setContractPrice(null);
                }
            }
            // 统一的空值处理策略：当客户姓名为空时，清空相关客户信息字段
            if (StrUtil.isEmpty(s.getName())) {
                s.setQuotaIds(null);
                s.setEnableArea(null);
                s.setIdCard(null);
                s.setPhone(null);
                s.setAddress(null);
                s.setSubscribeTime(null);
                // 清空合同相关信息，因为没有客户就不应该有合同
                s.setContractType(null);
                s.setPaymentMethod(null);
                s.setTotalHousePrice(null);
                s.setContractPrice(null);
                s.setTotalPayment(null);
                s.setDebtPayment(null);
                s.setDebtStatus(null);
            }

            // 一次性付款、全款分期、贷款
            s.setPaymentMethod(s.getPaymentMethod() != null ? s.getPaymentMethod().equals("1") ? "一次性付款" : s.getPaymentMethod().equals("2") ? "全款分期" : "贷款" : "");

            // 改进的欠款计算逻辑，添加空值检查和异常处理
            try {
                Double totalHousePrice = s.getTotalHousePrice();
                Double totalPayment = s.getTotalPayment();

                if (totalHousePrice != null && totalPayment != null) {
                    double debtAmount = totalHousePrice - totalPayment;
                    s.setDebtPayment(debtAmount);

                    // 改进结清状态判断逻辑
                    if (totalHousePrice == 0.0 && totalPayment == 0.0) {
                        s.setDebtStatus(""); // 无交易数据
                    } else {
                        s.setDebtStatus(Math.abs(debtAmount) < 0.01 ? "已结清" : "未结清");
                    }
                } else {
                    // 数据不完整时的处理
                    s.setDebtPayment(null);
                    s.setDebtStatus(totalHousePrice == null && totalPayment == null ? "" : "数据不完整");
                    log.warn("房屋台账导出：房屋价格或支付金额为空，房屋编号: {}", s.getHouseNumber());
                }
            } catch (Exception e) {
                log.error("计算欠款金额失败，房屋编号: {}", s.getHouseNumber(), e);
                s.setDebtPayment(null);
                s.setDebtStatus("计算异常");
            }

            // 计算维修基金单价，添加除零检查
            if (s.getMaintenanceFundPrice() != null && s.getMaintenanceFundArea() != null && s.getMaintenanceFundArea() > 0) {
                try {
                    s.setMaintenanceFundUnitPrice(s.getMaintenanceFundPrice() / s.getMaintenanceFundArea());
                } catch (Exception e) {
                    log.error("计算维修基金单价失败，房屋编号: {}, 维修基金: {}, 面积: {}",
                             s.getHouseNumber(), s.getMaintenanceFundPrice(), s.getMaintenanceFundArea(), e);
                    s.setMaintenanceFundUnitPrice(null);
                }
            } else {
                s.setMaintenanceFundUnitPrice(null);
            }
        });
        File destTemplateFile = null;
        String templateName = null;
        String sheetName = null;
        // 读取模板流
        InputStream inputStream = null;
        if (reHousePageParam.getHouseType().equals("1")) {
            templateName = "houseExportTemplate.xlsx";
            sheetName = "住宅台账信息";
        }
        if (reHousePageParam.getHouseType().equals("2")) {
            templateName = "businessLedgerExportTemplate.xlsx";
            sheetName = "商业台账信息";
        }
        inputStream = POICacheManager.getFile(templateName);
        // 创建一个临时模板
        destTemplateFile = FileUtil.writeFromStream(inputStream, FileUtil.file(FileUtil.getTmpDir() +
                File.separator + System.currentTimeMillis() + templateName));
        File file = FileUtil.file(FileUtil.getTmpDir() + FileUtil.FILE_SEPARATOR + sheetName + ".xlsx");
        // 填充数据
        ReProject byId = reProjectService.getById(reHousePageParam.getProjectId());
        if (null == byId) {
            throw new CommonException("项目不存在");
        }
        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("communityName", byId.getName());
        try (ExcelWriter excelWriter = EasyExcel.write(file).withTemplate(destTemplateFile).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(objectObjectHashMap, fillConfig, writeSheet);
            excelWriter.fill(list, writeSheet);
        }
        // 下载userExportTemplate.docx
        CommonDownloadUtil.download(sheetName + System.currentTimeMillis() + ".xlsx", FileUtil.readBytes(file), response);
        FileUtil.del(destTemplateFile);
        FileUtil.del(file);
    }

    @Override
    public void exportHouseTaxInfo(Context response, ReHousePageParam reHousePageParam) {
        // 必选项目id
        Optional.ofNullable(reHousePageParam.getProjectId()).orElseThrow(() -> new CommonException("项目id不能为空"));
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.select(
                        RE_HOUSE.BUILD_CODE, RE_HOUSE.FLOOR, RE_HOUSE.UNIT, RE_HOUSE.HOUSE_NUMBER, RE_HOUSE.ACTUAL_BUILD_AREA, RE_HOUSE.ACTUAL_HOUSE_AREA, RE_HOUSE.FORECAST_BUILD_AREA, RE_HOUSE.FORECAST_HOUSE_AREA,
                        RE_HOUSE.HOUSE_ORIENTATION, RE_HOUSE.HOUSE_LAYOUT,
                        RE_CONTRACT_INFO.CONTRACT_NUMBER, RE_CONTRACT_INFO.CONTRACT_RECORD_TIME,
                        RE_LEDGER_INFO.CONTRACT_PRICE, RE_LEDGER_INFO.NAME, RE_LEDGER_INFO.ID_CARD,
                        RE_BUILDING.EXT_JSON.as("presalePermitNumber"))
                .from(RE_HOUSE)
                .leftJoin(RE_BUILDING).on(RE_BUILDING.ID.eq(RE_HOUSE.BUILD_ID))
                .leftJoin(RE_LEDGER_INFO).on(RE_LEDGER_INFO.HOUSE_ID.eq(RE_HOUSE.ID))
                .leftJoin(RE_CONTRACT_INFO).on(RE_CONTRACT_INFO.LEDGER_ID.eq(RE_LEDGER_INFO.ID));
        queryWrapper.eq(ReHouse::getProjectId, reHousePageParam.getProjectId(), StringUtil.isNotBlank(reHousePageParam.getProjectId()));
        queryWrapper.eq(ReHouse::getBuildId, reHousePageParam.getBuildId(), StringUtil.isNotBlank(reHousePageParam.getBuildId()));
        queryWrapper.eq(ReHouse::getBuildCode, reHousePageParam.getBuildCode(), StringUtil.isNotBlank(reHousePageParam.getBuildCode()));
        queryWrapper.eq(ReHouse::getStatus, reHousePageParam.getHouseStatus(), StringUtil.isNotBlank(reHousePageParam.getHouseStatus()));
        queryWrapper.eq(ReHouse::getHouseNumber, reHousePageParam.getHouseCode(), StringUtil.isNotBlank(reHousePageParam.getHouseCode()));
        queryWrapper.like(ReHouse::getCustomerName, reHousePageParam.getCustomerName(), StringUtil.isNotBlank(reHousePageParam.getCustomerName()));
        queryWrapper.like(ReHouse::getCustomerPhone, reHousePageParam.getCustomerPhone(), StringUtil.isNotBlank(reHousePageParam.getCustomerPhone()));
        queryWrapper.eq(ReHouse::getHouseType, "1", StringUtil.isBlank(reHousePageParam.getHouseType()));
        queryWrapper.eq(ReHouse::getHouseType, reHousePageParam.getHouseType(), StringUtil.isNotBlank(reHousePageParam.getHouseType()));
        queryWrapper.orderByUnSafely("re_house.build_code",
                "re_house.unit",
                "CONVERT(re_house.floor,SIGNED)",
                "re_house.house_number");
        List<ResidenceTaxExport> list = this.listAs(queryWrapper, ResidenceTaxExport.class);
        //翻译字典
        JSONArray layoutType = dictApi.getDictListByType("house_layout");

        if (CollectionUtil.isNotEmpty(list)) {
            list.forEach(s -> {
                s.setHouseLayout(layoutType.stream().filter(r -> JSONUtil.parseObj(r).getStr("dictValue").equals(s.getHouseLayout())).findFirst().map(r -> JSONUtil.parseObj(r).getStr("dictLabel")).orElse(""));
                // 处理合同价格 - 增加空值判断
                if (StrUtil.isEmpty(s.getName())) {
                    s.setContractPrice(null);
                }
            });
        }
        // 查询项目详细信息
        ReProject byId = reProjectService.getById(reHousePageParam.getProjectId());
        String landValueAddedTaxNum = byId.getLandValueAddedTaxNum();
        Map<String, Object> objectObjectMap = new HashMap<>();
        objectObjectMap.put("landValueAddedTaxNum", landValueAddedTaxNum);
        File destTemplateFile = null;
        // 导出
        // 读取模板流
        InputStream inputStream = POICacheManager.getFile("houseTaxExportTemplate.xlsx");
        // 创建一个临时模板
        destTemplateFile = FileUtil.writeFromStream(inputStream, FileUtil.file(FileUtil.getTmpDir() +
                File.separator + System.currentTimeMillis() + "houseTaxExportTemplate.xlsx"));
        File file = FileUtil.file(FileUtil.getTmpDir() + FileUtil.FILE_SEPARATOR + System.currentTimeMillis() + "住宅税务汇报表目.xlsx");
        try (ExcelWriter excelWriter = EasyExcel.write(file).withTemplate(destTemplateFile).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(objectObjectMap, fillConfig, writeSheet);
            excelWriter.fill(list, writeSheet);
        }
        // 下载userExportTemplate.docx
        CommonDownloadUtil.download("住宅税务汇报表目" + System.currentTimeMillis() + ".xlsx", FileUtil.readBytes(file), response);
        FileUtil.del(destTemplateFile);
        FileUtil.del(file);
    }

    @Override
    public void exportStoreRoomLedgerInfo(Context response, ReHousePageParam reHousePageParam) {
        // 必选项目id
        Optional.ofNullable(reHousePageParam.getProjectId()).orElseThrow(() -> new CommonException("项目id不能为空"));

        // 构建查询条件
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ReHouse::getProjectId, reHousePageParam.getProjectId(), StringUtil.isNotBlank(reHousePageParam.getProjectId()))
                .eq(ReHouse::getBuildId, reHousePageParam.getBuildId(), StringUtil.isNotBlank(reHousePageParam.getBuildId()))
                .eq(ReHouse::getBuildCode, reHousePageParam.getBuildCode(), StringUtil.isNotBlank(reHousePageParam.getBuildCode()))
                .eq(ReHouse::getStatus, reHousePageParam.getHouseStatus(), StringUtil.isNotBlank(reHousePageParam.getHouseStatus()))
                .eq(ReHouse::getHouseNumber, reHousePageParam.getHouseCode(), StringUtil.isNotBlank(reHousePageParam.getHouseCode()))
                .like(ReHouse::getCustomerName, reHousePageParam.getCustomerName(), StringUtil.isNotBlank(reHousePageParam.getCustomerName()))
                .like(ReHouse::getCustomerPhone, reHousePageParam.getCustomerPhone(), StringUtil.isNotBlank(reHousePageParam.getCustomerPhone()))
                .eq(ReHouse::getHouseType, "3", StringUtil.isBlank(reHousePageParam.getHouseType())) // 默认查询储藏间
                .eq(ReHouse::getHouseType, reHousePageParam.getHouseType(), StringUtil.isNotBlank(reHousePageParam.getHouseType()));

        // 选择字段
        queryWrapper.select("ROW_NUMBER() OVER (ORDER BY `re_house`.`house_number`) AS index1")
                .select(
                        RE_HOUSE.ID,
                        RE_HOUSE.HOUSE_NUMBER,
                        RE_HOUSE.FLOOR,
                        RE_HOUSE.BUILD_CODE.as("buildId"),
                        RE_HOUSE.SALES_TOTAL_PRICE,
                        RE_HOUSE.SALES_UNIT_PRICE,
                        RE_HOUSE.ACTUAL_HOUSE_AREA,
                        RE_HOUSE.ACTUAL_BUILD_AREA,
                        RE_LEDGER_INFO.NAME,
                        RE_LEDGER_INFO.ID_CARD,
                        RE_LEDGER_INFO.PHONE,
                        RE_LEDGER_INFO.CONTRACT_PRICE,
                        RE_LEDGER_INFO.TOTAL_PAYMENT,
                        RE_SALES_CONTRACT.UNIT_PRICE.as("salesUnitPrice"),
                        RE_SALES_CONTRACT.TOTAL_PRICE.as("salesTotalPrice"),
                        RE_SALES_CONTRACT.TOTAL_HOUSE_PRICE,
                        RE_SALES_CONTRACT.DISCOUNT,
                        RE_SALES_CONTRACT.DISCOUNT_REMARK,
                        RE_CUSTOMER.SUBSCRIBE_TIME,
                        RE_CUSTOMER.CONTRACT_TIME
                ).from(RE_HOUSE)
                .leftJoin(RE_LEDGER_INFO).on(RE_LEDGER_INFO.HOUSE_ID.eq(RE_HOUSE.ID).and(RE_LEDGER_INFO.DELETE_FLAG.eq("NOT_DELETE")))
                .leftJoin(RE_CUSTOMER).on(RE_CUSTOMER.LEDGER_ID.eq(RE_LEDGER_INFO.ID).and(RE_CUSTOMER.DELETE_FLAG.eq("NOT_DELETE")))
                .leftJoin(RE_SALES_CONTRACT).on(RE_SALES_CONTRACT.LEDGER_ID.eq(RE_LEDGER_INFO.ID).and(RE_SALES_CONTRACT.DELETE_FLAG.eq("NOT_DELETE")));
//                .orderBy("buildId","unit","floor");
        queryWrapper.orderByUnSafely("re_house.build_code",
                "CONVERT(re_house.floor,SIGNED)",
                "re_house.house_number");
        // 查询数据
        List<ReStoreRoomkLedgerExport> list = this.listAs(queryWrapper, ReStoreRoomkLedgerExport.class);

        // 处理数据 - 改进的数据处理逻辑
        list.forEach(s -> {
            // 安全的日期格式化
            try {
                if (StrUtil.isNotEmpty(s.getContractTime())) {
                    s.setContractTime(DateUtil.format(DateUtil.parse(s.getContractTime()), "yyyy-MM-dd"));
                }
            } catch (Exception e) {
                log.warn("储藏间导出：签约时间格式化失败，房屋编号: {}, 原始时间: {}", s.getHouseNumber(), s.getContractTime());
                s.setContractTime(null);
            }

            try {
                if (StrUtil.isNotEmpty(s.getSubscribeTime())) {
                    s.setSubscribeTime(DateUtil.format(DateUtil.parse(s.getSubscribeTime()), "yyyy-MM-dd"));
                }
            } catch (Exception e) {
                log.warn("储藏间导出：认购时间格式化失败，房屋编号: {}, 原始时间: {}", s.getHouseNumber(), s.getSubscribeTime());
                s.setSubscribeTime(null);
            }

            // 改进的欠款计算逻辑
            if (s.getContractPrice() != null && s.getTotalPayment() != null) {
                double debtAmount = s.getContractPrice() - s.getTotalPayment();
                s.setDebtPriec(debtAmount);
                s.setDebtStatus(Math.abs(debtAmount) < 0.01 ? "已结清" : "未结清");
            } else {
                s.setDebtPriec(null);
                s.setDebtStatus(s.getContractPrice() == null && s.getTotalPayment() == null ? null : "数据不完整");
            }
        });

        // 获取项目名称
        ReProject byId = reProjectService.getById(reHousePageParam.getProjectId());
        HashMap<String, Object> communityName = new HashMap<>() {{
            put("communityName", byId.getName());
        }};

        // 导出Excel
        File destTemplateFile = null;
        InputStream inputStream = POICacheManager.getFile("storeRoomLedgerExportTemplate.xlsx");
        destTemplateFile = FileUtil.writeFromStream(inputStream,
                FileUtil.file(FileUtil.getTmpDir() + FileUtil.FILE_SEPARATOR + System.currentTimeMillis() + "storeRoomLedgerExportTemplate.xlsx"));

        File file = FileUtil.file(FileUtil.getTmpDir() + FileUtil.FILE_SEPARATOR + System.currentTimeMillis() + "储藏间台账信息.xlsx");
        try (ExcelWriter excelWriter = EasyExcel.write(file).withTemplate(destTemplateFile).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(communityName, fillConfig, writeSheet);
            excelWriter.fill(list, writeSheet);
        }

        // 下载文件
        CommonDownloadUtil.download("储藏间台账信息" + System.currentTimeMillis() + ".xlsx", FileUtil.readBytes(file), response);

        // 删除临时文件
        FileUtil.del(destTemplateFile);
        FileUtil.del(file);
    }


    @Override
    public void importHouseLedgerInfo(UploadedFile file, String projectId) {
        File file1 = FileUtil.file(FileUtil.getTmpDir() +
                FileUtil.FILE_SEPARATOR + System.currentTimeMillis() + "importLedgerTemplate.xlsx");

        File tempFile = FileUtil.writeBytes(IoUtil.readBytes(file.getContent()), file1);
        try {
            // 读取excel
            EasyExcel.read(tempFile).head(ReHouseLedgerPlaceImport.class).registerReadListener(new ImportHouseLedgerListener(projectId, "1")).headRowNumber(3).doReadAll();

        } catch (Exception e) {
            log.error("导入失败", e);
        } finally {
            file1.delete();
            tempFile.delete();
        }


    }

    @Override
    public void resetHouse(String houseId) {
        // 查询房屋信息
        ReHouse byId = this.getById(houseId);
        if (null != byId) {
            byId.setStatus("1");
            byId.setCustomerName("");
            byId.setCustomerPhone("");
            this.updateById(byId);
        }
    }

}
