/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.async.ReProjectDetailServiceAsync;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.mapper.RePaymentInfoMapper;
import vip.xiaonuo.biz.modular.realty.param.RePaymentInfoAddParam;
import vip.xiaonuo.biz.modular.realty.param.RePaymentInfoEditParam;
import vip.xiaonuo.biz.modular.realty.param.RePaymentInfoIdParam;
import vip.xiaonuo.biz.modular.realty.param.RePaymentInfoPageParam;
import vip.xiaonuo.biz.modular.realty.service.*;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 交款信息Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/08/17 14:35
 **/
@Component
public class RePaymentInfoServiceImpl extends ServiceImpl<RePaymentInfoMapper, RePaymentInfo> implements RePaymentInfoService {

    @Inject
    private ReLedgerInfoService reLedgerInfoService;

    @Inject
    private ReLeaseContractService reLeaseContractService;

    @Inject
    private RePlacementContractService rePlacementContractService;

    @Inject
    private ReSalesContractService reSalesContractService;

    @Inject
    private ReParkLedgerInfoService reParkLedgerInfoService;

    @Inject
    private ReParkService reParkService;

    @Inject
    private ReHouseService reHouseService;

    @Override
    public Page<RePaymentInfo> page(RePaymentInfoPageParam rePaymentInfoPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if (ObjectUtil.isAllNotEmpty(rePaymentInfoPageParam.getSortField(), rePaymentInfoPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(rePaymentInfoPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(rePaymentInfoPageParam.getSortField()), rePaymentInfoPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(RePaymentInfo::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public String add(RePaymentInfoAddParam rePaymentInfoAddParam) {
        RePaymentInfo rePaymentInfo1 = BeanUtil.toBean(rePaymentInfoAddParam, RePaymentInfo.class);
        if (rePaymentInfoAddParam.getType().equals(2) && rePaymentInfoAddParam.getExtJson().equals("house")) {
            String ledgerId = rePaymentInfoAddParam.getLedgerId();
            if (StrUtil.isNotEmpty(ledgerId)) {
                /** 添加交款信息 查询台账查询是什么签约  通过类型和签约id查询付款信息  **/
                ReLedgerInfo byId = reLedgerInfoService.getById(ledgerId);
                if (byId == null) {
                    throw new CommonException("台账信息不存在，id值为：{}", ledgerId);
                }
                String contractType = byId.getContractType();
                if (!contractType.equals("2")) {
                    /** 不管什么类型拿到总价 **/
                    BigDecimal contractPrice = BigDecimal.ZERO;
                    // 租赁
                    if (contractType.equals("1")) {
                        ReSalesContract byId1 = reSalesContractService.getById(rePaymentInfoAddParam.getContractId());
                        if (byId1 == null) {
                            throw new CommonException("销售合同信息不存在，id值为：{}", rePaymentInfoAddParam.getContractId());
                        }
                        contractPrice = byId1.getTotalHousePrice();
                    } else if (contractType.equals("3")) {
                        RePlacementContract byId1 = rePlacementContractService.getById(rePaymentInfoAddParam.getContractId());
                        contractPrice = byId1.getTotalHousePrice();
                    }
                    /** 拿到此签约id的所有交款信息 **/
                    QueryWrapper contractId = new QueryWrapper().eq("contract_id", rePaymentInfoAddParam.getContractId()).eq(RePaymentInfo::getType, "2");
                    List<RePaymentInfo> rePaymentInfos = this.list(contractId);
                    // 计算已经交款的总额
                    BigDecimal total = rePaymentInfos.stream().map(RePaymentInfo::getPaymentAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 修改台账状态
                    if (null == total) total = BigDecimal.ZERO;
                    if (null == contractPrice){
                        throw new CommonException("房款合计总价为空");
                    }
                    if (total.compareTo(contractPrice) >= 0) {
                        throw new CommonException("此签约已经交款完成，无需再交款");
                    } else {
                        int i = total.add(rePaymentInfo1.getPaymentAmount()).compareTo(contractPrice);
                        if (i > 0) {
                            throw new CommonException("交款金额超过签约总价");
                        }
                        if (i == 0) {
                            String status = byId.getStatus();
                            if (status.equals("2")) {
                                byId.setStatus("3");
                            }
                        } else {
                            byId.setStatus("2");
                        }
                        byId.setTotalPayment(total.add(rePaymentInfo1.getPaymentAmount()));
                        reLedgerInfoService.updateById(byId);
                        reHouseService.updateChain().set(ReHouse::getStatus, byId.getStatus()).set(ReHouse::getTotalPrice, byId.getTotalPayment()).eq(ReHouse::getId, byId.getHouseId()).update();
                    }
                } else {
                    // 租赁
                    ReLeaseContract byId1 = reLeaseContractService.getById(rePaymentInfoAddParam.getContractId());
                    if (byId1 == null) {
                        throw new CommonException("租赁合同信息不存在，id值为：{}", rePaymentInfoAddParam.getContractId());
                    }
                    BigDecimal totalHousePrice = byId1.getTotalHousePrice();
                    /** 拿到此签约id的所有交款信息 **/
                    QueryWrapper contractId = new QueryWrapper().eq("contract_id", rePaymentInfoAddParam.getContractId()).eq(RePaymentInfo::getType, "2");
                    List<RePaymentInfo> rePaymentInfos = this.list(contractId);
                    // 计算已经交款的总额
                    BigDecimal total = rePaymentInfos.stream().map(RePaymentInfo::getPaymentAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 修改台账状态
                    if (total.compareTo(totalHousePrice) >= 0) {
                        throw new CommonException("此签约已经交款完成，无需再交款");
                    } else {
                        int i = total.add(rePaymentInfo1.getPaymentAmount()).compareTo(totalHousePrice);
                        if (i > 0) {
                            throw new CommonException("交款金额超过签约总价");
                        }
                        // 根据支付情况设置正确的状态
                        if (i == 0) {
                            // 支付完成，设置为已结清状态
                            byId.setStatus("3");
                        } else {
                            // 未支付完成，设置为未结清状态
                            byId.setStatus("2");
                        }
                        byId.setTotalPayment(total.add(rePaymentInfo1.getPaymentAmount()));
                        reLedgerInfoService.updateById(byId);
                        reHouseService.updateChain().set(ReHouse::getStatus, byId.getStatus()).set(ReHouse::getTotalPrice, byId.getTotalPayment()).eq(ReHouse::getId, byId.getHouseId()).update();
                    }
                }
                ReHouse reHouse = new ReHouse();
                reHouse.setHouseType(byId.getHouseType());
                reProjectDetailServiceAsync.updateOrInsertDetail(reHouse, byId.getProjectId());
            }
        }
        if (rePaymentInfoAddParam.getType().equals(2) && rePaymentInfoAddParam.getExtJson().equals("park")) {
            if (StrUtil.isNotEmpty(rePaymentInfoAddParam.getLedgerId())) {
                ReParkLedgerInfo byId = reParkLedgerInfoService.getById(rePaymentInfoAddParam.getLedgerId());
                if (byId == null) {
                    throw new CommonException("台账信息不存在，id值为：{}", rePaymentInfoAddParam.getLedgerId());
                }
                /** 查询已经交的钱 **/
                List<RePaymentInfo> rePaymentInfos = this.list(QueryWrapper.create().eq("ledger_id", rePaymentInfoAddParam.getLedgerId()).eq("contract_id", rePaymentInfoAddParam.getContractId()));
                BigDecimal total = rePaymentInfos.stream().map(RePaymentInfo::getPaymentAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                if (total.compareTo(byId.getContractPrice()) > 0) {
                    throw new CommonException("交款金额超过签约总价，无需再交款");
                } else {
                    if (total.add(rePaymentInfo1.getPaymentAmount()).compareTo(byId.getContractPrice()) >= 0) {
                        byId.setStatus("3");
                    } else {
                        byId.setStatus("2");
                    }
                    byId.setTotalPayment(total.add(rePaymentInfo1.getPaymentAmount()));
                    reParkLedgerInfoService.updateById(byId);
                    reParkService.updateChain().set(RePark::getStatus, byId.getStatus()).set(RePark::getTotalPrice, byId.getTotalPayment()).eq(RePark::getId, byId.getParkId()).update();
                }
                reProjectDetailServiceAsync.updateOrInsertDetail(byId.getProjectId());
            }
        }
        this.save(rePaymentInfo1);
        return rePaymentInfo1.getId();
    }

    @Inject
    private ReProjectDetailServiceAsync reProjectDetailServiceAsync;

    @Tran
    @Override
    public void edit(RePaymentInfoEditParam rePaymentInfoEditParam) {
        RePaymentInfo rePaymentInfo = this.queryEntity(rePaymentInfoEditParam.getId());
        String extJson = Optional.ofNullable(rePaymentInfoEditParam.getExtJson()).orElse("");
        if (extJson.equals("house")) {
            if (null == rePaymentInfo.getLedgerId()) {
                return;
            }
            ReLedgerInfo byId = reLedgerInfoService.getById(rePaymentInfo.getLedgerId());
            if (byId == null) {
                throw new CommonException("台账信息不存在，id值为：{}", rePaymentInfo.getLedgerId());
            }
            BigDecimal paymentAmount = rePaymentInfo.getPaymentAmount();
            BigDecimal totalPayment = byId.getTotalPayment();
            byId.setTotalPayment(totalPayment.subtract(paymentAmount).add(rePaymentInfoEditParam.getPaymentAmount()));
            if (null != byId.getTotalPayment() && byId.getTotalPayment().compareTo(BigDecimal.ZERO) == 0) {
                byId.setStatus("2");
            }
            if (null != byId.getContractPrice() && byId.getContractPrice().compareTo(totalPayment.subtract(paymentAmount)) < 0) {
                byId.setStatus("2");
            }
            reLedgerInfoService.updateById(byId);
            reHouseService.updateChain().set(ReHouse::getStatus, byId.getStatus()).set(ReHouse::getTotalPrice, byId.getTotalPayment()).eq(ReHouse::getId, byId.getHouseId()).update();
            ReHouse reHouse = new ReHouse();
            reHouse.setHouseType(byId.getHouseType());
            reProjectDetailServiceAsync.updateOrInsertDetail(reHouse, byId.getProjectId());
        }
        if (extJson.equals("park")) {
            if (null == rePaymentInfo.getLedgerId()) {
                return;
            }
            ReParkLedgerInfo byId = reParkLedgerInfoService.getById(rePaymentInfo.getLedgerId());
            if (byId == null) {
                throw new CommonException("台账信息不存在，id值为：{}", rePaymentInfo.getLedgerId());
            }
            BigDecimal paymentAmount = rePaymentInfo.getPaymentAmount();
            BigDecimal totalPayment = byId.getTotalPayment();
            byId.setTotalPayment(totalPayment.subtract(paymentAmount).add(rePaymentInfoEditParam.getPaymentAmount()));
            if (byId.getTotalPayment().compareTo(BigDecimal.ZERO) == 0) {
                byId.setStatus("2");
            }
            if (byId.getContractPrice().compareTo(totalPayment.subtract(paymentAmount)) <= 0) {
                byId.setStatus("2");
            }
            reParkLedgerInfoService.updateById(byId);
            reParkService.updateChain().set(RePark::getStatus, byId.getStatus()).set(RePark::getTotalPrice, byId.getTotalPayment()).eq(RePark::getId, byId.getParkId()).update();
            reProjectDetailServiceAsync.updateOrInsertDetail(byId.getProjectId());
        }
        BeanUtil.copyProperties(rePaymentInfoEditParam, rePaymentInfo);
        this.updateById(rePaymentInfo);
    }

    @Tran
    @Override
    public void delete(List<RePaymentInfoIdParam> rePaymentInfoIdParamList) {
        // 删除之前对台账的合计交款进行修改
        rePaymentInfoIdParamList.forEach(s -> {
            String extJson = s.getExtJson();
            if (extJson.equals("house")) {
                RePaymentInfo rePaymentInfo = this.queryEntity(s.getId());
                if (null == rePaymentInfo.getLedgerId()) {
                    return;
                }
                ReLedgerInfo byId = reLedgerInfoService.getById(rePaymentInfo.getLedgerId());
                if (byId == null) {
                    throw new CommonException("台账信息不存在，id值为：{}", rePaymentInfo.getLedgerId());
                }
                BigDecimal paymentAmount = rePaymentInfo.getPaymentAmount();
                BigDecimal totalPayment = byId.getTotalPayment();
                byId.setTotalPayment(totalPayment.subtract(paymentAmount));
                if (null != byId.getTotalPayment() && byId.getTotalPayment().compareTo(BigDecimal.ZERO) == 0) {
                    byId.setStatus("2");
                }
                if (null != byId.getContractPrice() && byId.getContractPrice().compareTo(totalPayment.subtract(paymentAmount)) <= 0) {
                    byId.setStatus("2");
                }
                reLedgerInfoService.updateById(byId);
                reHouseService.updateChain().set(ReHouse::getStatus, byId.getStatus()).set(ReHouse::getTotalPrice, byId.getTotalPayment()).eq(ReHouse::getId, byId.getHouseId()).update();
                ReHouse reHouse = new ReHouse();
                reHouse.setHouseType(byId.getHouseType());
                reProjectDetailServiceAsync.updateOrInsertDetail(reHouse, byId.getProjectId());
            }
            if (extJson.equals("park")) {
                RePaymentInfo rePaymentInfo = this.queryEntity(s.getId());
                if (null == rePaymentInfo.getLedgerId()) {
                    return;
                }
                ReParkLedgerInfo byId = reParkLedgerInfoService.getById(rePaymentInfo.getLedgerId());
                if (byId == null) {
                    throw new CommonException("台账信息不存在，id值为：{}", rePaymentInfo.getLedgerId());
                }
                BigDecimal paymentAmount = rePaymentInfo.getPaymentAmount();
                BigDecimal totalPayment = byId.getTotalPayment();
                byId.setTotalPayment(totalPayment.subtract(paymentAmount));
                if (byId.getTotalPayment().compareTo(BigDecimal.ZERO) == 0) {
                    byId.setStatus("2");
                }
                if (byId.getContractPrice().compareTo(totalPayment.subtract(paymentAmount)) <= 0) {
                    byId.setStatus("2");
                }
                reParkLedgerInfoService.updateById(byId);
                reParkService.updateChain().set(RePark::getStatus, byId.getStatus()).set(RePark::getTotalPrice, byId.getTotalPayment()).eq(RePark::getId, byId.getParkId()).update();
                reProjectDetailServiceAsync.updateOrInsertDetail(byId.getProjectId());
            }
        });
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(rePaymentInfoIdParamList, RePaymentInfoIdParam::getId));
    }

    @Override
    public RePaymentInfo detail(RePaymentInfoIdParam rePaymentInfoIdParam) {
        return this.queryEntity(rePaymentInfoIdParam.getId());
    }

    @Override
    public RePaymentInfo queryEntity(String id) {
        RePaymentInfo rePaymentInfo = this.getById(id);
        if (ObjectUtil.isEmpty(rePaymentInfo)) {
            throw new CommonException("交款信息不存在，id值为：{}", id);
        }
        return rePaymentInfo;
    }
}
