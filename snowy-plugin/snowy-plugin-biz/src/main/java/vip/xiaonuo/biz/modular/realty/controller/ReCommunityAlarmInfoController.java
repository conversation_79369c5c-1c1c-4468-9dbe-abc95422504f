/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReCommunityAlarmInfo;
import vip.xiaonuo.biz.modular.realty.param.ReCommunityAlarmInfoAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReCommunityAlarmInfoEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReCommunityAlarmInfoIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReCommunityAlarmInfoPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReCommunityAlarmInfoService;

/**
 * 社区信息预警控制器
 *
 * <AUTHOR>
 * @date  2024/08/30 10:05
 */
@Api(tags = "社区信息预警控制器")
@Controller
@Valid
public class ReCommunityAlarmInfoController {

    @Inject
    private ReCommunityAlarmInfoService reCommunityAlarmInfoService;

    /**
     * 获取社区信息预警分页
     *
     * <AUTHOR>
     * @date  2024/08/30 10:05
     */
    @ApiOperation("获取社区信息预警分页")
    @SaCheckPermission("/biz/recommunityalarminfo/page")
    @Get
    @Mapping("/biz/recommunityalarminfo/page")
    public CommonResult<Page<ReCommunityAlarmInfo>> page(ReCommunityAlarmInfoPageParam reCommunityAlarmInfoPageParam) {
        return CommonResult.data(reCommunityAlarmInfoService.page(reCommunityAlarmInfoPageParam));
    }

    /**
     * 添加社区信息预警
     *
     * <AUTHOR>
     * @date  2024/08/30 10:05
     */
    @ApiOperation("添加社区信息预警")
    @CommonLog("添加社区信息预警")
    @SaCheckPermission("/biz/recommunityalarminfo/add")
    @Post
    @Mapping("/biz/recommunityalarminfo/add")
    public CommonResult<String> add(ReCommunityAlarmInfoAddParam reCommunityAlarmInfoAddParam) {
        reCommunityAlarmInfoService.add(reCommunityAlarmInfoAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑社区信息预警
     *
     * <AUTHOR>
     * @date  2024/08/30 10:05
     */
    @ApiOperation("编辑社区信息预警")
    @CommonLog("编辑社区信息预警")
    @SaCheckPermission("/biz/recommunityalarminfo/edit")
    @Post
    @Mapping("/biz/recommunityalarminfo/edit")
    public CommonResult<String> edit(ReCommunityAlarmInfoEditParam reCommunityAlarmInfoEditParam) {
        reCommunityAlarmInfoService.edit(reCommunityAlarmInfoEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除社区信息预警
     *
     * <AUTHOR>
     * @date  2024/08/30 10:05
     */
    @ApiOperation("删除社区信息预警")
    @CommonLog("删除社区信息预警")
    @SaCheckPermission("/biz/recommunityalarminfo/delete")
    @Post
    @Mapping("/biz/recommunityalarminfo/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReCommunityAlarmInfoIdParam> reCommunityAlarmInfoIdParamList) {
        reCommunityAlarmInfoService.delete(reCommunityAlarmInfoIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取社区信息预警详情
     *
     * <AUTHOR>
     * @date  2024/08/30 10:05
     */
    @ApiOperation("获取社区信息预警详情")
    @SaCheckPermission("/biz/recommunityalarminfo/detail")
    @Get
    @Mapping("/biz/recommunityalarminfo/detail")
    public CommonResult<ReCommunityAlarmInfo> detail(ReCommunityAlarmInfoIdParam reCommunityAlarmInfoIdParam) {
        return CommonResult.data(reCommunityAlarmInfoService.detail(reCommunityAlarmInfoIdParam));
    }
}
