/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.noear.solon.annotation.*;
import org.noear.solon.core.handle.Context;
import org.noear.solon.core.handle.UploadedFile;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.biz.modular.realty.entity.ReHouse;
import vip.xiaonuo.biz.modular.realty.param.*;
import vip.xiaonuo.biz.modular.realty.service.ReHouseService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;

import java.io.IOException;
import java.util.List;

/**
 * 房屋管理控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 */
@Api(tags = "房屋管理控制器")
@Controller
@Valid
public class ReHouseController {

    @Inject
    private ReHouseService reHouseService;


    /**
     * 导入房屋管理信息
     *
     * <AUTHOR>
     * @date 2024/8/21 17:06
     */
    @ApiOperation("导入房屋管理信息")
    @CommonLog("导入房屋管理信息")
    @Post
    @Mapping("/biz/rehouse/import")
    public CommonResult<String> importHouseInfo(@ApiParam(value="文件", required = true) UploadedFile file,@ApiParam(value="项目id", required = true) String projectId) {
        reHouseService.importHouseInfo(file,projectId);
        return CommonResult.ok();
    }


    /**
     * 导出房屋管理导入模板
     *
     * <AUTHOR>
     * @date 2024/8/22 18:40
     */
    @ApiOperation("导出房屋管理导入模板")
    @CommonLog("导出房屋管理导入模板")
    @Get
    @Mapping("/biz/rehouse/exportTemplate/{type}")
    public void downloadImportRehouseTemplate(Context response,@Param String type) throws IOException {
        reHouseService.downloadImportRehouseTemplate(response,type);
    }


    /**
     * 获取房屋管理分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取房屋管理分页")
    @SaCheckPermission("/biz/rehouse/page")
    @Get
    @Mapping("/biz/rehouse/page")
    public CommonResult<Page<ReHouse>> page(ReHousePageParam reHousePageParam) {
        return CommonResult.data(reHouseService.page(reHousePageParam));
    }

    /**
     * 添加房屋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("添加房屋管理")
    @CommonLog("添加房屋管理")
    @SaCheckPermission("/biz/rehouse/add")
    @Post
    @Mapping("/biz/rehouse/add")
    public CommonResult<String> add(ReHouseAddParam reHouseAddParam) {
        reHouseService.add(reHouseAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑房屋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("编辑房屋管理")
    @CommonLog("编辑房屋管理")
    @SaCheckPermission("/biz/rehouse/edit")
    @Post
    @Mapping("/biz/rehouse/edit")
    public CommonResult<String> edit(ReHouseEditParam reHouseEditParam) {
        reHouseService.edit(reHouseEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除房屋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("删除房屋管理")
    @CommonLog("删除房屋管理")
    @SaCheckPermission("/biz/rehouse/delete")
    @Post
    @Mapping("/biz/rehouse/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReHouseIdParam> reHouseIdParamList) {
        reHouseService.delete(reHouseIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取房屋管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取房屋管理详情")
    @SaCheckPermission("/biz/rehouse/detail")
    @Get
    @Mapping("/biz/rehouse/detail")
    public CommonResult<ReHouse> detail(ReHouseIdParam reHouseIdParam) {
        return CommonResult.data(reHouseService.detail(reHouseIdParam));
    }

    /**
     * 通过楼栋获取房屋管理列表
     *
     * <AUTHOR>
     * @date 2024/8/20 9:17
     */
    @ApiOperation("通过楼栋获取房屋管理列表")
    @Get
    @Mapping("/biz/rehouse/listByBuild")
    public CommonResult<List<JSONObject>> listByBuild(ReBuildingIdParam reBuildingIdParam) {
        return CommonResult.data(reHouseService.listByBuild(reBuildingIdParam));
    }

    /**
     * 退房操作
     *
     * <AUTHOR>
     * @date 2024/8/31 17:07
     */
    @ApiOperation("退房操作")
    @Post
    @CommonLog("退房操作")
    @Mapping("/biz/rehouse/checkout")
    public CommonResult<String> checkout(@NotEmpty(message = "集合不能为空")
                                         CommonValidList<ReHouseIdParam> reHouseIdParamList) {
        reHouseService.checkout(reHouseIdParamList);
        return CommonResult.ok();
    }

    /**
     * 导出住宅台账信息
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2024/10/11 15:45
     */
    @ApiOperation("导出住宅台账信息")
    @CommonLog("导出住宅台账信息")
    @Get
    @Mapping("/biz/rehouse/export")
    public void exportHouseLedgerInfo(Context response, ReHousePageParam reHousePageParam) throws IOException {
        reHouseService.exportHouseLedgerInfo(response,reHousePageParam);
    }


    /**
     * 导出住宅税务汇总信息
     *
     * <AUTHOR>
     * @date 2024/10/11 17:39
     */
    @ApiOperation("导出住宅税务汇总信息")
    @CommonLog("导出住宅税务汇总信息")
    @Get
    @Mapping("/biz/rehouse/exportTax")
    public void exportHouseTaxInfo(Context response, ReHousePageParam reHousePageParam) throws IOException {
        reHouseService.exportHouseTaxInfo(response,reHousePageParam);
    }

    /**
     * 导出储藏间台账信息
     *
     * <AUTHOR>
     * @date 2024/10/12 15:08
     */
    @ApiOperation("导出储藏间台账信息")
    @CommonLog("导出储藏间台账信息")
    @Get
    @Mapping("/biz/rehouse/exportStoreRoom")
    public void exportStoreRoomLedgerInfo(Context response, ReHousePageParam reHousePageParam) throws IOException {
        reHouseService.exportStoreRoomLedgerInfo(response,reHousePageParam);
    }


    /**
     * 导入房屋台账信息
     *
     * <AUTHOR>
     * @date 2024/10/29 11:04
     */
    @ApiOperation("导入房屋台账信息")
    @CommonLog("导入房屋台账信息")
    @Post
    @Mapping("/biz/rehouse/importLedger")
    public CommonResult<String> importHouseLedgerInfo(@ApiParam(value="文件", required = true) UploadedFile file,@ApiParam(value="项目id", required = true) String projectId) {
        reHouseService.importHouseLedgerInfo(file,projectId);
        return CommonResult.ok();
    }

}
