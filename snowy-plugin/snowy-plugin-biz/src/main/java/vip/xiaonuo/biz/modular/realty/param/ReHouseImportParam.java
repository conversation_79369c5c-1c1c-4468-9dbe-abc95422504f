/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.param;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 房屋管理添加参数
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
@Getter
@Setter
@ToString
public class ReHouseImportParam {

    /** 楼栋编号 */
    @ExcelProperty(value = "楼栋编号")
    private String buildCode;

    /** 楼层 */
    @ExcelProperty(value = "楼层")
    private String floor;

    /** 单元 */
    @ExcelProperty(value = "单元")
    private String unit;

    /** 房屋编号 */
    @ExcelProperty(value = "房屋编号")
    private String houseNumber;

    /** 房屋类型--字典（住宅、商业、储藏间） */
    @ExcelProperty(value = "房屋类型")
    private String houseType;

    /** 预测建筑面积 */
    @ExcelProperty(value = "预测建筑面积")
    private BigDecimal forecastBuildArea;

    /** 预测套内面积 */
    @ExcelProperty(value = "预测套内面积")
    private BigDecimal forecastHouseArea;

    /** 实测建筑面积 */
    @ExcelProperty(value = {"实测建筑面积"})
    private BigDecimal actualBuildArea;

    @ExcelProperty(value = {"建筑面积"})
    private BigDecimal area;

    /** 实测套内面积 */
    @ExcelProperty(value = "实测套内面积")
    private BigDecimal actualHouseArea;

    /** 房屋朝向--字典 */
    @ExcelProperty(value = "房屋朝向")
    private String houseOrientation;

    /** 房屋户型--字典 */
    @ExcelProperty(value = "房屋户型")
    private String houseLayout;

    /** 安置价 */
    @ExcelProperty(value = "安置单价")
    private BigDecimal placementPrice;

    /** 市场价 */
    @ExcelProperty(value = "市场单价")
    private BigDecimal marketPrice;

    /** 房源位置 */
    @ExcelProperty(value = "房源位置")
    private String houseLocation;

    /** 总价--储藏室使用 */
    @ExcelProperty(value = "储藏室总价")
    private BigDecimal totalPrice;

    /** 单价--储藏室使用 */
    @ExcelProperty(value = "储藏室单价")
    private BigDecimal unitPrice;

    /** 销售单价 */
    @ExcelProperty(value = "销售单价")
    private BigDecimal salesUnitPrice;

    /** 销售总价 */
    @ExcelProperty(value = "销售总价")
    private BigDecimal salesTotalPrice;

    /** 租赁总价 **/
    @ExcelProperty(value = "租赁总价")
    private BigDecimal leaseTotalPrice;

    /** 租赁单价 **/
    @ExcelProperty(value = "租赁单价")
    private BigDecimal leaseUnitPrice;

}
