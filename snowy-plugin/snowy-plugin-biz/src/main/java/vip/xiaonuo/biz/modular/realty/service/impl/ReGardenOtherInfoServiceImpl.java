/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.ReGardenOtherInfo;
import vip.xiaonuo.biz.modular.realty.mapper.ReGardenOtherInfoMapper;
import vip.xiaonuo.biz.modular.realty.param.ReGardenOtherInfoAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenOtherInfoEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenOtherInfoIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenOtherInfoPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReGardenOtherInfoService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;

import java.util.List;

/**
 * 园区其他信息Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
@Component
public class ReGardenOtherInfoServiceImpl extends ServiceImpl<ReGardenOtherInfoMapper, ReGardenOtherInfo> implements ReGardenOtherInfoService {

    @Override
    public Page<ReGardenOtherInfo> page(ReGardenOtherInfoPageParam reGardenOtherInfoPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if(ObjectUtil.isAllNotEmpty(reGardenOtherInfoPageParam.getSortField(), reGardenOtherInfoPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reGardenOtherInfoPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reGardenOtherInfoPageParam.getSortField()),reGardenOtherInfoPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReGardenOtherInfo::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public void add(ReGardenOtherInfoAddParam reGardenOtherInfoAddParam) {
        ReGardenOtherInfo reGardenOtherInfo = BeanUtil.toBean(reGardenOtherInfoAddParam, ReGardenOtherInfo.class);
        this.save(reGardenOtherInfo);
    }

    @Tran
    @Override
    public void edit(ReGardenOtherInfoEditParam reGardenOtherInfoEditParam) {
        ReGardenOtherInfo reGardenOtherInfo = this.queryEntity(reGardenOtherInfoEditParam.getId());
        BeanUtil.copyProperties(reGardenOtherInfoEditParam, reGardenOtherInfo);
        this.updateById(reGardenOtherInfo);
    }

    @Tran
    @Override
    public void delete(List<ReGardenOtherInfoIdParam> reGardenOtherInfoIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(reGardenOtherInfoIdParamList, ReGardenOtherInfoIdParam::getId));
    }

    @Override
    public ReGardenOtherInfo detail(ReGardenOtherInfoIdParam reGardenOtherInfoIdParam) {
        return this.queryEntity(reGardenOtherInfoIdParam.getId());
    }

    @Override
    public ReGardenOtherInfo queryEntity(String id) {
        ReGardenOtherInfo reGardenOtherInfo = this.getById(id);
        if(ObjectUtil.isEmpty(reGardenOtherInfo)) {
            throw new CommonException("园区其他信息不存在，id值为：{}", id);
        }
        return reGardenOtherInfo;
    }
}
