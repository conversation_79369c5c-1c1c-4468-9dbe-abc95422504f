package vip.xiaonuo.biz.modular.realty.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.Solon;
import vip.xiaonuo.biz.modular.realty.async.ReProjectDetailServiceAsync;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.mapper.*;
import vip.xiaonuo.biz.modular.realty.param.ReBussnessLeaseLedgerInfoImportParam;

import java.math.BigDecimal;
import java.util.*;

/**
 * Excel 导入监听器，用于处理商业租赁台账数据导入
 * 已集成重复性检查功能
 *
 * <AUTHOR>
 * @date 2024/8/22 10:35
 * @updated Claude 4.0 sonnet 2024/12/19
 */
@Slf4j
public class ImportBussnessLeaseLedgerInfoListener extends AbstractImportListener<ReBussnessLeaseLedgerInfoImportParam> {

    private static final String HOUSE_TYPE = "2"; // 房屋类型：商业
    private static final String STATUS_PAID = "3"; // 状态：已结清
    private static final String STATUS_UNPAID = "2"; // 状态：未结清
    private static final String INVOICE_ISSUED = "1"; // 发票状态：已开发票
    private static final String INVOICE_NOT_ISSUED = "2"; // 发票状态：未开发票

    private final JSONObject res;

    private final ReHouseMapper reHouseMapper = Solon.context().getBean(ReHouseMapper.class);
    private final ReCustomerInfoMapper reCustomerInfoMapper = Solon.context().getBean(ReCustomerInfoMapper.class);
    private final ReLedgerInfoMapper reLedgerInfoMapper = Solon.context().getBean(ReLedgerInfoMapper.class);
    private final ReCustomerMapper reCustomerMapper = Solon.context().getBean(ReCustomerMapper.class);
    private final ReLeaseContractMapper reLeaseContractMapper = Solon.context().getBean(ReLeaseContractMapper.class);
    private final RePaymentInfoMapper rePaymentInfoMapper = Solon.context().getBean(RePaymentInfoMapper.class);
    private final ReContractInfoMapper reContractInfoMapper = Solon.context().getBean(ReContractInfoMapper.class);
    private static ReProjectDetailServiceAsync reProjectDetailServiceAsync = Solon.context().getBean(ReProjectDetailServiceAsync.class);

    public ImportBussnessLeaseLedgerInfoListener(String projectId, JSONObject res) {
        super(projectId);
        this.res = res;
    }

    @Override
    public void invoke(ReBussnessLeaseLedgerInfoImportParam param, AnalysisContext context) {
        total++;
        try {
            // 🔥 调试日志：记录每条记录的处理
            log.info("处理第{}条记录：客户={}, 房号={}, 楼号={}, 交款日期={}",
                    total, param.getLessee(), param.getRoomNumber(), param.getBuildingNumber(), param.getPaymentDate());

            // 🔥 优先级1：首先检查交款日期（增强验证）
            if (param.getPaymentDate() == null) {
                log.warn("❌ 跳过交款日期为null的记录：第{}条，客户={}, 房号={}, 楼号={}",
                        total, param.getLessee(), param.getRoomNumber(), param.getBuildingNumber());
                return; // 交款日期为空，跳过处理
            }

            // 🔥 优先级2：检查客户名
            if (StrUtil.isEmpty(param.getLessee())) {
                return; // 客户名为空，跳过处理
            }

            // 根据房屋信息匹配房屋
            ReHouse reHouse = findHouse(param);
            if (reHouse == null) {
                errorList.add(createQueryMap(param, "未找到房屋"));
                return;
            }

            // 🔥 新增：商业租赁重复性检查（使用交款日期）
            if (checkLeaseHouseLedgerDuplicate(reHouse.getId(), param.getLessee(), param.getPaymentDate())) {
                errorList.add(createQueryMap(param, "租赁台账记录重复，跳过导入"));
                log.info("跳过重复租赁台账记录：房源ID={}, 客户={}, 交款日期={}",
                        reHouse.getId(), param.getLessee(), param.getPaymentDate());
                return;
            }
//            if (param.getPropertyStatus()==null ||!param.getPropertyStatus().equals("租赁")) {
//                errorList.add(createQueryMap(param, "非租赁台账，跳过导入"));
//                log.info("跳过非租赁：房源ID={}, 客户={}, 交款日期={}",
//                        reHouse.getId(), param.getLessee(), param.getPaymentDate());
//                return;
//            }

            // 将所有台账置为历史
            updateLedgerHistory(reHouse.getId());

            // 插入台账信息
            ReLedgerInfo reLedgerInfo = insertLedgerInfo(param, reHouse);

            // 插入客户信息
            ReCustomerInfo reCustomerInfo = insertCustomerInfo(param);


            // 插入客户关系
            insertCustomerRelation(param, reHouse, reLedgerInfo, reCustomerInfo);

            // 插入签约信息
//            ReSalesContract reSalesContract = insertSalesContract(param, reLedgerInfo);
            ReLeaseContract reLeaseContract = insertleaseContract(param, reLedgerInfo);

            // 插入付款信息
            insertPaymentInfo(param, reLedgerInfo, reLeaseContract);

            // 插入合同信息
            insertContractInfo(param, reLedgerInfo);

            // 更新房屋状态
            updateHouseStatus(reHouse, reLedgerInfo, reCustomerInfo, reLeaseContract);

            reProjectDetailServiceAsync.updateOrInsertDetail(reHouse, projectId);
        } catch (Exception e) {
            log.error("处理数据失败: {}", e.getMessage(), e);
            errorList.add(createQueryMap(param, "系统异常:" + e.getMessage()));
        }
    }

    @Override
    protected void processData(ReBussnessLeaseLedgerInfoImportParam data) {
        // 此方法由invoke方法实现，这里留空
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (errorList.isEmpty()) {
            res.set("code", 200);
            res.set("msg", "所有导入成功");
            res.set("total",total);
        } else {
            res.set("code", 500);
            res.set("msg", "有部分数据导入失败");
            res.set("errorList", errorList);
            res.set("total",total);
        }
    }

    private ReHouse findHouse(ReBussnessLeaseLedgerInfoImportParam param) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("FLOOR", param.getFloorCount());
        queryMap.put("BUILD_CODE", param.getBuildingNumber());
        queryMap.put("HOUSE_NUMBER", param.getRoomNumber());
        queryMap.put("PROJECT_ID", projectId);
        queryMap.put("HOUSE_TYPE", HOUSE_TYPE);
        return reHouseMapper.selectOneByMap(queryMap);
    }

    // 移除此方法，使用父类的updateLedgerHistory方法

    private ReLedgerInfo insertLedgerInfo(ReBussnessLeaseLedgerInfoImportParam param, ReHouse reHouse) {
        ReLedgerInfo reLedgerInfo = new ReLedgerInfo();
        reLedgerInfo.setHouseId(reHouse.getId());
        reLedgerInfo.setProjectId(projectId);
        reLedgerInfo.setContractType("2");
        reLedgerInfo.setBuildCode(reHouse.getBuildCode());
        reLedgerInfo.setName(param.getLessee());
        reLedgerInfo.setIdCard(param.getIdCardNumber());
        reLedgerInfo.setPhone(param.getContactPhone());
        reLedgerInfo.setStatus("3");
        reLedgerInfo.setFloor(param.getFloorCount());
        reLedgerInfo.setHouseNumber(param.getRoomNumber());
        reLedgerInfo.setSubscribeTime(param.getPaymentDate());
        reLedgerInfo.setContractTime(param.getContractSignDate());
        reLedgerInfo.setDealUnitPrice(param.getRentalPricePerMonthPerSqm());
        reLedgerInfo.setArea(param.getArea());
        if (param.getTotalPayment() == null){
            param.setTotalPayment(BigDecimal.ZERO);
            reLedgerInfo.setTotalPayment(BigDecimal.ZERO);
        }else {
            reLedgerInfo.setTotalPayment(param.getTotalPayment());
//            if (reLedgerInfo.getTotalPayment().compareTo(BigDecimal.ZERO) >= 0) {
//                reLedgerInfo.setContractPrice(param.getTotalPayment());
//                reLedgerInfo.setTotalPayment(param.getTotalPayment());
//            }
        }
        reLedgerInfo.setHouseType(HOUSE_TYPE);
        reLedgerInfo.setIsHistory(false);

        // 🔧 精确计算欠款金额，解决JavaScript精度问题
        BigDecimal calculatedArrears = calculateAccurateArrears(param, reLedgerInfo);

        // 优先使用计算的欠款，如果Excel中有欠款数据则进行对比验证
        BigDecimal excelArrears = param.getArrears();
        if (excelArrears != null) {
            // 对比Excel欠款与计算欠款的差异
            BigDecimal difference = calculatedArrears.subtract(excelArrears).abs();
            if (difference.compareTo(new BigDecimal("0.01")) > 0) {
                log.warn("欠款计算差异：Excel欠款={}, 计算欠款={}, 差异={} (客户：{})",
                        excelArrears, calculatedArrears, difference, param.getLessee());
            }
        }

        // 设置精确计算的欠款到台账信息
        // 注意：这里需要检查ReLedgerInfo是否有欠款字段，如果没有则通过其他方式存储

        // 使用精确计算的欠款设置状态
        if (calculatedArrears.compareTo(BigDecimal.ZERO) <= 0) {
            reLedgerInfo.setStatus(STATUS_PAID);
        } else {
            reLedgerInfo.setStatus(STATUS_UNPAID);
        }
        reLedgerInfoMapper.insert(reLedgerInfo);
        return reLedgerInfo;
    }

    private ReCustomerInfo insertCustomerInfo(ReBussnessLeaseLedgerInfoImportParam param) {
        ReCustomerInfo reCustomerInfo = new ReCustomerInfo();
        reCustomerInfo.setPhone(param.getContactPhone());
        reCustomerInfo.setName(param.getLessee());
        reCustomerInfo.setIdCard(param.getIdCardNumber());
        reCustomerInfoMapper.insert(reCustomerInfo);
        return reCustomerInfo;
    }


    private void insertCustomerRelation(ReBussnessLeaseLedgerInfoImportParam param, ReHouse reHouse, ReLedgerInfo reLedgerInfo, ReCustomerInfo reCustomerInfo) {
        ReCustomer reCustomer = new ReCustomer();
        reCustomer.setCustomerId(reCustomerInfo.getId());
//        reCustomer.setShareholderIds(reCustomerInfo2 == null ? "" : reCustomerInfo2.getId());
        reCustomer.setEnableArea(param.getArea());
        reCustomer.setHouseId(reHouse.getId());
        reCustomer.setProjectId(projectId);
        reCustomer.setSubscribeTime(param.getPaymentDate());
        reCustomer.setContractTime(param.getContractSignDate());
        reCustomer.setLedgerId(reLedgerInfo.getId());
        reCustomerMapper.insert(reCustomer);
    }

    private ReLeaseContract insertleaseContract(ReBussnessLeaseLedgerInfoImportParam param, ReLedgerInfo reLedgerInfo) {
        ReLeaseContract reLeaseContract = new ReLeaseContract();
        reLeaseContract.setLedgerId(reLedgerInfo.getId());
        reLeaseContract.setLeaseDeposit(param.getRentalDeposit());
        reLeaseContract.setLeaseStartTime(param.getLeaseStartDate());
        reLeaseContract.setLeaseEndTime(param.getLeaseEndDate());
        reLeaseContract.setLeaseTerm(param.getLeaseTerm());
        reLeaseContract.setRentFreePeriod(param.getRentFreePeriod());
        reLeaseContract.setDiscountRemark(param.getDiscount());
        reLeaseContract.setDiscountType("1");
        reLeaseContract.setLeaseUnitPrice(param.getRentalPricePerMonthPerSqm());
        reLeaseContract.setPaymentMethod("1");

        // 🔧 修复租赁总价计算逻辑
        BigDecimal leaseTotalPrice = calculateLeaseTotalPrice(param, reLedgerInfo);
        reLeaseContract.setLeaseTotalPrice(leaseTotalPrice);

        // 🔧 确保totalHousePrice与leaseTotalPrice一致
        reLeaseContract.setTotalHousePrice(leaseTotalPrice);

        reLeaseContract.setExpireRemind(DateUtil.offsetDay(param.getLeaseEndDate(), -15));

        log.info("商业租赁合同创建成功：台账ID={}, 租赁总价={}, 租赁单价={}, 面积={}, 租期={}月, 客户={}",
            reLedgerInfo.getId(), leaseTotalPrice, param.getRentalPricePerMonthPerSqm(),
            param.getArea(), param.getLeaseTerm(), param.getLessee());

        reLeaseContractMapper.insert(reLeaseContract);
        return reLeaseContract;
    }

    /**
     * 计算租赁总价
     * 🔧 修复后的优先级：1.年租金 2.应交金额 3.公式计算
     */
    private BigDecimal calculateLeaseTotalPrice(ReBussnessLeaseLedgerInfoImportParam param, ReLedgerInfo reLedgerInfo) {
        // 🔥 优先级1：使用Excel中的年租金
        if (param.getAnnualRent() != null && param.getAnnualRent().compareTo(BigDecimal.ZERO) > 0) {
            log.info("使用Excel年租金作为租赁总价：{} (客户：{})", param.getAnnualRent(), param.getLessee());
            return param.getAnnualRent();
        }

        // 🔥 优先级2：使用Excel中的应交金额
        if (param.getPayableAmount() != null && param.getPayableAmount().compareTo(BigDecimal.ZERO) > 0) {
            log.info("年租金为空，使用Excel应交金额作为租赁总价：{} (客户：{})", param.getPayableAmount(), param.getLessee());
            return param.getPayableAmount();
        }

        // 🔥 优先级3：根据公式计算（保持向后兼容性）
        BigDecimal unitPrice = param.getRentalPricePerMonthPerSqm();
        BigDecimal area = param.getArea();
        Integer leaseTerm = param.getLeaseTerm();
        Integer rentFreePeriod = param.getRentFreePeriod();

        if (unitPrice != null && area != null && leaseTerm != null) {
            // 计算有效租期（总租期 - 免租期）
            int effectiveTerm = leaseTerm - (rentFreePeriod != null ? rentFreePeriod : 0);
            if (effectiveTerm <= 0) {
                log.warn("有效租期计算异常：总租期={}, 免租期={} (客户：{})", leaseTerm, rentFreePeriod, param.getLessee());
                effectiveTerm = leaseTerm; // 使用总租期
            }

            // 租赁总价 = 租赁单价 × 面积 × 有效租期
            BigDecimal calculatedPrice = unitPrice.multiply(area).multiply(new BigDecimal(effectiveTerm));
            log.info("年租金和应交金额均为空，根据公式计算租赁总价：{} = {} × {} × {} (客户：{})",
                    calculatedPrice, unitPrice, area, effectiveTerm, param.getLessee());
            return calculatedPrice;
        }

        log.warn("无法计算租赁总价，缺少必要参数：年租金={}, 应交金额={}, 单价={}, 面积={}, 租期={} (客户：{})",
                param.getAnnualRent(), param.getPayableAmount(), unitPrice, area, leaseTerm, param.getLessee());
        return BigDecimal.ZERO;
    }

    /**
     * 精确计算欠款金额，解决JavaScript精度问题
     *
     * @param param 导入参数
     * @param reLedgerInfo 台账信息
     * @return 精确的欠款金额
     */
    private BigDecimal calculateAccurateArrears(ReBussnessLeaseLedgerInfoImportParam param, ReLedgerInfo reLedgerInfo) {
        // 1. 获取租赁总价（使用已有的精确计算方法）
        BigDecimal leaseTotalPrice = calculateLeaseTotalPrice(param, reLedgerInfo);

        // 2. 获取合计交款金额
        BigDecimal totalPayment = param.getTotalPayment() != null ? param.getTotalPayment() : BigDecimal.ZERO;

        // 3. 精确计算欠款：租赁总价 - 合计交款
        BigDecimal arrears = leaseTotalPrice.subtract(totalPayment);

        // 4. 记录详细的计算日志
        log.info("精确欠款计算：租赁总价={} - 合计交款={} = 欠款={} (客户：{})",
                leaseTotalPrice, totalPayment, arrears, param.getLessee());

        // 5. 确保欠款不为负数（如果为负数，说明多交了钱）
        if (arrears.compareTo(BigDecimal.ZERO) < 0) {
            log.info("检测到多交款情况：欠款={} (客户：{})", arrears, param.getLessee());
        }

        return arrears;
    }

    private void insertPaymentInfo(ReBussnessLeaseLedgerInfoImportParam param, ReLedgerInfo reLedgerInfo, ReLeaseContract reSalesContract) {
        if (param.getPaidAmount() != null) {
            RePaymentInfo rePaymentInfo = new RePaymentInfo();
            rePaymentInfo.setPaymentType("1");
            rePaymentInfo.setLedgerId(reLedgerInfo.getId());
            rePaymentInfo.setContractId(reSalesContract.getId());
            rePaymentInfo.setPaymentAmount(param.getPaidAmount());
            rePaymentInfo.setPaymentTime(param.getPaymentDate());
            rePaymentInfoMapper.insert(rePaymentInfo);
        }
        if (param.getRentalDeposit() != null) {
            RePaymentInfo rePaymentInfo = new RePaymentInfo();
            rePaymentInfo.setPaymentType("1");
            rePaymentInfo.setLedgerId(reLedgerInfo.getId());
            rePaymentInfo.setContractId(reSalesContract.getId());
            rePaymentInfo.setPaymentAmount(param.getRentalDeposit());
            rePaymentInfoMapper.insert(rePaymentInfo);
        }
    }

    private void insertContractInfo(ReBussnessLeaseLedgerInfoImportParam param, ReLedgerInfo reLedgerInfo) {
        ReContractInfo reContractInfo = new ReContractInfo();
        reContractInfo.setLedgerId(reLedgerInfo.getId());
        reContractInfo.setRemark(StrUtil.isNotEmpty(param.getRemarks()) ? param.getRemarks() : "");
        //计算合同日期
        reContractInfo.setContractRecordTime(param.getContractSignDate());
        reContractInfoMapper.insert(reContractInfo);
    }

    private void updateHouseStatus(ReHouse reHouse, ReLedgerInfo reLedgerInfo, ReCustomerInfo reCustomerInfo, ReLeaseContract reSalesContract) {
        // 使用台账的状态，而不是硬编码为"2"
        reHouse.setStatus(reLedgerInfo.getStatus());
        reHouse.setCustomerPhone(reCustomerInfo.getPhone());
        reHouse.setCustomerName(reCustomerInfo.getName());
        reHouse.setLeaseUnitPrice(reSalesContract.getLeaseUnitPrice());
        reHouse.setLeaseTotalPrice(reSalesContract.getLeaseTotalPrice());
        reHouseMapper.update(reHouse);
    }

    private RePaymentInfo createPaymentInfo(BigDecimal money, String ledgerId, String contractId, Date paymentTime) {
        RePaymentInfo rePaymentInfo = new RePaymentInfo();
        rePaymentInfo.setPaymentType("2");
        rePaymentInfo.setLedgerId(ledgerId);
        rePaymentInfo.setContractId(contractId);
        rePaymentInfo.setPaymentAmount(money);
        rePaymentInfo.setPaymentTime(paymentTime);
        return rePaymentInfo;
    }

    // 移除此方法，使用父类的parseBigDecimal方法

    private Map<String, Object> createQueryMap(ReBussnessLeaseLedgerInfoImportParam param,String msg) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("楼层", param.getFloorCount());
        queryMap.put("楼号", param.getBuildingNumber());
        queryMap.put("房号", param.getRoomNumber());
        queryMap.put("项目ID", projectId);
        queryMap.put("房屋类型", HOUSE_TYPE);
        queryMap.put("错误信息", msg);
        return queryMap;
    }
}
