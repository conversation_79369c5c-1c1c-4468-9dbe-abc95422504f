/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.ReCommunityAlarmInfo;
import vip.xiaonuo.biz.modular.realty.mapper.ReCommunityAlarmInfoMapper;
import vip.xiaonuo.biz.modular.realty.param.ReCommunityAlarmInfoAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReCommunityAlarmInfoEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReCommunityAlarmInfoIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReCommunityAlarmInfoPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReCommunityAlarmInfoService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;

import java.util.List;

/**
 * 社区信息预警Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/08/30 10:05
 **/
@Component
public class ReCommunityAlarmInfoServiceImpl extends ServiceImpl<ReCommunityAlarmInfoMapper, ReCommunityAlarmInfo> implements ReCommunityAlarmInfoService {

    @Override
    public Page<ReCommunityAlarmInfo> page(ReCommunityAlarmInfoPageParam reCommunityAlarmInfoPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();

        queryWrapper.eq(ReCommunityAlarmInfo::getHouseType, reCommunityAlarmInfoPageParam.getHouseType(), ObjectUtil.isNotEmpty(reCommunityAlarmInfoPageParam.getHouseType()));
        if(ObjectUtil.isNotEmpty(reCommunityAlarmInfoPageParam.getEndDate())){
            queryWrapper.and("date_format(deadline,'%Y-%m-%d') = date_format(?,'%Y-%m-%d')",reCommunityAlarmInfoPageParam.getEndDate());
        }
        queryWrapper.eq(ReCommunityAlarmInfo::getExtJson, reCommunityAlarmInfoPageParam.getExtJson(), ObjectUtil.isNotEmpty(reCommunityAlarmInfoPageParam.getExtJson()));
        if(ObjectUtil.isAllNotEmpty(reCommunityAlarmInfoPageParam.getSortField(), reCommunityAlarmInfoPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reCommunityAlarmInfoPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reCommunityAlarmInfoPageParam.getSortField()),reCommunityAlarmInfoPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReCommunityAlarmInfo::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public void add(ReCommunityAlarmInfoAddParam reCommunityAlarmInfoAddParam) {
        ReCommunityAlarmInfo reCommunityAlarmInfo = BeanUtil.toBean(reCommunityAlarmInfoAddParam, ReCommunityAlarmInfo.class);
        this.save(reCommunityAlarmInfo);
    }

    @Tran
    @Override
    public void edit(ReCommunityAlarmInfoEditParam reCommunityAlarmInfoEditParam) {
        ReCommunityAlarmInfo reCommunityAlarmInfo = this.queryEntity(reCommunityAlarmInfoEditParam.getId());
        BeanUtil.copyProperties(reCommunityAlarmInfoEditParam, reCommunityAlarmInfo);
        this.updateById(reCommunityAlarmInfo);
    }

    @Tran
    @Override
    public void delete(List<ReCommunityAlarmInfoIdParam> reCommunityAlarmInfoIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(reCommunityAlarmInfoIdParamList, ReCommunityAlarmInfoIdParam::getId));
    }

    @Override
    public ReCommunityAlarmInfo detail(ReCommunityAlarmInfoIdParam reCommunityAlarmInfoIdParam) {
        return this.queryEntity(reCommunityAlarmInfoIdParam.getId());
    }

    @Override
    public ReCommunityAlarmInfo queryEntity(String id) {
        ReCommunityAlarmInfo reCommunityAlarmInfo = this.getById(id);
        if(ObjectUtil.isEmpty(reCommunityAlarmInfo)) {
            throw new CommonException("社区信息预警不存在，id值为：{}", id);
        }
        return reCommunityAlarmInfo;
    }
}
