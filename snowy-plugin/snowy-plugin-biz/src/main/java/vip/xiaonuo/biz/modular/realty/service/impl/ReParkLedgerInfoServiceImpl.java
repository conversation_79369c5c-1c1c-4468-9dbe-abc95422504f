/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.async.ReProjectDetailServiceAsync;
import vip.xiaonuo.biz.modular.realty.constants.LedgerConstants;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.mapper.ReParkLedgerInfoMapper;
import vip.xiaonuo.biz.modular.realty.param.*;
import vip.xiaonuo.biz.modular.realty.service.*;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import lombok.extern.slf4j.Slf4j;
import vip.xiaonuo.biz.modular.realty.constants.LedgerConstants;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 车位台账信息Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/08/17 14:35
 **/
@Slf4j
@Component
public class ReParkLedgerInfoServiceImpl extends ServiceImpl<ReParkLedgerInfoMapper, ReParkLedgerInfo> implements ReParkLedgerInfoService {

    @Inject
    private ReCustomerInfoService reCustomerInfoService;

    @Inject
    private ReParkService reParkService;

    @Inject
    private RePaymentInfoService rePaymentInfoService;

    @Inject
    private ReCustomerService reCustomerService;

    @Inject
    private ReParkContractService reParkContractService;

    @Override
    public Page<ReParkLedgerInfo> page(ReParkLedgerInfoPageParam reParkLedgerInfoPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq(ReParkLedgerInfo::getStatus, reParkLedgerInfoPageParam.getStatus(), ObjectUtil.isNotEmpty(reParkLedgerInfoPageParam.getStatus()));
        queryWrapper.eq(ReParkLedgerInfo::getType, reParkLedgerInfoPageParam.getType(), ObjectUtil.isNotEmpty(reParkLedgerInfoPageParam.getType()));
        queryWrapper.like(ReParkLedgerInfo::getName, reParkLedgerInfoPageParam.getName(), ObjectUtil.isNotEmpty(reParkLedgerInfoPageParam.getName()));
        queryWrapper.like(ReParkLedgerInfo::getIdCard, reParkLedgerInfoPageParam.getIdCard(), ObjectUtil.isNotEmpty(reParkLedgerInfoPageParam.getIdCard()));
        queryWrapper.eq(ReParkLedgerInfo::getProjectId, reParkLedgerInfoPageParam.getProjectId(), ObjectUtil.isNotEmpty(reParkLedgerInfoPageParam.getProjectId()));
        queryWrapper.eq(ReParkLedgerInfo::getIsHistory, reParkLedgerInfoPageParam.getIsHistory(), ObjectUtil.isNotEmpty(reParkLedgerInfoPageParam.getIsHistory()));
        queryWrapper.eq(ReParkLedgerInfo::getContractTime, reParkLedgerInfoPageParam.getContractTime(), ObjectUtil.isNotEmpty(reParkLedgerInfoPageParam.getContractTime()));
        if (ObjectUtil.isNotEmpty(reParkLedgerInfoPageParam.getSubscribeTime())) {
            queryWrapper.ge(ReParkLedgerInfo::getSubscribeTime, reParkLedgerInfoPageParam.getSubscribeTime()[0]);
            queryWrapper.le(ReParkLedgerInfo::getSubscribeTime, reParkLedgerInfoPageParam.getSubscribeTime()[1]);
        }
        if (ObjectUtil.isAllNotEmpty(reParkLedgerInfoPageParam.getSortField(), reParkLedgerInfoPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reParkLedgerInfoPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reParkLedgerInfoPageParam.getSortField()), reParkLedgerInfoPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReParkLedgerInfo::getId);
        }

        Page<ReParkLedgerInfo> page = this.page(CommonPageRequest.defaultPage(), queryWrapper);

        // 🔧 修复：为每条记录计算欠款金额
        for (ReParkLedgerInfo ledgerInfo : page.getRecords()) {
            calculateAndSetDebt(ledgerInfo);
        }

        return page;
    }

    @Tran
    @Override
    public void add(ReParkLedgerInfoAddParam reParkLedgerInfoAddParam) {
        ReParkLedgerInfo reParkLedgerInfo = BeanUtil.toBean(reParkLedgerInfoAddParam, ReParkLedgerInfo.class);
        this.save(reParkLedgerInfo);
    }

    @Tran
    @Override
    public void edit(ReParkLedgerInfoEditParam reParkLedgerInfoEditParam) {
        ReParkLedgerInfo reParkLedgerInfo = this.queryEntity(reParkLedgerInfoEditParam.getId());
        BeanUtil.copyProperties(reParkLedgerInfoEditParam, reParkLedgerInfo);
        this.updateById(reParkLedgerInfo);
    }

    @Tran
    @Override
    public void delete(List<ReParkLedgerInfoIdParam> reParkLedgerInfoIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(reParkLedgerInfoIdParamList, ReParkLedgerInfoIdParam::getId));
    }

    @Override
    public ReParkLedgerInfo detail(ReParkLedgerInfoIdParam reParkLedgerInfoIdParam) {
        return this.queryEntity(reParkLedgerInfoIdParam.getId());
    }

    @Override
    public ReParkLedgerInfo queryEntity(String id) {
        ReParkLedgerInfo reParkLedgerInfo = this.getById(id);
        if (ObjectUtil.isEmpty(reParkLedgerInfo)) {
            throw new CommonException("车位台账信息不存在，id值为：{}", id);
        }
        return reParkLedgerInfo;
    }

    @Inject
    private ReParkAreaService reParkAreaService;

    @Override
    public void addBySaleControl(ReParkLedgerInfoXiaoKongParam reParkLedgerInfoXiaoKongParam) {
        String parkId = reParkLedgerInfoXiaoKongParam.getParkId();
        /** 查询车位详细信息 **/
        RePark byId = reParkService.getById(parkId);
        if (null == byId) {
            throw new CommonException("车位信息不存在，id值为：{}", parkId);
        }
        // 判断车位是否已经售出 如果已经有台账 需要调用退款
        ReParkLedgerInfo one = this.getOne(new QueryWrapper().eq(ReParkLedgerInfo::getIsHistory, false).eq(ReParkLedgerInfo::getParkId, parkId));
        if (null != one) {
            throw new CommonException("车位已售出，无法重复售出");
        }
        // 通过车位区域id获取项目id
        ReParkArea reParkArea = reParkAreaService.getById(byId.getAreaId());
        ReCustomerAddParam customerInfo = reParkLedgerInfoXiaoKongParam.getCustomerInfo();
        String phone = customerInfo.getPhone();
        String name = customerInfo.getName();
        String idCard = customerInfo.getIdCard();
        ReCustomerInfo reCustomerInfo = new ReCustomerInfo();
        reCustomerInfo.setPhone(phone);
        reCustomerInfo.setName(name);
        reCustomerInfo.setIdCard(idCard);
        reCustomerInfoService.save(reCustomerInfo);
        customerInfo.setCustomerId(reCustomerInfo.getId());

        ReParkLedgerInfo reParkLedgerInfoAddParam = new ReParkLedgerInfo();
        reParkLedgerInfoAddParam.setName(name);
        reParkLedgerInfoAddParam.setIdCard(idCard);
        reParkLedgerInfoAddParam.setPhone(phone);
        reParkLedgerInfoAddParam.setCode(byId.getCode());
        reParkLedgerInfoAddParam.setType(byId.getType());
        reParkLedgerInfoAddParam.setAreaId(byId.getAreaId());
        reParkLedgerInfoAddParam.setParkPartition(byId.getParkPartition());
        reParkLedgerInfoAddParam.setContractTime(customerInfo.getContractTime());
        reParkLedgerInfoAddParam.setSubscribeTime(customerInfo.getSubscribeTime());
        reParkLedgerInfoAddParam.setContractPrice(reParkLedgerInfoXiaoKongParam.getParkSign().getContractPrice());
        String paymentRecordIds = reParkLedgerInfoXiaoKongParam.getPaymentRecordIds();
        List<RePaymentInfo> rePaymentInfos = rePaymentInfoService.listByIds(Arrays.asList(paymentRecordIds.split(",")));
        reParkLedgerInfoAddParam.setTotalPayment(rePaymentInfos.stream().map(RePaymentInfo::getPaymentAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        reParkLedgerInfoAddParam.setParkId(parkId);
        reParkLedgerInfoAddParam.setIsHistory(false);
        reParkLedgerInfoAddParam.setProjectId(reParkArea.getProjectId());
        // 🔧 修复：使用统一的状态常量定义
        if (reParkLedgerInfoAddParam.getTotalPayment().compareTo(reParkLedgerInfoAddParam.getContractPrice()) == 0) {
            reParkLedgerInfoAddParam.setStatus(LedgerConstants.STATUS_SOLD_PAID); // 已结清
        } else {
            reParkLedgerInfoAddParam.setStatus(LedgerConstants.STATUS_SOLD_UNPAID); // 未结清
        }
        this.save(reParkLedgerInfoAddParam);
        customerInfo.setLedgerId(reParkLedgerInfoAddParam.getId());
        ReCustomer reCustomer = BeanUtil.copyProperties(customerInfo, ReCustomer.class);
        reCustomerService.save(reCustomer);

        /** 添加合同 **/
        ReParkContractAddParam parkSign = reParkLedgerInfoXiaoKongParam.getParkSign();
        parkSign.setLedgerId(reParkLedgerInfoAddParam.getId());
        String add = reParkContractService.add(parkSign);

        rePaymentInfos.stream().forEach(rePaymentInfo -> {
            rePaymentInfo.setLedgerId(reParkLedgerInfoAddParam.getId());
            rePaymentInfo.setContractId(add);
        });
        rePaymentInfoService.updateBatch(rePaymentInfos);
        /** 修改车位信息 **/
        byId.setStatus(reParkLedgerInfoAddParam.getStatus());
        byId.setCustomerName(reParkLedgerInfoAddParam.getName());
        byId.setCustomerPhone(reParkLedgerInfoAddParam.getPhone());
        reParkService.updateById(byId);
        reProjectDetailServiceAsync.updateOrInsertDetail(reParkArea.getProjectId());

    }

    @Inject
    private ReProjectDetailServiceAsync reProjectDetailServiceAsync;

    @Override
    public ReParkLedgerInfoXiaoKongParamVo detailBySaleControl(ReParkLedgerInfoIdParam reParkLedgerInfoIdParam) {
        ReParkLedgerInfo one = this.getOne(new QueryWrapper().eq(ReParkLedgerInfo::getIsHistory, false).eq(ReParkLedgerInfo::getParkId, reParkLedgerInfoIdParam.getParkId()));
        if (null == one) {
            return null;
        }

        // 🔧 修复：计算欠款金额
        calculateAndSetDebt(one);

        ReParkLedgerInfoXiaoKongParamVo reParkLedgerInfoXiaoKongParamVo = new ReParkLedgerInfoXiaoKongParamVo();
        reParkLedgerInfoXiaoKongParamVo.setLedgerInfo(one);
        ReCustomer reCustomer = reCustomerService.getOne(new QueryWrapper().eq(ReCustomer::getLedgerId, one.getId()));
        if (null != reCustomer) {
            reParkLedgerInfoXiaoKongParamVo.setCustomerInfo(reCustomer);
        }
        ReParkContract reParkContract = reParkContractService.getOne(new QueryWrapper().eq(ReParkContract::getLedgerId, one.getId()));
        if (null != reParkContract) {
            reParkLedgerInfoXiaoKongParamVo.setParkSign(reParkContract);
        }
        List<RePaymentInfo> rePaymentInfos = rePaymentInfoService.list(new QueryWrapper().eq(RePaymentInfo::getLedgerId, one.getId()));
        reParkLedgerInfoXiaoKongParamVo.setPaymentList(rePaymentInfos);


        List<ReParkLedgerInfo> reParkLedgerInfos = this.list(new QueryWrapper().eq(ReParkLedgerInfo::getIsHistory, true).eq(ReParkLedgerInfo::getParkId, reParkLedgerInfoIdParam.getParkId()));
        reParkLedgerInfoXiaoKongParamVo.setHistoryLedgerList(reParkLedgerInfos);

        return reParkLedgerInfoXiaoKongParamVo;
    }

    /**
     * 🔧 修复：计算并设置欠款金额
     * 欠款 = 签约价 - 已交款 - 定金
     *
     * @param ledgerInfo 车位台账信息
     */
    private void calculateAndSetDebt(ReParkLedgerInfo ledgerInfo) {
        if (ledgerInfo == null) {
            return;
        }

        BigDecimal contractPrice = ledgerInfo.getContractPrice() != null ? ledgerInfo.getContractPrice() : BigDecimal.ZERO;
        BigDecimal totalPayment = ledgerInfo.getTotalPayment() != null ? ledgerInfo.getTotalPayment() : BigDecimal.ZERO;

        // 查询该台账的支付记录，计算定金总额
        BigDecimal depositAmount = BigDecimal.ZERO;
        try {
            List<RePaymentInfo> paymentList = rePaymentInfoService.list(
                QueryWrapper.create().eq(RePaymentInfo::getLedgerId, ledgerInfo.getId())
            );

            if (paymentList != null && !paymentList.isEmpty()) {
                depositAmount = paymentList.stream()
                    .filter(payment -> LedgerConstants.PAYMENT_TYPE_DEPOSIT.equals(payment.getPaymentType())) // 支付类型：定金
                    .map(payment -> payment.getPaymentAmount() != null ? payment.getPaymentAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
        } catch (Exception e) {
            log.warn("计算定金金额时出错，台账ID：{}, 错误：{}", ledgerInfo.getId(), e.getMessage());
        }

        // 计算欠款：签约价 - 已交款 - 定金
        BigDecimal debt = contractPrice.subtract(totalPayment).subtract(depositAmount);

        // 欠款不能为负数
        ledgerInfo.setDebt(debt.compareTo(BigDecimal.ZERO) >= 0 ? debt : BigDecimal.ZERO);

        log.debug("计算欠款：台账ID={}, 签约价={}, 已交款={}, 定金={}, 欠款={}",
                ledgerInfo.getId(), contractPrice, totalPayment, depositAmount, ledgerInfo.getDebt());
    }
}
