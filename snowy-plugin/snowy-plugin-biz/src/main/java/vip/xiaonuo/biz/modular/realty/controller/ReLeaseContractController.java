/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReLeaseContract;
import vip.xiaonuo.biz.modular.realty.param.ReLeaseContractAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReLeaseContractEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReLeaseContractIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReLeaseContractPageParam;
import vip.xiaonuo.biz.modular.realty.param.ReLeaseRenewalParam;
import vip.xiaonuo.biz.modular.realty.service.ReLeaseContractService;
import vip.xiaonuo.biz.modular.realty.service.ReLeaseRenewalService;

/**
 * 租赁签约控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 */
@Api(tags = "租赁签约控制器")
@Controller
@Valid
public class ReLeaseContractController {

    @Inject
    private ReLeaseContractService reLeaseContractService;

    @Inject
    private ReLeaseRenewalService reLeaseRenewalService;

    /**
     * 获取租赁签约分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取租赁签约分页")
    @SaCheckPermission("/biz/releasecontract/page")
    @Get
    @Mapping("/biz/releasecontract/page")
    public CommonResult<Page<ReLeaseContract>> page(ReLeaseContractPageParam reLeaseContractPageParam) {
        return CommonResult.data(reLeaseContractService.page(reLeaseContractPageParam));
    }

    /**
     * 添加租赁签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("添加租赁签约")
    @CommonLog("添加租赁签约")
    @SaCheckPermission("/biz/releasecontract/add")
    @Post
    @Mapping("/biz/releasecontract/add")
    public CommonResult<String> add(ReLeaseContractAddParam reLeaseContractAddParam) {
        reLeaseContractService.add(reLeaseContractAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑租赁签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("编辑租赁签约")
    @CommonLog("编辑租赁签约")
    @SaCheckPermission("/biz/releasecontract/edit")
    @Post
    @Mapping("/biz/releasecontract/edit")
    public CommonResult<String> edit(ReLeaseContractEditParam reLeaseContractEditParam) {
        reLeaseContractService.edit(reLeaseContractEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除租赁签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("删除租赁签约")
    @CommonLog("删除租赁签约")
    @SaCheckPermission("/biz/releasecontract/delete")
    @Post
    @Mapping("/biz/releasecontract/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReLeaseContractIdParam> reLeaseContractIdParamList) {
        reLeaseContractService.delete(reLeaseContractIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取租赁签约详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取租赁签约详情")
    @SaCheckPermission("/biz/releasecontract/detail")
    @Get
    @Mapping("/biz/releasecontract/detail")
    public CommonResult<ReLeaseContract> detail(ReLeaseContractIdParam reLeaseContractIdParam) {
        return CommonResult.data(reLeaseContractService.detail(reLeaseContractIdParam));
    }

    /**
     * 商业租赁续签
     *
     * <AUTHOR> 4.0 sonnet
     * @date 2024/12/19
     */
    @ApiOperation("商业租赁续签")
    @CommonLog("商业租赁续签")
    @SaCheckPermission("/biz/regardenledgerinfo/detail")
    @Post
    @Mapping("/biz/releasecontract/renewal")
    public CommonResult<String> renewLease(@Body ReLeaseRenewalParam renewalParam) {
        String newLedgerId = reLeaseRenewalService.renewLease(renewalParam);
        return CommonResult.data(newLedgerId);
    }

    /**
     * 检查续签重复性
     *
     * <AUTHOR> 4.0 sonnet
     * @date 2024/12/19
     */
    @ApiOperation("检查续签重复性")
    @SaCheckPermission("/biz/regardenledgerinfo/detail")
    @Post
    @Mapping("/biz/releasecontract/checkRenewalDuplicate")
    public CommonResult<String> checkRenewalDuplicate(@Body ReLeaseRenewalParam renewalParam) {
        try {
            reLeaseRenewalService.checkRenewalDuplicate(
                    renewalParam.getHouseId(),
                    renewalParam.getCustomerName(),
                    renewalParam.getPaymentDate(),
                    renewalParam.getProjectId()
            );
            return CommonResult.ok("无重复记录，可以续签");
        } catch (Exception e) {
            return CommonResult.error(e.getMessage());
        }
    }
}
