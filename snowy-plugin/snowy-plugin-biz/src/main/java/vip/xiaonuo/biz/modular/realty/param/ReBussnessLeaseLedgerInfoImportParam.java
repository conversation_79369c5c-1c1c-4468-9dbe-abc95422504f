package vip.xiaonuo.biz.modular.realty.param;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ToString
public class ReBussnessLeaseLedgerInfoImportParam {

    @ExcelProperty("序号")
    private String serialNumber;

    @ExcelProperty("项目名称")
    private String projectName;

    @ExcelProperty("楼号")
    private String buildingNumber;

    @ExcelProperty("房号")
    private String roomNumber;

    @ExcelProperty("面积")
    private BigDecimal area;

    @ExcelProperty("层数")
    private String floorCount;

    @ExcelProperty("物业现状")
    private String propertyStatus;

    @ExcelProperty("经营类别")
    private String businessCategory;

    @ExcelProperty("承租人")
    private String lessee;

    @ExcelProperty("身份证号码")
    private String idCardNumber;

    @ExcelProperty("联系电话")
    private String contactPhone;

    @ExcelProperty("租赁价格\n" +
            "（元/月/㎡）")
    private BigDecimal rentalPricePerMonthPerSqm;

    @ExcelProperty("物业费\n" +
            "（元/月/㎡）")
    private BigDecimal propertyFeePerMonthPerSqm;

    @ExcelProperty("年租金")
    private BigDecimal annualRent;

    @ExcelProperty("应交金额")
    private BigDecimal payableAmount;

    @ExcelProperty("交款日期")
    @DateTimeFormat("yyyy/MM/dd")
    private Date paymentDate; // 可替换为 Date 或 LocalDateTime 类型

    @ExcelProperty("优惠")
    private String discount;

    @ExcelProperty("交款金额")
    private BigDecimal paidAmount;

    @ExcelProperty("装修押金")
    private BigDecimal decorationDeposit;

    @ExcelProperty("租赁保证金")
    private BigDecimal rentalDeposit;

    @ExcelProperty("合计交款")
    private BigDecimal totalPayment;

    @ExcelProperty("欠款")
    private BigDecimal arrears;

    @ExcelProperty("租赁起始时间")
    @DateTimeFormat("yyyy/MM/dd")
    private Date leaseStartDate; // 可替换为 Date 或 LocalDateTime 类型

    @ExcelProperty("租赁截止时间")
    @DateTimeFormat("yyyy/MM/dd")
    private Date leaseEndDate; // 可替换为 Date 或 LocalDateTime 类型

    @ExcelProperty("租期")
    private Integer leaseTerm;

    @ExcelProperty("免租期")
    private Integer rentFreePeriod;

    @ExcelProperty("到期提醒")
    private String expirationReminder;

    @ExcelProperty("是否退保证金")
    private String refundDepositFlag;

    @ExcelProperty("合同签订日期")
    @DateTimeFormat("yyyy/MM/dd")
    private Date contractSignDate; // 可替换为 Date 或 LocalDateTime 类型

    @ExcelProperty("备注")
    private String remarks;
}
