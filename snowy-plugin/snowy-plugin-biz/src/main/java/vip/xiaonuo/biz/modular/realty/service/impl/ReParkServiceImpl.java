/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.afterturn.easypoi.cache.manager.POICacheManager;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.core.handle.Context;
import org.noear.solon.core.handle.UploadedFile;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.async.ReProjectDetailServiceAsync;
import vip.xiaonuo.biz.modular.realty.constants.LedgerConstants;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.listener.ImportParkLedgerInfoListener;
import vip.xiaonuo.biz.modular.realty.listener.ImportParkListener;
import vip.xiaonuo.biz.modular.realty.mapper.ReParkMapper;
import vip.xiaonuo.biz.modular.realty.param.*;
import vip.xiaonuo.biz.modular.realty.service.ReParkAreaService;
import vip.xiaonuo.biz.modular.realty.service.ReParkLedgerInfoService;
import vip.xiaonuo.biz.modular.realty.service.ReParkService;
import vip.xiaonuo.biz.modular.realty.service.ReProjectService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.common.util.CommonDownloadUtil;
import vip.xiaonuo.common.util.CommonResponseUtil;
import vip.xiaonuo.dev.api.DevDictApi;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static vip.xiaonuo.biz.modular.realty.entity.table.ReCustomerTableDef.RE_CUSTOMER;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReParkAreaTableDef.RE_PARK_AREA;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReParkContractTableDef.RE_PARK_CONTRACT;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReParkLedgerInfoTableDef.RE_PARK_LEDGER_INFO;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReParkTableDef.RE_PARK;

/**
 * 车位管理Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/08/17 14:34
 **/
@Component
public class ReParkServiceImpl extends ServiceImpl<ReParkMapper, RePark> implements ReParkService {


    @Inject
    private ReParkLedgerInfoService reParkLedgerInfoService;

    @Inject
    private ReProjectDetailServiceAsync reProjectDetailServiceAsync;

    @Inject
    private ReProjectService reProjectService;

    @Inject
    private ReParkAreaService reParkAreaService;

    @Inject
    private DevDictApi dictApi;

    @Override
    public Page<RePark> page(ReParkPageParam reParkPageParam) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .select(
                        RE_PARK.ALL_COLUMNS,
                        RE_PARK_LEDGER_INFO.CONTRACT_PRICE.as("contractPrice")
                ).from(RE_PARK)
                .leftJoin(RE_PARK_LEDGER_INFO).on(RE_PARK_LEDGER_INFO.PARK_ID.eq(RE_PARK.ID).and(RE_PARK_LEDGER_INFO.IS_HISTORY.eq(false)));
        queryWrapper.eq(RePark::getStatus, reParkPageParam.getStatus(), ObjectUtil.isNotEmpty(reParkPageParam.getStatus()));
        queryWrapper.like(RePark::getCode, reParkPageParam.getCode(), ObjectUtil.isNotEmpty(reParkPageParam.getCode()));
        queryWrapper.like(RePark::getCustomerName, reParkPageParam.getCustomerName(), ObjectUtil.isNotEmpty(reParkPageParam.getCustomerName()));
        queryWrapper.like(RePark::getCustomerPhone, reParkPageParam.getCustomerPhone(), ObjectUtil.isNotEmpty(reParkPageParam.getCustomerPhone()));
        queryWrapper.eq(RePark::getAreaId, reParkPageParam.getAreaId(), ObjectUtil.isNotEmpty(reParkPageParam.getAreaId()));
        if (ObjectUtil.isAllNotEmpty(reParkPageParam.getSortField(), reParkPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reParkPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reParkPageParam.getSortField()), reParkPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(RePark::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public void add(ReParkAddParam reParkAddParam) {
        RePark rePark = BeanUtil.toBean(reParkAddParam, RePark.class);
        reParkAreaService.getByIdOpt(reParkAddParam.getAreaId()).ifPresent(s -> {
            this.save(rePark);
            reProjectDetailServiceAsync.updateOrInsertDetail(s.getProjectId());
        });
    }

    @Tran
    @Override
    public void edit(ReParkEditParam reParkEditParam) {
        RePark rePark = this.queryEntity(reParkEditParam.getId());
        BeanUtil.copyProperties(reParkEditParam, rePark);
        reParkAreaService.getByIdOpt(rePark.getAreaId()).ifPresent(s -> {
            this.updateById(rePark);
            reProjectDetailServiceAsync.updateOrInsertDetail(s.getProjectId());
        });
    }

    @Tran
    @Override
    public void delete(List<ReParkIdParam> reParkIdParamList) {
        List<RePark> reParks = listByIds(CollStreamUtil.toList(reParkIdParamList, ReParkIdParam::getId));
        List<ReParkArea> list = reParkAreaService.list(QueryWrapper.create().in(ReParkArea::getId, CollStreamUtil.toList(reParks, RePark::getAreaId)));
        List<String> collect = list.stream().map(s -> s.getProjectId()).distinct().collect(Collectors.toList());
        Set<String> ids = reParks.stream().map(RePark::getId).collect(Collectors.toSet());
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(reParkIdParamList, ReParkIdParam::getId));
        collect.forEach(s -> {
            reProjectDetailServiceAsync.updateOrInsertDetail(s);
        });
        reParkLedgerInfoService.updateChain().set(ReParkLedgerInfo::getIsHistory, true).set(ReParkLedgerInfo::getDeleteFlag, "DELETED").in(ReParkLedgerInfo::getParkId, ids).update();
    }

    @Override
    public RePark detail(ReParkIdParam reParkIdParam) {
        return this.queryEntity(reParkIdParam.getId());
    }

    @Override
    public RePark queryEntity(String id) {
        RePark rePark = this.getById(id);
        if (ObjectUtil.isEmpty(rePark)) {
            throw new CommonException("车位管理不存在，id值为：{}", id);
        }
        return rePark;
    }

    @Override
    public void importPark(UploadedFile file, String projectId) {
        //TODO 通过excel导入车位信息
        File tempFile = FileUtil.writeBytes(IoUtil.readBytes(file.getContent()), FileUtil.file(FileUtil.getTmpDir() +
                FileUtil.FILE_SEPARATOR + "parkImportTemplate.xlsx"));
        // 读取excel
        EasyExcel.read(tempFile).head(ReParkImportParam.class).registerReadListener(new ImportParkListener(projectId)).headRowNumber(1).doReadAll();
    }

    @Override
    public void exportPark(Context context) throws IOException {
        try {
            InputStream inputStream = POICacheManager.getFile("parkImportTemplate.xlsx");
            byte[] bytes = IoUtil.readBytes(inputStream);
            CommonDownloadUtil.download("车位导入模板.xlsx", bytes, context);
        } catch (Exception e) {
            CommonResponseUtil.renderError(context, "下载车位导入模板失败");
        }
    }

    @Override
    public void refund(CommonValidList<ReParkIdParam> reParkIdParamList) {
        reParkIdParamList.forEach(reParkIdParam -> {
            // 🔧 修复：退款时使用统一的状态常量，设为待售状态
            this.updateChain().set(RePark::getStatus, LedgerConstants.STATUS_AVAILABLE).set(RePark::getCustomerName, "").set(RePark::getCustomerPhone, "").set(RePark::getTotalPrice, BigDecimal.ZERO).eq(RePark::getId, reParkIdParam.getId()).update();
            // 同时 修改台账信息
            reParkLedgerInfoService.getOneOpt(QueryWrapper.create().eq(ReParkLedgerInfo::getParkId, reParkIdParam.getId()).eq(ReParkLedgerInfo::getIsHistory, false)).ifPresent(s -> {
                reParkLedgerInfoService.updateChain().set(ReParkLedgerInfo::getIsHistory, true).eq(ReParkLedgerInfo::getId, s.getId()).update();
                reProjectDetailServiceAsync.updateOrInsertDetail(s.getProjectId());
            });
        });
    }

    @Override
    public void exportLedger(ReParkPageParam reParkPageParam, Context context) throws IOException {
        // 查询所有车位区域
        List<ReParkArea> list = reParkAreaService.list(QueryWrapper.create()
                .eq(ReParkArea::getProjectId, reParkPageParam.getProjectId()));
        if (CollectionUtil.isEmpty(list)) {
            CommonResponseUtil.renderError(context, "该项目下暂无车位信息");
            return;
        }

        // 获取所有车位区域的ID
        List<String> areaIds = list.stream().map(ReParkArea::getId).collect(Collectors.toList());

        // 构建查询条件
        QueryWrapper wrapper = QueryWrapper.create()
                .in(RePark::getAreaId, areaIds)
                .eq(RePark::getStatus, reParkPageParam.getStatus(), ObjectUtil.isNotEmpty(reParkPageParam.getStatus()))
                .like(RePark::getCode, reParkPageParam.getCode(), ObjectUtil.isNotEmpty(reParkPageParam.getCode()))
                .like(RePark::getCustomerName, reParkPageParam.getCustomerName(), ObjectUtil.isNotEmpty(reParkPageParam.getCustomerName()))
                .like(RePark::getCustomerPhone, reParkPageParam.getCustomerPhone(), ObjectUtil.isNotEmpty(reParkPageParam.getCustomerPhone()));

        // 查询所有车位及相关信息
        wrapper.select("ROW_NUMBER() OVER (ORDER BY `re_park`.`id`) AS index1")
                .select(RE_CUSTOMER.VILLAGE_ID, RE_CUSTOMER.CODE, RE_CUSTOMER.SUBSCRIBE_TIME,
                        RE_PARK_LEDGER_INFO.NAME, RE_PARK_LEDGER_INFO.ID_CARD, RE_PARK_LEDGER_INFO.PHONE,
                        RE_PARK_LEDGER_INFO.CONTRACT_TIME, RE_PARK_LEDGER_INFO.CONTRACT_PRICE, RE_PARK_LEDGER_INFO.TOTAL_PAYMENT,
                        RE_PARK.TYPE, RE_PARK.PARK_PARTITION, RE_PARK.CODE.as("reParkCode"), RE_PARK.TOTAL_PRICE, RE_PARK.STATUS,
                        RE_PARK_CONTRACT.DISCOUNT, RE_PARK_CONTRACT.REMARK,
                        RE_PARK_AREA.CODE.as("areaCode")
                )
                .from(RE_PARK)
                .leftJoin(RE_PARK_AREA).on(RE_PARK_AREA.ID.eq(RE_PARK.AREA_ID))
                .leftJoin(RE_PARK_LEDGER_INFO).on(RE_PARK_LEDGER_INFO.PARK_ID.eq(RE_PARK.ID)) // 改为 leftJoin
                .leftJoin(RE_CUSTOMER).on(RE_CUSTOMER.LEDGER_ID.eq(RE_PARK_LEDGER_INFO.ID))
                .leftJoin(RE_PARK_CONTRACT).on(RE_PARK_CONTRACT.PARK_ID.eq(RE_PARK.ID));

        // 查询数据
        List<ReParkLedgerExport> reParks = listAs(wrapper, ReParkLedgerExport.class);
        if (CollectionUtil.isEmpty(reParks)) {
            CommonResponseUtil.renderError(context, "该项目下暂无车位信息");
            return;
        }
        JSONArray carType = dictApi.getDictListByType("car_type");

        // 处理数据字典和欠款
        reParks.forEach(s -> {
            if(null!= s.getContractTime()){
                s.setContractTime(DateUtil.format(DateUtil.parse(s.getContractTime()), "yyyy-MM-dd"));
            }
            if(null!= s.getSubscribeTime()){
                s.setSubscribeTime(DateUtil.format(DateUtil.parse(s.getSubscribeTime()), "yyyy-MM-dd"));
            }
            if (null!=s.getType()){
                List<Object> dictLabel = carType.stream()
                        .filter(d -> JSONUtil.parseObj(d).getStr("dictValue").equals(s.getType())).collect(Collectors.toList());
                if (dictLabel.size()>0){
                    s.setType(JSONUtil.parseObj(dictLabel.get(0)).getStr("dictLabel"));
                }
            }
            if (StrUtil.isNotEmpty(s.getName())){
                if (s.getContractPrice() != null && s.getTotalPayment() != null) {
                    s.setDebtPriec(s.getContractPrice() - s.getTotalPayment());
                    s.setDebtStatus(s.getDebtPriec() > 0 ? "未结清" : "已结清");
                } else {
                    s.setDebtPriec(0.0); // 如果没有合同价格或总支付金额，默认欠款为0
                    s.setDebtStatus("无欠款信息");
                }
            }else{
                s.setDebtPriec(null);
                s.setDebtStatus(null);
            }
        });

        // 获取项目名称
        String name = reProjectService.getById(reParkPageParam.getProjectId()).getName();
        HashMap<String, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put("commityName", name);

        // 导出Excel
        String templateName = "parkLedgerExportTemplate";
        File destTemplateFile;
        InputStream inputStream = POICacheManager.getFile(templateName + ".xlsx");
        destTemplateFile = FileUtil.writeFromStream(inputStream, FileUtil.file(FileUtil.getTmpDir() +
                File.separator + System.currentTimeMillis() + templateName + ".xlsx"));

        File file = FileUtil.file(FileUtil.getTmpDir() + FileUtil.FILE_SEPARATOR + System.currentTimeMillis() + "车位台账信息.xlsx");
        try (ExcelWriter excelWriter = EasyExcel.write(file).withTemplate(destTemplateFile).build()) {
            WriteSheet writeSheet = EasyExcel.writerSheet().build();
            FillConfig fillConfig = FillConfig.builder().forceNewRow(Boolean.TRUE).build();
            excelWriter.fill(objectObjectHashMap, fillConfig, writeSheet);
            excelWriter.fill(reParks, writeSheet);
        }

        // 下载文件
        CommonDownloadUtil.download("车位台账导出" + System.currentTimeMillis() + ".xlsx", FileUtil.readBytes(file), context);

        // 删除临时文件
        FileUtil.del(destTemplateFile);
        FileUtil.del(file);
    }


    @Override
    public JSONObject importExcel(UploadedFile file, String projectId) {
        //TODO 从excel批量导入车位台账信息
        File tempFile = FileUtil.writeBytes(IoUtil.readBytes(file.getContent()), FileUtil.file(FileUtil.getTmpDir() +
                FileUtil.FILE_SEPARATOR+ System.currentTimeMillis() + "parkLedgerImportTemplate.xlsx"));
        JSONArray carType = dictApi.getDictListByType("car_type");
//        JSONArray catSettel = dictApi.getDictListByType("car_settle");
        EasyExcel.read(tempFile).head(ReParkLedgerInfoImportParam.class).registerReadListener(new ImportParkLedgerInfoListener(projectId,carType)).headRowNumber(3).sheet(0).doRead();
        return null;
    }
}
