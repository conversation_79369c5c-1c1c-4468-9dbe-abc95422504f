/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.ReProject;
import vip.xiaonuo.biz.modular.realty.entity.ReProjectDetail;
import vip.xiaonuo.biz.modular.realty.mapper.ReProjectMapper;
import vip.xiaonuo.biz.modular.realty.param.ReProjectAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReProjectEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReProjectIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReProjectPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReProjectDetailService;
import vip.xiaonuo.biz.modular.realty.service.ReProjectService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;

import java.util.List;

/**
 * 项目管理Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/08/17 14:35
 **/
@Component
public class ReProjectServiceImpl extends ServiceImpl<ReProjectMapper, ReProject> implements ReProjectService {

    @Inject
    private ReProjectDetailService reProjectDetailService;

    @Override
    public Page<ReProject> page(ReProjectPageParam reProjectPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.like(ReProject::getName, reProjectPageParam.getName(),StrUtil::isNotBlank);
        if (ObjectUtil.isAllNotEmpty(reProjectPageParam.getSortField(), reProjectPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reProjectPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reProjectPageParam.getSortField()), reProjectPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReProject::getId);
        }
        Page<ReProject> page = this.page(CommonPageRequest.defaultPage(), queryWrapper);
        if (reProjectPageParam.getWithDetail()) {
            page.getRecords().forEach(s -> {
                String id = s.getId();
                List<ReProjectDetail> list = reProjectDetailService.list(new QueryWrapper().eq(ReProjectDetail::getProjectId, id));
                s.setProjectDetails(list);
            });
        }
        return page;
    }

    @Tran
    @Override
    public void add(ReProjectAddParam reProjectAddParam) {
        ReProject reProject = BeanUtil.toBean(reProjectAddParam, ReProject.class);
        this.save(reProject);
    }

    @Tran
    @Override
    public void edit(ReProjectEditParam reProjectEditParam) {
        ReProject reProject = this.queryEntity(reProjectEditParam.getId());
        BeanUtil.copyProperties(reProjectEditParam, reProject);
        this.updateById(reProject);
    }

    @Tran
    @Override
    public void delete(List<ReProjectIdParam> reProjectIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(reProjectIdParamList, ReProjectIdParam::getId));
    }

    @Override
    public ReProject detail(ReProjectIdParam reProjectIdParam) {
        return this.queryEntity(reProjectIdParam.getId());
    }

    @Override
    public ReProject queryEntity(String id) {
        ReProject reProject = this.getById(id);
        if (ObjectUtil.isEmpty(reProject)) {
            throw new CommonException("项目管理不存在，id值为：{}", id);
        }
        return reProject;
    }

    @Override
    public List<ReProject> listByStp() {
        long loginIdAsLong = StpUtil.getLoginIdAsLong();
        if (loginIdAsLong > 0) {
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.select(ReProject::getId, ReProject::getName, ReProject::getModule, ReProject::getCategory, ReProject::getType);
            queryWrapper.where("find_in_set(?,ext_json) > 0", loginIdAsLong);
            return this.list(queryWrapper);
        }
        return List.of();
    }
}
