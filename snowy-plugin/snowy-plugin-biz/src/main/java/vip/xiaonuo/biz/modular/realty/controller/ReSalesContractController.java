/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReSalesContract;
import vip.xiaonuo.biz.modular.realty.param.ReSalesContractAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReSalesContractEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReSalesContractIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReSalesContractPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReSalesContractService;

/**
 * 销售签约控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 */
@Api(tags = "销售签约控制器")
@Controller
@Valid
public class ReSalesContractController {

    @Inject
    private ReSalesContractService reSalesContractService;

    /**
     * 获取销售签约分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取销售签约分页")
    @SaCheckPermission("/biz/resalescontract/page")
    @Get
    @Mapping("/biz/resalescontract/page")
    public CommonResult<Page<ReSalesContract>> page(ReSalesContractPageParam reSalesContractPageParam) {
        return CommonResult.data(reSalesContractService.page(reSalesContractPageParam));
    }

    /**
     * 添加销售签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("添加销售签约")
    @CommonLog("添加销售签约")
    @SaCheckPermission("/biz/resalescontract/add")
    @Post
    @Mapping("/biz/resalescontract/add")
    public CommonResult<String> add(ReSalesContractAddParam reSalesContractAddParam) {
        reSalesContractService.add(reSalesContractAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑销售签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("编辑销售签约")
    @CommonLog("编辑销售签约")
    @SaCheckPermission("/biz/resalescontract/edit")
    @Post
    @Mapping("/biz/resalescontract/edit")
    public CommonResult<String> edit(ReSalesContractEditParam reSalesContractEditParam) {
        reSalesContractService.edit(reSalesContractEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除销售签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("删除销售签约")
    @CommonLog("删除销售签约")
    @SaCheckPermission("/biz/resalescontract/delete")
    @Post
    @Mapping("/biz/resalescontract/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReSalesContractIdParam> reSalesContractIdParamList) {
        reSalesContractService.delete(reSalesContractIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取销售签约详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取销售签约详情")
    @SaCheckPermission("/biz/resalescontract/detail")
    @Get
    @Mapping("/biz/resalescontract/detail")
    public CommonResult<ReSalesContract> detail(ReSalesContractIdParam reSalesContractIdParam) {
        return CommonResult.data(reSalesContractService.detail(reSalesContractIdParam));
    }
}
