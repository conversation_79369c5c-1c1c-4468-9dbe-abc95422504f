/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service;

import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import org.noear.solon.core.handle.Context;
import org.noear.solon.core.handle.UploadedFile;
import vip.xiaonuo.biz.modular.realty.entity.RePark;
import vip.xiaonuo.biz.modular.realty.param.ReParkAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkPageParam;
import vip.xiaonuo.common.pojo.CommonValidList;

import java.io.IOException;
import java.util.List;

/**
 * 车位管理Service接口
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
public interface ReParkService extends IService<RePark> {

    /**
     * 获取车位管理分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    Page<RePark> page(ReParkPageParam reParkPageParam);

    /**
     * 添加车位管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void add(ReParkAddParam reParkAddParam);

    /**
     * 编辑车位管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void edit(ReParkEditParam reParkEditParam);

    /**
     * 删除车位管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void delete(List<ReParkIdParam> reParkIdParamList);

    /**
     * 获取车位管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    RePark detail(ReParkIdParam reParkIdParam);

    /**
     * 获取车位管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     **/
    RePark queryEntity(String id);

    /**
     * 通过excel导入车位信息
     *
     * <AUTHOR>
     * @date 2024/8/22 18:49
     */
    void importPark(UploadedFile file, String projectId);

    /**
     * 导出车位信息excel
     *
     * <AUTHOR>
     * @date 2024/8/27 18:35
     */
    void exportPark(Context context) throws IOException;

    /**
     * 车位退购
     *
     * <AUTHOR>
     * @date 2024/9/3 10:52
     */
    void refund(CommonValidList<ReParkIdParam> reParkIdParamList);

    /**
     * 车位台账导出
     *
     * <AUTHOR>
     * @date 2024/10/12 14:25
     */
    void exportLedger(ReParkPageParam reParkPageParam, Context context) throws IOException;

    JSONObject importExcel(UploadedFile file, String projectId);
}
