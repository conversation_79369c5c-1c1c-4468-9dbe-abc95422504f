/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import vip.xiaonuo.biz.modular.realty.entity.ReLeaseContract;
import vip.xiaonuo.biz.modular.realty.param.ReLeaseContractAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReLeaseContractEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReLeaseContractIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReLeaseContractPageParam;

import java.util.List;

/**
 * 租赁签约Service接口
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
public interface ReLeaseContractService extends IService<ReLeaseContract> {

    /**
     * 获取租赁签约分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    Page<ReLeaseContract> page(ReLeaseContractPageParam reLeaseContractPageParam);

    /**
     * 添加租赁签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    String add(ReLeaseContractAddParam reLeaseContractAddParam);

    /**
     * 编辑租赁签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void edit(ReLeaseContractEditParam reLeaseContractEditParam);

    /**
     * 删除租赁签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void delete(List<ReLeaseContractIdParam> reLeaseContractIdParamList);

    /**
     * 获取租赁签约详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    ReLeaseContract detail(ReLeaseContractIdParam reLeaseContractIdParam);

    /**
     * 获取租赁签约详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     **/
    ReLeaseContract queryEntity(String id);
}
