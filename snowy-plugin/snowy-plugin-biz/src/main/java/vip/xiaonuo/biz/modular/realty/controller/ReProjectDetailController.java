/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReProjectDetail;
import vip.xiaonuo.biz.modular.realty.param.ReProjectDetailAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReProjectDetailEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReProjectDetailIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReProjectDetailPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReProjectDetailService;

/**
 * 项目详情管理控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 */
@Api(tags = "项目详情管理控制器")
@Controller
@Valid
public class ReProjectDetailController {

    @Inject
    private ReProjectDetailService reProjectDetailService;

    /**
     * 获取项目详情管理分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取项目详情管理分页")
    @SaCheckPermission("/biz/reprojectdetail/page")
    @Get
    @Mapping("/biz/reprojectdetail/page")
    public CommonResult<Page<ReProjectDetail>> page(ReProjectDetailPageParam reProjectDetailPageParam) {
        return CommonResult.data(reProjectDetailService.page(reProjectDetailPageParam));
    }

    /**
     * 添加项目详情管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("添加项目详情管理")
    @CommonLog("添加项目详情管理")
    @SaCheckPermission("/biz/reprojectdetail/add")
    @Post
    @Mapping("/biz/reprojectdetail/add")
    public CommonResult<String> add(ReProjectDetailAddParam reProjectDetailAddParam) {
        reProjectDetailService.add(reProjectDetailAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑项目详情管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("编辑项目详情管理")
    @CommonLog("编辑项目详情管理")
    @SaCheckPermission("/biz/reprojectdetail/edit")
    @Post
    @Mapping("/biz/reprojectdetail/edit")
    public CommonResult<String> edit(ReProjectDetailEditParam reProjectDetailEditParam) {
        reProjectDetailService.edit(reProjectDetailEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除项目详情管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("删除项目详情管理")
    @CommonLog("删除项目详情管理")
    @SaCheckPermission("/biz/reprojectdetail/delete")
    @Post
    @Mapping("/biz/reprojectdetail/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReProjectDetailIdParam> reProjectDetailIdParamList) {
        reProjectDetailService.delete(reProjectDetailIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取项目详情管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取项目详情管理详情")
    @SaCheckPermission("/biz/reprojectdetail/detail")
    @Get
    @Mapping("/biz/reprojectdetail/detail")
    public CommonResult<ReProjectDetail> detail(ReProjectDetailIdParam reProjectDetailIdParam) {
        return CommonResult.data(reProjectDetailService.detail(reProjectDetailIdParam));
    }
}
