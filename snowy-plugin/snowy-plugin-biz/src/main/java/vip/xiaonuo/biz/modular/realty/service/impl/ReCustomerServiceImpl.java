/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.ReCustomer;
import vip.xiaonuo.biz.modular.realty.mapper.ReCustomerMapper;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReCustomerService;
import vip.xiaonuo.biz.modular.realty.service.ReLedgerInfoService;
import vip.xiaonuo.biz.modular.realty.service.ReParkLedgerInfoService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;

import java.util.List;

/**
 * 客户管理Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/08/17 14:34
 **/
@Component
public class ReCustomerServiceImpl extends ServiceImpl<ReCustomerMapper, ReCustomer> implements ReCustomerService {

    @Inject
    private ReLedgerInfoService reLedgerInfoService;

    @Override
    public Page<ReCustomer> page(ReCustomerPageParam reCustomerPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if (ObjectUtil.isAllNotEmpty(reCustomerPageParam.getSortField(), reCustomerPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reCustomerPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reCustomerPageParam.getSortField()), reCustomerPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReCustomer::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public void add(ReCustomerAddParam reCustomerAddParam) {
        ReCustomer reCustomer = BeanUtil.toBean(reCustomerAddParam, ReCustomer.class);
        this.save(reCustomer);
    }

    @Inject
    private ReParkLedgerInfoService reParkLedgerInfoService;

    @Tran
    @Override
    public void edit(ReCustomerEditParam reCustomerEditParam) {
        ReCustomer reCustomer = this.queryEntity(reCustomerEditParam.getId());
        // 同时修改台账里边的数据
        reLedgerInfoService.getByIdOpt(reCustomer.getLedgerId()).ifPresentOrElse(reLedgerInfo -> {
            reLedgerInfo.setVillageId(reCustomerEditParam.getVillageId());
            reLedgerInfo.setContractTime(reCustomerEditParam.getContractTime());
            reLedgerInfo.setSubscribeTime(reCustomerEditParam.getSubscribeTime());
            reLedgerInfo.setCode(reCustomerEditParam.getCode());
            reLedgerInfoService.updateById(reLedgerInfo);
        }, () -> {
            reParkLedgerInfoService.getByIdOpt(reCustomer.getLedgerId()).ifPresent(reParkLedgerInfo -> {
                reParkLedgerInfo.setContractTime(reCustomerEditParam.getContractTime());
                reParkLedgerInfo.setSubscribeTime(reCustomerEditParam.getSubscribeTime());
                reParkLedgerInfoService.updateById(reParkLedgerInfo);
            });
        });
        BeanUtil.copyProperties(reCustomerEditParam, reCustomer);
        this.updateById(reCustomer);
    }

    @Tran
    @Override
    public void delete(List<ReCustomerIdParam> reCustomerIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(reCustomerIdParamList, ReCustomerIdParam::getId));
    }

    @Override
    public ReCustomer detail(ReCustomerIdParam reCustomerIdParam) {
        return this.queryEntity(reCustomerIdParam.getId());
    }

    @Override
    public ReCustomer queryEntity(String id) {
        ReCustomer reCustomer = this.getById(id);
        if (ObjectUtil.isEmpty(reCustomer)) {
            throw new CommonException("客户管理不存在，id值为：{}", id);
        }
        return reCustomer;
    }
}
