/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.noear.solon.annotation.*;
import org.noear.solon.core.handle.UploadedFile;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.biz.modular.realty.entity.ReLedgerInfo;
import vip.xiaonuo.biz.modular.realty.param.*;
import vip.xiaonuo.biz.modular.realty.service.ReLedgerInfoService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;

/**
 * 房屋台账信息控制器
 *
 * <AUTHOR>
 * @date 2024/08/17 14:34
 */
@Api(tags = "房屋台账信息控制器")
@Controller
@Valid
public class ReLedgerInfoController {

    @Inject
    private ReLedgerInfoService reLedgerInfoService;

    /**
     * 获取房屋台账信息分页
     *
     * <AUTHOR>
     * @date 2024/08/17 14:34
     */
    @ApiOperation("获取房屋台账信息分页")
    @SaCheckPermission("/biz/reledgerinfo/page")
    @Get
    @Mapping("/biz/reledgerinfo/page")
    public CommonResult<Page<ReLedgerInfo>> page(ReLedgerInfoPageParam reLedgerInfoPageParam) {
        return CommonResult.data(reLedgerInfoService.page(reLedgerInfoPageParam));
    }

    /**
     * 添加房屋台账信息
     *
     * <AUTHOR>
     * @date 2024/08/17 14:34
     */
    @ApiOperation("添加房屋台账信息")
    @CommonLog("添加房屋台账信息")
    @SaCheckPermission("/biz/reledgerinfo/add")
    @Post
    @Mapping("/biz/reledgerinfo/add")
    public CommonResult<String> add(@Body ReLedgerInfoAddParam reLedgerInfoAddParam) {
        reLedgerInfoService.add(reLedgerInfoAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑房屋台账信息
     *
     * <AUTHOR>
     * @date 2024/08/17 14:34
     */
    @ApiOperation("编辑房屋台账信息")
    @CommonLog("编辑房屋台账信息")
    @SaCheckPermission("/biz/reledgerinfo/edit")
    @Post
    @Mapping("/biz/reledgerinfo/edit")
    public CommonResult<String> edit(@Body ReLedgerInfoEditParam reLedgerInfoEditParam) {
        reLedgerInfoService.edit(reLedgerInfoEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除房屋台账信息
     *
     * <AUTHOR>
     * @date 2024/08/17 14:34
     */
    @ApiOperation("删除房屋台账信息")
    @CommonLog("删除房屋台账信息")
    @SaCheckPermission("/biz/reledgerinfo/delete")
    @Post
    @Mapping("/biz/reledgerinfo/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                       CommonValidList<ReLedgerInfoIdParam> reLedgerInfoIdParamList) {
        reLedgerInfoService.delete(reLedgerInfoIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取房屋台账信息详情
     *
     * <AUTHOR>
     * @date 2024/08/17 14:34
     */
    @ApiOperation("获取房屋台账信息详情")
    @SaCheckPermission("/biz/reledgerinfo/detail")
    @Get
    @Mapping("/biz/reledgerinfo/detail")
    public CommonResult<ReLedgerInfo> detail(ReLedgerInfoIdParam reLedgerInfoIdParam) {
        return CommonResult.data(reLedgerInfoService.detail(reLedgerInfoIdParam));
    }

    /**
     * 通过销控新增房屋台账信息
     *
     * <AUTHOR>
     * @date 2024/8/20 10:11
     */
    @ApiOperation("通过销控新增房屋台账信息")
    @CommonLog("通过销控新增房屋台账信息")
    @Post
    @Mapping("/biz/reledgerinfo/addBySaleControl")
    public CommonResult<String> addBySaleControl(@Body ReLedgerInfoXiaoKongParam reLedgerInfoXiaoKongParam) {
        reLedgerInfoService.addBySaleControl(reLedgerInfoXiaoKongParam);
        return CommonResult.ok();
    }

    /**
     * 通过房屋id查询台账信息
     *
     * <AUTHOR>
     * @date 2024/8/20 14:42
     */
    @ApiOperation("通过房屋id查询台账信息")
    @Get
    @Mapping("/biz/reledgerinfo/queryEntityByHouseId")
    public CommonResult<ReLedgerInfoXiaoKongParamVo> queryEntityByHouseId(String id) {
        return CommonResult.data(reLedgerInfoService.queryEntityByHouseId(id));
    }

    /**
     * 通过台账id查询详情
     *
     * <AUTHOR>
     * @date 2024/9/20 16:51
     */
    @ApiOperation("通过台账id查询详情")
    @Get
    @Mapping("/biz/reledgerinfo/queryEntity")
    public CommonResult<ReLedgerInfoXiaoKongParamVo> queryEntity(String id) {
        return CommonResult.data(reLedgerInfoService.queryEntityById(id));
    }

    /**
     * 通过导入商业销控台账
     *
     * <AUTHOR>
     * @date 2024/9/20 16:51
     */
    @ApiOperation("导入商业销控台账")
    @CommonLog("导入商业销控台账")
    @Post
    @Mapping("/biz/reledgerinfo/bussiness/importLeder")
    public CommonResult<JSONObject> importBussinessExcel(@ApiParam(value="文件", required = true) UploadedFile file, String projectId) {
        return CommonResult.data(reLedgerInfoService.imporBussinesstExcel(file,projectId));
    }

    @ApiOperation("导入商业租赁台账")
    @CommonLog("导入商业租赁台账")
    @Post
    @Mapping("/biz/reledgerinfo/bussiness/import")
    public CommonResult<JSONObject> importBussinessExcel2(@ApiParam(value="文件", required = true) UploadedFile file, String projectId) {
        return CommonResult.data(reLedgerInfoService.importLeaseBusinessExcel(file,projectId));
    }


    /**
     * 通过导入商业销控台账
     *
     * <AUTHOR>
     * @date 2024/9/20 16:51
     */
    @ApiOperation("导入储藏室台账")
    @CommonLog("导入储藏室台账")
    @Post
    @Mapping("/biz/reledgerinfo/storeroom/importLeder")
    public CommonResult<JSONObject> importStoreroomExcel(@ApiParam(value="文件", required = true) UploadedFile file, String projectId) {
        return CommonResult.data(reLedgerInfoService.importStoreroomExcel(file,projectId));
    }


}
