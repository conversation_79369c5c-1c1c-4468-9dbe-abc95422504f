package vip.xiaonuo.biz.modular.realty.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.Solon;
import vip.xiaonuo.biz.modular.realty.async.ReProjectDetailServiceAsync;
import vip.xiaonuo.biz.modular.realty.entity.RePark;
import vip.xiaonuo.biz.modular.realty.entity.ReParkArea;
import vip.xiaonuo.biz.modular.realty.param.ReParkImportParam;
import vip.xiaonuo.biz.modular.realty.service.ReParkAreaService;
import vip.xiaonuo.biz.modular.realty.service.ReParkService;
import vip.xiaonuo.dev.api.DevDictApi;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/22 10:35
 */
@Slf4j
public class ImportParkListener implements ReadListener<ReParkImportParam> {

    private String projectId;

    public ImportParkListener(String projectId) {
        this.projectId = projectId;
    }

    private static ReParkService reParkService = Solon.context().getBean(ReParkService.class);

    private static ReParkAreaService reParkAreaService = Solon.context().getBean(ReParkAreaService.class);

    private ThreadLocal<HashMap<String, String>> threadLocal = new ThreadLocal<>();

    private ThreadLocal<List<RePark>> reParkThreadLocal = new ThreadLocal<>();

    private ThreadLocal<List<RePark>> reParkThreadLocalTwo = new ThreadLocal<>();

    private static DevDictApi devDictApi = Solon.context().getBean(DevDictApi.class);

    private static ThreadLocal<JSONArray> dictList = new ThreadLocal<>();

    private static ReProjectDetailServiceAsync reProjectDetailServiceAsync = Solon.context().getBean(ReProjectDetailServiceAsync.class);

    @Override
    public void invoke(ReParkImportParam data, AnalysisContext context) {
        if (dictList.get() == null || dictList.get().size() == 0) {
            // 首次查询字典
            JSONArray carType = devDictApi.getDictListByType("car_type");
            dictList.set(carType);
        }
        if (threadLocal.get() == null) {
            threadLocal.set(new HashMap<>());
        }
        if (reParkThreadLocal.get() == null) {
            reParkThreadLocal.set(new ArrayList<>());
        }
        if (!threadLocal.get().containsKey(data.getAreaCode())) {
            ReParkArea one = reParkAreaService.getOne(QueryWrapper.create().eq(ReParkArea::getCode, data.getAreaCode()).eq(ReParkArea::getProjectId, projectId));
            if (one == null) {
                log.error("车位不存在,车位区域编号:{}", data.getAreaCode());
            } else {
                threadLocal.get().put(data.getAreaCode(), one.getId());
            }
        }
        RePark rePark = BeanUtil.copyProperties(data, RePark.class);
        // 通过车位区域编号查询车位区域id
        dictList.get().stream().filter(s -> JSONUtil.parseObj(s).getStr("dictLabel").equals(data.getType())).findFirst().ifPresent(s -> {
            rePark.setType(JSONUtil.parseObj(s).getStr("dictValue"));
        });
        rePark.setAreaId(threadLocal.get().get(data.getAreaCode()));
        // 校验每次数据 通过区域编号 编码 车位类型 查询是否存在
        try {
            reParkService.getOneOpt(QueryWrapper.create().eq(RePark::getAreaId, rePark.getAreaId()).eq(RePark::getCode, rePark.getCode()).eq(RePark::getType, rePark.getType())).ifPresentOrElse(s->{
                rePark.setId(s.getId());
                reParkThreadLocalTwo.get().add(rePark);
            },()->{
                reParkThreadLocal.get().add(rePark);
                // 每一百条数据插入一次 存入到当前线程中
                if (threadLocal.get().size() == 100) {
                    reParkService.saveBatch(reParkThreadLocal.get());
                    reParkThreadLocal.remove();
                }
            });
        }catch (Exception e){

        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (reParkThreadLocal.get() != null && reParkThreadLocal.get().size() > 0) {
            reParkService.saveBatch(reParkThreadLocal.get());
            reParkThreadLocal.remove();
        }
        if (reParkThreadLocalTwo.get() != null && reParkThreadLocalTwo.get().size() > 0) {
            reParkThreadLocalTwo.get().forEach(rePark -> {
                reParkService.update(rePark, QueryWrapper.create().eq(RePark::getAreaId, rePark.getAreaId()).eq(RePark::getCode, rePark.getCode()).eq(RePark::getType, rePark.getType()));
            });
            reParkThreadLocalTwo.remove();
        }
        if (dictList.get() != null) {
            dictList.remove();
        }
        reProjectDetailServiceAsync.updateOrInsertDetail(projectId);
    }
}
