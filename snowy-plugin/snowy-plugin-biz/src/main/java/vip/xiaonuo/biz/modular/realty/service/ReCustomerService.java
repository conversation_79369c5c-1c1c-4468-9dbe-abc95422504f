/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import vip.xiaonuo.biz.modular.realty.entity.ReCustomer;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerPageParam;

import java.util.List;

/**
 * 客户管理Service接口
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
public interface ReCustomerService extends IService<ReCustomer> {

    /**
     * 获取客户管理分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    Page<ReCustomer> page(ReCustomerPageParam reCustomerPageParam);

    /**
     * 添加客户管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void add(ReCustomerAddParam reCustomerAddParam);

    /**
     * 编辑客户管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void edit(ReCustomerEditParam reCustomerEditParam);

    /**
     * 删除客户管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void delete(List<ReCustomerIdParam> reCustomerIdParamList);

    /**
     * 获取客户管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    ReCustomer detail(ReCustomerIdParam reCustomerIdParam);

    /**
     * 获取客户管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     **/
    ReCustomer queryEntity(String id);
}
