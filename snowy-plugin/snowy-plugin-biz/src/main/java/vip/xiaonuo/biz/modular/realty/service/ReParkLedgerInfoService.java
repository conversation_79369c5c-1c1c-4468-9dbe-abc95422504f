/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import vip.xiaonuo.biz.modular.realty.entity.ReParkLedgerInfo;
import vip.xiaonuo.biz.modular.realty.param.*;

import java.util.List;

/**
 * 车位台账信息Service接口
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
public interface ReParkLedgerInfoService extends IService<ReParkLedgerInfo> {

    /**
     * 获取车位台账信息分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    Page<ReParkLedgerInfo> page(ReParkLedgerInfoPageParam reParkLedgerInfoPageParam);

    /**
     * 添加车位台账信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    void add(ReParkLedgerInfoAddParam reParkLedgerInfoAddParam);

    /**
     * 编辑车位台账信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    void edit(ReParkLedgerInfoEditParam reParkLedgerInfoEditParam);

    /**
     * 删除车位台账信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    void delete(List<ReParkLedgerInfoIdParam> reParkLedgerInfoIdParamList);

    /**
     * 获取车位台账信息详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    ReParkLedgerInfo detail(ReParkLedgerInfoIdParam reParkLedgerInfoIdParam);

    /**
     * 获取车位台账信息详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     **/
    ReParkLedgerInfo queryEntity(String id);

    /**
     * 通过销控新增车位台账信息
     *
     * <AUTHOR>
     * @date 2024/8/20 15:16
     */
    void addBySaleControl(ReParkLedgerInfoXiaoKongParam reParkLedgerInfoXiaoKongParam);

    /**
     * 通过销控查询车位台账详情
     *
     * <AUTHOR>
     * @date 2024/8/20 15:59
     */
    ReParkLedgerInfoXiaoKongParamVo detailBySaleControl(ReParkLedgerInfoIdParam reParkLedgerInfoIdParam);
}
