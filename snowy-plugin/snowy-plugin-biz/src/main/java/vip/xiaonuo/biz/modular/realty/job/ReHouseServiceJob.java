package vip.xiaonuo.biz.modular.realty.job;

import cn.hutool.core.collection.CollectionUtil;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.service.*;
import vip.xiaonuo.common.timer.CommonTimerTaskRunner;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/30 8:44
 */
@Slf4j
@Component
public class ReHouseServiceJob implements CommonTimerTaskRunner {

    @Inject
    private ReLedgerInfoService reLedgerInfoService;

    @Inject
    private ReLeaseContractService reLeaseContractService;

    @Inject
    private RePlacementContractService rePlacementContractService;

    @Inject
    private ReSalesContractService reSalesContractService;

    @Inject
    private ReCommunityAlarmInfoService reCommunityAlarmInfoService;

    @Inject
    private RePaymentInfoService rePaymentInfoService;

    @Inject
    private ReParkLedgerInfoService reParkLedgerInfoService;

    @Inject
    private ReHouseService reHouseService;

    @Inject
    private ReProjectService reProjectService;

    @Override
    public void action() {
        log.info("开始执行租赁到期提醒任务...");
        try {
            /** 查询签约类型为租赁的或者其他 但是未结清的数据 查询台账信息 是否历史字段为false的数据  **/
            List<ReLedgerInfo> list = reLedgerInfoService.list(QueryWrapper.create().eq(ReLedgerInfo::getStatus, 2).eq(ReLedgerInfo::getIsHistory, false));
            log.info("查询到{}条待处理的台账记录", list.size());

            if (CollectionUtil.isNotEmpty(list)) {
                /** 判断是什么签约类型 **/
                for (ReLedgerInfo reLedgerInfo : list) {
                    try {
                        processLedgerInfo(reLedgerInfo);
                    } catch (Exception e) {
                        log.error("处理台账记录失败，台账ID：{}，错误信息：{}", reLedgerInfo.getId(), e.getMessage(), e);
                    }
                }
            }
            log.info("租赁到期提醒任务执行完成");
        } catch (Exception e) {
            log.error("租赁到期提醒任务执行失败：{}", e.getMessage(), e);
        }
    }

    private void processLedgerInfo(ReLedgerInfo reLedgerInfo) {
        ReCommunityAlarmInfo reCommunityAlarmInfo = new ReCommunityAlarmInfo();
        // 设置基本信息
        reCommunityAlarmInfo.setCustomerName(reLedgerInfo.getName());
        reCommunityAlarmInfo.setContactInformation(reLedgerInfo.getPhone());
        reCommunityAlarmInfo.setLedgerId(reLedgerInfo.getId());
        reCommunityAlarmInfo.setHouseNumber(reLedgerInfo.getHouseNumber());
        reCommunityAlarmInfo.setHouseType(reLedgerInfo.getHouseType());

        // 设置楼栋信息（优先使用台账中的信息）
        String buildingCode = reLedgerInfo.getBuildCode();

        // 查询房屋信息补充数据
        String houseId = reLedgerInfo.getHouseId();
        ReHouse house = reHouseService.getById(houseId);

        if (house != null && house.getProjectId() != null) {
            // 如果房屋信息中的楼栋代码不为空，则使用房屋信息中的
            if (house.getBuildCode() != null) {
                buildingCode = house.getBuildCode();
            }
            // 设置社区信息
            reProjectService.getByIdOpt(house.getProjectId()).ifPresent(reProject -> {
                reCommunityAlarmInfo.setCommunity(reProject.getName());
            });
        }

        reCommunityAlarmInfo.setBuilding(buildingCode);
        // 设置项目ID到扩展字段
        reCommunityAlarmInfo.setExtJson(reLedgerInfo.getProjectId());

        if (reLedgerInfo.getContractType().equals("2")) {
            /** 租赁 通过台账id查询签约信息 **/
            processLeaseContract(reLedgerInfo, reCommunityAlarmInfo);
        } else {
            /** 销售 安置 **/
            processSalesOrPlacementContract(reLedgerInfo, reCommunityAlarmInfo);
        }
    }

    private void processLeaseContract(ReLedgerInfo reLedgerInfo, ReCommunityAlarmInfo reCommunityAlarmInfo) {
        ReLeaseContract reLeaseContract = new ReLeaseContract();
        reLeaseContract.setLedgerId(reLedgerInfo.getId());

        reLeaseContractService.getOneAsOpt(QueryWrapper.create(reLeaseContract), ReLeaseContract.class).ifPresent(leaseContract -> {
            if (leaseContract.getExpireRemind() != null && leaseContract.getExpireRemind().compareTo(new Date()) <= 0) {
                log.info("租赁合同到期提醒触发，台账ID：{}，客户：{}，到期提醒时间：{}",
                    reLedgerInfo.getId(), reLedgerInfo.getName(), leaseContract.getExpireRemind());

                reCommunityAlarmInfo.setDeadline(leaseContract.getLeaseEndTime());

                // 使用更精确的重复判断条件：台账ID + 截止日期
                if (!isDuplicateAlarm(reLedgerInfo.getId(), leaseContract.getLeaseEndTime())) {
                    reCommunityAlarmInfoService.save(reCommunityAlarmInfo);
                    log.info("租赁到期预警保存成功，台账ID：{}，客户：{}", reLedgerInfo.getId(), reLedgerInfo.getName());
                } else {
                    log.info("租赁到期预警已存在，跳过保存，台账ID：{}，客户：{}", reLedgerInfo.getId(), reLedgerInfo.getName());
                }
            }
        });
    }

    private void processSalesOrPlacementContract(ReLedgerInfo reLedgerInfo, ReCommunityAlarmInfo reCommunityAlarmInfo) {
        // 签约id
        AtomicReference<String> contractId = new AtomicReference<>();
        QueryWrapper eq = QueryWrapper.create().eq("ledger_id", reLedgerInfo.getId());

        if (reLedgerInfo.getContractType().equals("1")) {
            reSalesContractService.getOneOpt(eq).ifPresent(reSalesContract -> {
                contractId.set(reSalesContract.getId());
            });
        } else {
            rePlacementContractService.getOneOpt(eq).ifPresent(rePlacementContract -> {
                contractId.set(rePlacementContract.getId());
            });
        }

        if (contractId.get() != null) {
            // 通过签约id查询付款信息
            List<RePaymentInfo> rePaymentInfos = rePaymentInfoService.list(QueryWrapper.create().eq(RePaymentInfo::getContractId, contractId.get()));
            List<RePaymentInfo> paymentList = rePaymentInfos.stream()
                .filter(s -> s.getType() != null && s.getType().equals("1"))
                .sorted(Comparator.comparing(RePaymentInfo::getCreateTime))
                .collect(Collectors.toList());
            List<RePaymentInfo> receivedList = rePaymentInfos.stream()
                .filter(s -> s.getType() != null && s.getType().equals("2"))
                .sorted(Comparator.comparing(RePaymentInfo::getCreateTime))
                .collect(Collectors.toList());

            // 修正逻辑：计算未交款的付款记录
            if (paymentList.size() > receivedList.size() && paymentList.size() > 0) {
                // 修正索引计算：获取未交款的记录
                List<RePaymentInfo> unpaidList = paymentList.subList(receivedList.size(), paymentList.size());
                if (unpaidList.size() > 0) {
                    Date paymentTime = unpaidList.get(0).getPaymentTime();
                    if (paymentTime.compareTo(new Date()) < 0) {
                        log.info("销售/安置合同付款逾期，台账ID：{}，客户：{}，逾期付款时间：{}",
                            reLedgerInfo.getId(), reLedgerInfo.getName(), paymentTime);

                        reCommunityAlarmInfo.setDeadline(paymentTime);

                        // 使用更精确的重复判断条件
                        if (!isDuplicateAlarm(reLedgerInfo.getId(), paymentTime)) {
                            reCommunityAlarmInfoService.save(reCommunityAlarmInfo);
                            log.info("付款逾期预警保存成功，台账ID：{}，客户：{}", reLedgerInfo.getId(), reLedgerInfo.getName());
                        } else {
                            log.info("付款逾期预警已存在，跳过保存，台账ID：{}，客户：{}", reLedgerInfo.getId(), reLedgerInfo.getName());
                        }
                    }
                }
            }
        }
    }

    /**
     * 判断是否重复预警
     * 使用台账ID + 截止日期作为唯一标识
     */
    private boolean isDuplicateAlarm(String ledgerId, Date deadline) {
        QueryWrapper queryWrapper = QueryWrapper.create()
            .eq(ReCommunityAlarmInfo::getLedgerId, ledgerId)
            .eq(ReCommunityAlarmInfo::getDeadline, deadline);
        return reCommunityAlarmInfoService.getOneOpt(queryWrapper).isPresent();
    }
}
