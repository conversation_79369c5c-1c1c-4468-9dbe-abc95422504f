<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.biz.modular.realty.mapper.ReGardenLedgerInfoMapper">

    <select id="selectIndexStatistics"
            resultType="vip.xiaonuo.biz.modular.realty.param.ReGardenIndexStatisticsParam">
    SELECT
        COUNT(*) AS totalHouse,
        PROJECT_ID AS projectId,
        SUM(BUILD_AREA) AS totalArea,
        SUM(CASE WHEN `STATUS` = 1 THEN 1 ELSE 0 END) AS rentHouse,
        SUM(CASE WHEN `STATUS` = 1 THEN BUILD_AREA ELSE 0 END) AS rentArea,
        SUM(CASE WHEN `STATUS` != 1 THEN 1 ELSE 0 END) AS notRentHouse,
        SUM(CASE WHEN `STATUS` != 1 THEN BUILD_AREA ELSE 0 END) AS notRentArea,
        HOUSE_TYPE AS houseType
        FROM
            re_garden_house
        <where>
            DELETE_FLAG = "NOT_DELETE"
            <if test="projectIds != null">
                AND PROJECT_ID IN
                <foreach collection="projectIds" item="projectId" open="(" close=")" separator=",">
                    #{projectId}
                </foreach>
            </if>
        </where>
        GROUP BY
            HOUSE_TYPE,PROJECT_ID;
    </select>
    <select id="selectIndexStatisticsTimeDeprecated"
            resultType="vip.xiaonuo.biz.modular.realty.param.ReGardenStatisticsTimeParam">
        SELECT
            h.ID houseId,
            li.ID ledgerId,
            gc.ID contractId,
            h.HOUSE_TYPE houseType,
            h.BUILD_AREA buildArea,
            SUM( CASE WHEN h.`STATUS` = 1 THEN 1 ELSE 0 END ) AS rentHouse,
            SUM( CASE WHEN h.`STATUS` = 1 THEN BUILD_AREA ELSE 0 END ) AS rentArea,
            SUM( CASE WHEN h.`STATUS` != 1 THEN 1 ELSE 0 END ) AS notRentHouse,
            SUM( CASE WHEN h.`STATUS` != 1 THEN BUILD_AREA ELSE 0 END ) AS notRentArea,
            IFNULL(SUM(CASE WHEN gc.`RENT_ARREARS` IS NULL THEN 0 ELSE gc.RENT_ARREARS END), 0) AS rentArrears
        FROM
            re_garden_house h
                LEFT JOIN re_garden_ledger_info li ON li.HOUSE_ID = h.ID
                AND li.IS_HISTORY = 0
                LEFT JOIN re_garden_contract gc ON li.ID = gc.LEDGER_ID
        <where>
            li.DELETE_FLAG = "NOT_DELETE"
            <if test="projectId != null">
                AND li.PROJECT_ID = #{projectId}
            </if>
            <if test="startTime != null">
                AND li.CREATE_TIME &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND li.CREATE_TIME &lt;= #{endTime}
            </if>
        </where>
        GROUP BY h.ID,li.ID,gc.ID
    </select>

    <select id="selectIndexStatisticsNotTime"
            resultType="vip.xiaonuo.biz.modular.realty.param.ReGardenStatisticsParam">
        SELECT
        h.HOUSE_TYPE houseType,
        COUNT(h.ID) AS totalHouse,
        SUM(h.BUILD_AREA) AS totalArea,
        SUM(CASE WHEN h.`STATUS` != 1 THEN 1 ELSE 0 END) AS notRentHouse
        FROM
        re_garden_house h
        LEFT JOIN re_garden_ledger_info li
        ON li.HOUSE_ID = h.ID AND li.IS_HISTORY = 0
        LEFT JOIN re_garden_contract gc
        ON li.ID = gc.LEDGER_ID
        <where>
            h.DELETE_FLAG = "NOT_DELETE"
            <if test="projectId != null">
                AND h.PROJECT_ID = #{projectId}
            </if>
        </where>
        GROUP BY h.HOUSE_TYPE
    </select>

    <select id="selectIndexStatisticsTime"
            resultType="vip.xiaonuo.biz.modular.realty.param.ReGardenStatisticsParam">
        SELECT
        h.HOUSE_TYPE houseType,
        SUM(CASE WHEN h.`STATUS` = 1 THEN 1 ELSE 0 END) AS rentHouse,
        SUM(CASE WHEN h.`STATUS` = 1 THEN h.BUILD_AREA ELSE 0 END) AS rentArea,
        SUM(CASE WHEN gc.CONTRACT_TIME IS NOT NULL THEN 1 ELSE 0 END) AS signHouse
        FROM
        re_garden_house h
        LEFT JOIN re_garden_ledger_info li
        ON li.HOUSE_ID = h.ID AND li.IS_HISTORY = 0
        LEFT JOIN re_garden_contract gc
        ON li.ID = gc.LEDGER_ID
        <where>
            h.DELETE_FLAG = "NOT_DELETE"
            <if test="projectId != null">
                AND h.PROJECT_ID = #{projectId}
            </if>
            <if test="startTime != null">
                AND li.CREATE_TIME &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND li.CREATE_TIME &lt;= #{endTime}
            </if>
        </where>
        GROUP BY h.HOUSE_TYPE
    </select>
</mapper>