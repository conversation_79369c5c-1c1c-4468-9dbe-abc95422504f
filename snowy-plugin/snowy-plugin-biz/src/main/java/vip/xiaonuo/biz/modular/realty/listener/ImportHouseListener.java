package vip.xiaonuo.biz.modular.realty.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.Solon;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.async.ReProjectDetailServiceAsync;
import vip.xiaonuo.biz.modular.realty.entity.ReBuilding;
import vip.xiaonuo.biz.modular.realty.entity.ReHouse;
import vip.xiaonuo.biz.modular.realty.param.ReHouseImportParam;
import vip.xiaonuo.biz.modular.realty.service.ReBuildingService;
import vip.xiaonuo.biz.modular.realty.service.ReHouseService;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.dev.api.DevDictApi;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * 房屋信息导入监听器
 * <AUTHOR>
 * @date 2024/8/22 10:35
 */
@Slf4j
public class ImportHouseListener implements ReadListener<ReHouseImportParam> {

    private static final int BATCH_SIZE = 100;
    private final String projectId;

    private static final ReHouseService reHouseService;
    private static final ReBuildingService reBuildingService;
    private static final DevDictApi devDictApi;
    private static final ReProjectDetailServiceAsync reProjectDetailServiceAsync;

    static {
        reHouseService = Solon.context().getBean(ReHouseService.class);
        reBuildingService = Solon.context().getBean(ReBuildingService.class);
        devDictApi = Solon.context().getBean(DevDictApi.class);
        reProjectDetailServiceAsync = Solon.context().getBean(ReProjectDetailServiceAsync.class);
    }

    private final ThreadLocal<HashMap<String, String>> buildingCache = ThreadLocal.withInitial(HashMap::new);
    private final ThreadLocal<List<ReHouse>> newHousesList = ThreadLocal.withInitial(ArrayList::new);
    private final ThreadLocal<List<ReHouse>> updateHousesList = ThreadLocal.withInitial(ArrayList::new);
    private final ThreadLocal<JSONArray> houseLayoutCache = new ThreadLocal<>();

    public ImportHouseListener(String projectId) {
        this.projectId = Objects.requireNonNull(projectId, "projectId cannot be null");
    }

    @Override
    @Tran
    public void invoke(ReHouseImportParam data, AnalysisContext context) {
        try {
            // 设置房屋类型
            setHouseType(data, context);

            if (data.getBuildCode() == null || data.getBuildCode().isEmpty()) {
                return;
            }
            if(data.getArea()!=null){
                data.setActualBuildArea(data.getArea());
            }

            // 获取并验证楼栋信息
            String buildingId = getBuildingId(data.getBuildCode());

            // 转换并设置基本信息
            ReHouse reHouse = convertToReHouse(data, buildingId);

            // 设置户型布局
            setHouseLayout(reHouse, data.getHouseLayout());

            // 处理房屋信息（新增或更新）
            processHouseInfo(reHouse);

        } catch (Exception e) {
            String errorMsg = String.format("导入房屋信息失败,楼栋编号:%s,楼层:%s,单元:%s,房屋编号:%s",
                    data.getBuildCode(), data.getFloor(), data.getUnit(), data.getHouseNumber());
            log.error(errorMsg, e);
            throw new CommonException(errorMsg, e);
        }
    }

    private void setHouseType(ReHouseImportParam data, AnalysisContext context) {
        String sheetName = context.readSheetHolder().getSheetName();
        switch (sheetName) {
            case "住宅房导入":
                data.setHouseType("1");
                break;
            case "商业房导入":
                data.setHouseType("2");
                break;
            case "储藏室导入":
                data.setHouseType("3");
                break;
            default:
                log.debug("未知的sheet类型:{}, 数据:{}", sheetName, data);
        }
    }

    private String getBuildingId(String buildCode) {
        return buildingCache.get().computeIfAbsent(buildCode, code -> {
            ReBuilding building = reBuildingService.getOne(
                    QueryWrapper.create()
                            .eq(ReBuilding::getProjectId, projectId)
                            .eq(ReBuilding::getCode, code)
            );
            if (building == null) {
                throw new CommonException("楼栋不存在,楼栋编号:" + code);
            }
            return building.getId();
        });
    }

    private ReHouse convertToReHouse(ReHouseImportParam data, String buildingId) {
        ReHouse reHouse = BeanUtil.copyProperties(data, ReHouse.class);
        reHouse.setBuildId(buildingId);
        reHouse.setProjectId(projectId);
        return reHouse;
    }

    private void setHouseLayout(ReHouse reHouse, String houseLayoutLabel) {
        if (houseLayoutCache.get() == null) {
            houseLayoutCache.set(devDictApi.getDictListByType("house_layout"));
        }

        houseLayoutCache.get().stream()
                .map(JSONUtil::parseObj)
                .filter(obj -> obj.getStr("dictLabel").equals(houseLayoutLabel))
                .findFirst()
                .ifPresent(obj -> reHouse.setHouseLayout(obj.getStr("dictValue")));
    }

    private void processHouseInfo(ReHouse reHouse) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ReHouse::getFloor, reHouse.getFloor(), ObjectUtil.isNotEmpty(reHouse.getFloor()))
                .eq(ReHouse::getUnit, reHouse.getUnit(), ObjectUtil.isNotEmpty(reHouse.getUnit()))
                .eq(ReHouse::getHouseType, reHouse.getHouseType())
                .eq(ReHouse::getHouseNumber, reHouse.getHouseNumber())
                .eq(ReHouse::getBuildCode, reHouse.getBuildCode())
                .eq(ReHouse::getProjectId, projectId);

        reHouseService.getOneOpt(queryWrapper).ifPresentOrElse(
                existingHouse -> {
                    reHouse.setId(existingHouse.getId());
                    updateHousesList.get().add(reHouse);
                },
                () -> {
                    List<ReHouse> houses = newHousesList.get();
                    houses.add(reHouse);
                    if (houses.size() >= BATCH_SIZE) {
                        reHouseService.saveBatch(houses);
                        houses.clear();
                    }
                }
        );
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        try {
            // 处理剩余的新增数据
            List<ReHouse> remainingNewHouses = newHousesList.get();
            if (!remainingNewHouses.isEmpty()) {
                reHouseService.saveBatch(remainingNewHouses);
            }

            // 处理需要更新的数据
            List<ReHouse> housesToUpdate = updateHousesList.get();
            if (!housesToUpdate.isEmpty()) {
                for (ReHouse house : housesToUpdate) {
                    reHouseService.update(house, QueryWrapper.create()
                            .eq(ReHouse::getFloor, house.getFloor(), ObjectUtil.isNotEmpty(house.getFloor()))
                            .eq(ReHouse::getUnit, house.getUnit())
                            .eq(ReHouse::getHouseNumber, house.getHouseNumber())
                            .eq(ReHouse::getBuildCode, house.getBuildCode())
                            .eq(ReHouse::getProjectId, projectId)
                    );
                }
            }

            // 更新项目详情
            devDictApi.getDictListByType("house_type").forEach(dict -> {
                ReHouse reHouse = new ReHouse();
                reHouse.setHouseType(JSONUtil.parseObj(dict).getStr("dictValue"));
                reProjectDetailServiceAsync.updateOrInsertDetail(reHouse, projectId);
            });
        } finally {
            // 清理ThreadLocal资源
            buildingCache.remove();
            newHousesList.remove();
            updateHousesList.remove();
            houseLayoutCache.remove();
        }
    }
}