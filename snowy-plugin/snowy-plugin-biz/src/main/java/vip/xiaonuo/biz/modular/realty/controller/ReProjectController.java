/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReProject;
import vip.xiaonuo.biz.modular.realty.param.ReProjectAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReProjectEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReProjectIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReProjectPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReProjectService;
import vip.xiaonuo.sys.api.SysModuleApi;

import java.util.List;

/**
 * 项目管理控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 */
@Api(tags = "项目管理控制器")
@Controller
@Valid
public class ReProjectController {

    @Inject
    private ReProjectService reProjectService;

    @Inject
    private SysModuleApi sysModuleApi;

    /**
     * 获取项目管理分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取项目管理分页")
    @SaCheckPermission("/biz/reproject/page")
    @Get
    @Mapping("/biz/reproject/page")
    public CommonResult<Page<ReProject>> page(ReProjectPageParam reProjectPageParam) {
        return CommonResult.data(reProjectService.page(reProjectPageParam));
    }

    /**
     * 添加项目管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("添加项目管理")
    @CommonLog("添加项目管理")
    @SaCheckPermission("/biz/reproject/add")
    @Post
    @Mapping("/biz/reproject/add")
    public CommonResult<String> add(ReProjectAddParam reProjectAddParam) {
        reProjectService.add(reProjectAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑项目管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("编辑项目管理")
    @CommonLog("编辑项目管理")
    @SaCheckPermission("/biz/reproject/edit")
    @Post
    @Mapping("/biz/reproject/edit")
    public CommonResult<String> edit(ReProjectEditParam reProjectEditParam) {
        reProjectService.edit(reProjectEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除项目管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("删除项目管理")
    @CommonLog("删除项目管理")
    @SaCheckPermission("/biz/reproject/delete")
    @Post
    @Mapping("/biz/reproject/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReProjectIdParam> reProjectIdParamList) {
        reProjectService.delete(reProjectIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取项目管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取项目管理详情")
    @SaCheckPermission("/biz/reproject/detail")
    @Get
    @Mapping("/biz/reproject/detail")
    public CommonResult<ReProject> detail(ReProjectIdParam reProjectIdParam) {
        return CommonResult.data(reProjectService.detail(reProjectIdParam));
    }


    /**
     * 通过当前登录用户查询当前用户的项目列表
     *
     * <AUTHOR>
     * @date 2024/8/20 10:03
     */
    @ApiOperation("通过当前登录用户查询当前用户的项目列表")
    @Get
    @Mapping("/biz/reproject/list")
    public CommonResult<List<ReProject>> listByStp() {
        return CommonResult.data(reProjectService.listByStp());
    }


    /**
     * 获取模块分页
     *
     * <AUTHOR>
     * @date 2022/4/24 20:00
     */
    @ApiOperation("获取模块分页")
    @Get
    @Mapping("/biz/module/page")
    public CommonResult<Page<JSONObject>> page() {
        return CommonResult.data(sysModuleApi.moduleSelector());
    }
}
