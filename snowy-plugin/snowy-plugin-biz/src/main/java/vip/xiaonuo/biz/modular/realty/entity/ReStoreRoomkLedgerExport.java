package vip.xiaonuo.biz.modular.realty.entity;

import lombok.Data;

/**
 * 储藏间导出实体
 * 该实体类用于车位储藏间导出
 * <AUTHOR>
 * @date 2024/10/11 15:25
 */

@Data
public class ReStoreRoomkLedgerExport {

    /** 序号 */
    private int index1;

    /** 客户姓名 */
    private String name;

    /** 身份证号 */
    private String idCard;

    /** 客户电话 */
    private String phone;

    /** 签约时间 */
    private String contractTime;

    /** 认购时间 */
    private String subscribeTime;



    /** 楼号 */
    private String buildId;

    /** 楼层 */
    private String floor;

    /** 房号 */
    private String houseNumber;

    /** 建筑面积 */
    private Double actualBuildArea;

    /** 套内面积 */
    private Double actualHouseArea;

    /** 销售单价 */
    private double salesUnitPrice;

    /** 销售总价 */
    private double salesTotalPrice;


    private Double totalHousePrice;


    /** 执行优惠 */
    private Double discount;

    /** 优惠备注 */
    private String discountRemark;

    /** 签约总价 */
    private Double contractPrice;

    
    /** 合计交款 */
    private Double totalPayment;
    
    /** 欠款金额 */
    private Double debtPriec;
    
    /** 结清状态 */
    private String debtStatus;


}
