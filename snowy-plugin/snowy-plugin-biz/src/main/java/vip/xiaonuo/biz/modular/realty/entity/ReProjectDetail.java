/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.common.pojo.CommonEntity;

import java.math.BigDecimal;

/**
 * 项目详情管理实体
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
@Getter
@Setter
@Table(value = "re_project_detail")
public class ReProjectDetail extends CommonEntity {

    /** ID */
    @Id
    @ApiModelProperty(value = "ID", position = 1)
    private String id;

    /** 项目编号 */
    @ApiModelProperty(value = "项目编号", position = 2)
    private String projectId;

    /** 总套数 */
    @ApiModelProperty(value = "总套数", position = 3)
    private Integer totalHouse;

    /** 总面积 */
    @ApiModelProperty(value = "总面积", position = 4)
    private BigDecimal totalArea;

    /** 已售套数 */
    @ApiModelProperty(value = "已售套数", position = 5)
    private Integer soldHouse;

    /** 已售面积 */
    @ApiModelProperty(value = "已售面积", position = 6)
    private BigDecimal soldArea;

    /** 未售套数 */
    @ApiModelProperty(value = "未售套数", position = 7)
    private Integer unsoldHouse;

    /** 未售面积 */
    @ApiModelProperty(value = "未售面积", position = 8)
    private BigDecimal unsoldArea;

    /** 类型--字典（住宅、商业、储藏室 、车位） */
    @ApiModelProperty(value = "类型--字典（住宅、商业、储藏室 、车位）", position = 9)
    private String type;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息", position = 10)
    private String extJson;

}
