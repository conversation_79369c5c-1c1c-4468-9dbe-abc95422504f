/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReGardenContract;
import vip.xiaonuo.biz.modular.realty.param.ReGardenContractAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenContractEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenContractIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenContractPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReGardenContractService;

/**
 * 园区签约控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 */
@Api(tags = "园区签约控制器")
@Controller
@Valid
public class ReGardenContractController {

    @Inject
    private ReGardenContractService reGardenContractService;

    /**
     * 获取园区签约分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取园区签约分页")
    @SaCheckPermission("/biz/regardencontract/page")
    @Get
    @Mapping("/biz/regardencontract/page")
    public CommonResult<Page<ReGardenContract>> page(ReGardenContractPageParam reGardenContractPageParam) {
        return CommonResult.data(reGardenContractService.page(reGardenContractPageParam));
    }

    /**
     * 添加园区签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("添加园区签约")
    @CommonLog("添加园区签约")
    @SaCheckPermission("/biz/regardencontract/add")
    @Post
    @Mapping("/biz/regardencontract/add")
    public CommonResult<String> add(ReGardenContractAddParam reGardenContractAddParam) {
        reGardenContractService.add(reGardenContractAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑园区签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("编辑园区签约")
    @CommonLog("编辑园区签约")
    @SaCheckPermission("/biz/regardencontract/edit")
    @Post
    @Mapping("/biz/regardencontract/edit")
    public CommonResult<String> edit(ReGardenContractEditParam reGardenContractEditParam) {
        reGardenContractService.edit(reGardenContractEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除园区签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("删除园区签约")
    @CommonLog("删除园区签约")
    @SaCheckPermission("/biz/regardencontract/delete")
    @Post
    @Mapping("/biz/regardencontract/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReGardenContractIdParam> reGardenContractIdParamList) {
        reGardenContractService.delete(reGardenContractIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取园区签约详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取园区签约详情")
    @SaCheckPermission("/biz/regardencontract/detail")
    @Get
    @Mapping("/biz/regardencontract/detail")
    public CommonResult<ReGardenContract> detail(ReGardenContractIdParam reGardenContractIdParam) {
        return CommonResult.data(reGardenContractService.detail(reGardenContractIdParam));
    }
}
