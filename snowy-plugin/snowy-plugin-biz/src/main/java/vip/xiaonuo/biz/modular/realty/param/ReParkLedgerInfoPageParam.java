/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 车位台账信息查询参数
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
@Getter
@Setter
public class ReParkLedgerInfoPageParam {

    /** 当前页 */
    @ApiModelProperty(value = "当前页码")
    private Integer current;

    /** 每页条数 */
    @ApiModelProperty(value = "每页条数")
    private Integer size;

    /** 排序字段 */
    @ApiModelProperty(value = "排序字段，字段驼峰名称，如：userName")
    private String sortField;

    /** 排序方式 */
    @ApiModelProperty(value = "排序方式，升序：ASCEND；降序：DESCEND")
    private String sortOrder;

    /** 关键词 */
    @ApiModelProperty(value = "关键词")
    private String searchKey;

    /** 车位类型 **/
    @ApiModelProperty(value = "车位类型")
    private String type;

    /** 车位状态 **/
    @ApiModelProperty(value = "车位状态")
    private String status;

    /** 客户姓名 **/
    @ApiModelProperty(value = "客户姓名")
    private String name;

    /** 客户身份证号 **/
    @ApiModelProperty(value = "客户身份证号")
    private String idCard;

    /** 签约日期 **/
    @ApiModelProperty(value = "签约日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String contractTime;

    /** 项目id **/
    @ApiModelProperty(value = "项目id")
    private String projectId;

    /** 是否历史 **/
    @ApiModelProperty(value = "是否历史")
    private Boolean isHistory;
    /** 认购时间 */
    @ApiModelProperty(value = "认购时间", position = 11)
    private Date[] subscribeTime;

}
