package vip.xiaonuo.biz.modular.realty.service;

import vip.xiaonuo.biz.modular.realty.entity.ReHouseSalesStatisticsDataInfo;
import vip.xiaonuo.biz.modular.realty.entity.ReHouseStatisticsDataInfo;
import vip.xiaonuo.biz.modular.realty.entity.table.ReHouseStatisticsTableInfo;
import vip.xiaonuo.biz.modular.realty.param.ReQueryByDateParam;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/5 9:10
 */
public interface ReProjectStatisticsService {

    /**
     * 住宅统计信息
     *
     * <AUTHOR>
     * @date 2024/9/5 9:10
     */
    ReHouseStatisticsDataInfo residentialStatistics(String projectId);

    /**
     * 户型统计列表
     *
     * <AUTHOR>
     * @date 2024/9/5 17:07
     */
    List<ReHouseStatisticsTableInfo> houseTypeStatistics(ReQueryByDateParam reQueryByDateParam);

    /**
     * 住宅销售统计
     *
     * <AUTHOR>
     * @date 2024/9/5 17:35
     */
    ReHouseSalesStatisticsDataInfo houseSalesStatistics(ReQueryByDateParam reQueryByDateParam);

    /**
     * 商业和储藏间统计
     *
     * <AUTHOR>
     * @date 2024/9/5 17:52
     */
    ReHouseSalesStatisticsDataInfo businessAndStorageStatistics(String type, ReQueryByDateParam reQueryByDateParam);


    /**
     * 商业和储藏间统计总数
     *
     * <AUTHOR>
     * @date 2024/9/5 17:53
     */
    ReHouseStatisticsDataInfo businessAndStorageStatisticsTotal(String type, String projectId);

    /**
     * 车位统计
     *
     * <AUTHOR>
     * @date 2024/9/5 18:18
     */
    ReHouseSalesStatisticsDataInfo parkingSpaceStatistics(ReQueryByDateParam reQueryByDateParam);


    /**
     * 车位统计总数
     *
     * <AUTHOR>
     * @date 2024/9/5 18:39
     */
    ReHouseStatisticsDataInfo parkingSpaceStatisticsTotal(String projectId);
}
