package vip.xiaonuo.biz.modular.realty.param;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class ReGardenHouseImportParam {


    @ApiModelProperty(value = "房屋类型", position = 2)
    private String houseType;

    /** 房源楼号 */
    @ApiModelProperty(value = "房源楼号", position = 3)
    private String buildId;

    /** 房源楼层 */
    @ApiModelProperty(value = "房源楼层", position = 4)
    @ExcelProperty(value = "房源楼层")
    private Integer floor;

    /** 房源房号 */
    @ApiModelProperty(value = "房源房号", position = 5)
    @ExcelProperty(value = "房源房号")
    private String houseNum;

    /** 房源位置 */
    @ApiModelProperty(value = "房源位置", position = 6)
    @ExcelProperty(value = "房源位置")
    private String houseLocation;

    /** 建筑面积 */
    @ApiModelProperty(value = "建筑面积", position = 7)
    @ExcelProperty(value = "建筑面积")
    private BigDecimal buildArea;

    /** 连廊面积 */
    @ApiModelProperty(value = "连廊面积", position = 8)
    @ExcelProperty(value = "连廊面积")
    private BigDecimal corridorArea;

    /** 项目Id */
    @ApiModelProperty(value = "项目Id", position = 9)
    private String projectId;


}
