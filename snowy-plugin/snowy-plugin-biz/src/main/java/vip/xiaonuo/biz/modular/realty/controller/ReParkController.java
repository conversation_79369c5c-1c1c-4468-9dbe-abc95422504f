/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.noear.solon.annotation.*;
import org.noear.solon.core.handle.Context;
import org.noear.solon.core.handle.UploadedFile;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import org.noear.solon.validation.annotation.Validated;
import vip.xiaonuo.biz.modular.realty.entity.RePark;
import vip.xiaonuo.biz.modular.realty.param.*;
import vip.xiaonuo.biz.modular.realty.service.ReParkService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;

import java.io.IOException;

/**
 * 车位管理控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 */
@Api(tags = "车位管理控制器")
@Controller
@Valid
public class ReParkController {

    @Inject
    private ReParkService reParkService;

    /**
     * 获取车位管理分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取车位管理分页")
    @SaCheckPermission("/biz/repark/page")
    @Get
    @Mapping("/biz/repark/page")
    public CommonResult<Page<RePark>> page(@Validated ReParkPageParam reParkPageParam) {
        return CommonResult.data(reParkService.page(reParkPageParam));
    }

    /**
     * 添加车位管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("添加车位管理")
    @CommonLog("添加车位管理")
    @SaCheckPermission("/biz/repark/add")
    @Post
    @Mapping("/biz/repark/add")
    public CommonResult<String> add(ReParkAddParam reParkAddParam) {
        reParkService.add(reParkAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑车位管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("编辑车位管理")
    @CommonLog("编辑车位管理")
    @SaCheckPermission("/biz/repark/edit")
    @Post
    @Mapping("/biz/repark/edit")
    public CommonResult<String> edit(ReParkEditParam reParkEditParam) {
        reParkService.edit(reParkEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除车位管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("删除车位管理")
    @CommonLog("删除车位管理")
    @SaCheckPermission("/biz/repark/delete")
    @Post
    @Mapping("/biz/repark/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReParkIdParam> reParkIdParamList) {
        reParkService.delete(reParkIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取车位管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取车位管理详情")
    @SaCheckPermission("/biz/repark/detail")
    @Get
    @Mapping("/biz/repark/detail")
    public CommonResult<RePark> detail(ReParkIdParam reParkIdParam) {
        return CommonResult.data(reParkService.detail(reParkIdParam));
    }


    /**
     * 通过excel导入车位信息
     *
     * <AUTHOR>
     * @date 2024/8/22 18:49
     */
    @ApiOperation("通过excel导入车位信息")
    @CommonLog("通过excel导入车位信息")
    @SaCheckPermission("/biz/repark/import")
    @Post
    @Mapping("/biz/repark/import")
    public CommonResult<String> importPark(@ApiParam(value="文件", required = true) UploadedFile file, @ApiParam(value="项目id", required = true) String projectId) {
        reParkService.importPark(file,projectId);
        return CommonResult.ok();
    }

    /**
     * 导出车位信息excel
     *
     * <AUTHOR>
     * @date 2024/8/27 18:35
     */
    @ApiOperation("导出车位信息excel")
    @CommonLog("导出车位信息excel")
    @SaCheckPermission("/biz/repark/export")
    @Get
    @Mapping("/biz/repark/export")
    public void exportPark(Context context) throws IOException {
        reParkService.exportPark(context);
    }

    /**
     * 车位退购
     *
     * <AUTHOR>
     * @date 2024/9/3 10:52
     */
    @ApiOperation("车位退购")
    @CommonLog("车位退购")
    @Post
    @Mapping("/biz/repark/refund")
    public CommonResult<String> refund(@NotEmpty(message = "集合不能为空") CommonValidList<ReParkIdParam> reParkIdParamList) {
        reParkService.refund(reParkIdParamList);
        return CommonResult.ok();
    }

    /**
     * 车位台账导出
     *
     * <AUTHOR>
     * @date 2024/10/12 14:25
     */
    @ApiOperation("车位台账导出")
    @CommonLog("车位台账导出")
    @Get
    @Mapping("/biz/repark/exportLedger")
    public void exportLedger(ReParkPageParam reParkPageParam,Context context) throws IOException {
        reParkService.exportLedger(reParkPageParam,context);
    }


    /**
     *  从excel批量导入车位台账信息
     **/
    @ApiOperation("导入车位台账信息")
    @CommonLog("导入车位台账信息")
    @Post
    @Mapping("/biz/repark/importLeder")
    public CommonResult<JSONObject> importExcel(@ApiParam(value="文件", required = true) UploadedFile file,String projectId) {
        return CommonResult.data(reParkService.importExcel(file,projectId));
    }

}
