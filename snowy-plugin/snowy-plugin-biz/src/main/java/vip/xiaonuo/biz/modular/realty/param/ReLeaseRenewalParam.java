/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商业租赁续签参数
 * 
 * <AUTHOR> 4.0 sonnet
 * @date 2024/12/19
 */
@Getter
@Setter
public class ReLeaseRenewalParam {

    /** 原台账ID（可选，用于将原台账标记为历史） */
    @ApiModelProperty(value = "原台账ID", position = 1)
    private String originalLedgerId;

    /** 房源ID */
    @ApiModelProperty(value = "房源ID", position = 2, required = true)
    private String houseId;

    /** 项目ID */
    @ApiModelProperty(value = "项目ID", position = 3, required = true)
    private String projectId;

    /** 客户姓名 */
    @ApiModelProperty(value = "客户姓名", position = 4, required = true)
    private String customerName;

    /** 客户电话 */
    @ApiModelProperty(value = "客户电话", position = 5)
    private String customerPhone;

    /** 客户身份证 */
    @ApiModelProperty(value = "客户身份证", position = 6)
    private String customerIdCard;

    /** 交款日期（用于重复性检查） */
    @ApiModelProperty(value = "交款日期", position = 7, required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date paymentDate;

    /** 合同签订日期 */
    @ApiModelProperty(value = "合同签订日期", position = 8)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractDate;

    /** 租赁开始时间 */
    @ApiModelProperty(value = "租赁开始时间", position = 9)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date leaseStartTime;

    /** 租赁结束时间 */
    @ApiModelProperty(value = "租赁结束时间", position = 10)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date leaseEndTime;

    /** 租赁期限（月） */
    @ApiModelProperty(value = "租赁期限（月）", position = 11)
    private Integer leaseTerm;

    /** 租赁单价 */
    @ApiModelProperty(value = "租赁单价", position = 12)
    private BigDecimal unitPrice;

    /** 合同总价 */
    @ApiModelProperty(value = "合同总价", position = 13)
    private BigDecimal contractPrice;

    /** 已交款总额 */
    @ApiModelProperty(value = "已交款总额", position = 14)
    private BigDecimal totalPayment;

    /** 面积 */
    @ApiModelProperty(value = "面积", position = 15)
    private BigDecimal area;

    /** 押金 */
    @ApiModelProperty(value = "押金", position = 16)
    private BigDecimal deposit;

    /** 付款方式 */
    @ApiModelProperty(value = "付款方式", position = 17)
    private String paymentMethod;

    /** 免租期 */
    @ApiModelProperty(value = "免租期", position = 18)
    private Integer rentFreePeriod;

    /** 优惠备注 */
    @ApiModelProperty(value = "优惠备注", position = 19)
    private String discountRemark;

    /** 楼栋编号 */
    @ApiModelProperty(value = "楼栋编号", position = 20)
    private String buildCode;

    /** 楼层 */
    @ApiModelProperty(value = "楼层", position = 21)
    private String floor;

    /** 单元 */
    @ApiModelProperty(value = "单元", position = 22)
    private String unit;

    /** 房号 */
    @ApiModelProperty(value = "房号", position = 23)
    private String houseNumber;

    /** 备注 */
    @ApiModelProperty(value = "备注", position = 24)
    private String remark;
}
