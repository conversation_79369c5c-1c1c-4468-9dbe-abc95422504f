/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.ReParkContract;
import vip.xiaonuo.biz.modular.realty.entity.ReParkLedgerInfo;
import vip.xiaonuo.biz.modular.realty.mapper.ReParkContractMapper;
import vip.xiaonuo.biz.modular.realty.param.ReParkContractAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkContractEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkContractIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkContractPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReParkContractService;
import vip.xiaonuo.biz.modular.realty.service.ReParkLedgerInfoService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;

import java.util.List;

/**
 * 车位签约Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
@Component
public class ReParkContractServiceImpl extends ServiceImpl<ReParkContractMapper, ReParkContract> implements ReParkContractService {

    @Inject
    private ReParkLedgerInfoService reParkLedgerInfoService;

    @Override
    public Page<ReParkContract> page(ReParkContractPageParam reParkContractPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if(ObjectUtil.isAllNotEmpty(reParkContractPageParam.getSortField(), reParkContractPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reParkContractPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reParkContractPageParam.getSortField()),reParkContractPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReParkContract::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public String add(ReParkContractAddParam reParkContractAddParam) {
        ReParkContract reParkContract = BeanUtil.toBean(reParkContractAddParam, ReParkContract.class);
        this.save(reParkContract);
        return reParkContract.getId();
    }

    @Tran
    @Override
    public void edit(ReParkContractEditParam reParkContractEditParam) {
        ReParkContract reParkContract = this.queryEntity(reParkContractEditParam.getId());
        BeanUtil.copyProperties(reParkContractEditParam, reParkContract);
        reParkLedgerInfoService.updateChain().set(ReParkLedgerInfo::getContractPrice, reParkContractEditParam.getContractPrice())
                .eq(ReParkLedgerInfo::getId, reParkContractEditParam.getLedgerId()).update();
        this.updateById(reParkContract);
    }

    @Tran
    @Override
    public void delete(List<ReParkContractIdParam> reParkContractIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(reParkContractIdParamList, ReParkContractIdParam::getId));
    }

    @Override
    public ReParkContract detail(ReParkContractIdParam reParkContractIdParam) {
        return this.queryEntity(reParkContractIdParam.getId());
    }

    @Override
    public ReParkContract queryEntity(String id) {
        ReParkContract reParkContract = this.getById(id);
        if(ObjectUtil.isEmpty(reParkContract)) {
            throw new CommonException("车位签约不存在，id值为：{}", id);
        }
        return reParkContract;
    }
}
