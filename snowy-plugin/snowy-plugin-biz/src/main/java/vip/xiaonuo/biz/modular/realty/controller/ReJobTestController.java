package vip.xiaonuo.biz.modular.realty.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.annotation.Controller;
import org.noear.solon.annotation.Inject;
import org.noear.solon.annotation.Mapping;
import org.noear.solon.annotation.Post;
import vip.xiaonuo.biz.modular.realty.job.ReHouseServiceJob;
import vip.xiaonuo.biz.modular.realty.job.ReGardenHouseServiceJob;
import vip.xiaonuo.biz.modular.realty.job.ReGardenHouseEndServiceJob;
import vip.xiaonuo.common.pojo.CommonResult;

/**
 * 房地产定时任务测试控制器
 * 用于开发环境手动测试定时任务功能
 * 
 * <AUTHOR> 4.0 sonnet
 * @date 2024/12/19
 */
@Api(tags = "房地产定时任务测试控制器")
@Controller
@Slf4j
public class ReJobTestController {

    @Inject
    private ReHouseServiceJob reHouseServiceJob;

    @Inject
    private ReGardenHouseServiceJob reGardenHouseServiceJob;

    @Inject
    private ReGardenHouseEndServiceJob reGardenHouseEndServiceJob;

    /**
     * 手动执行房屋租赁到期提醒任务
     */
    @ApiOperation("手动执行房屋租赁到期提醒任务")
    @Post
    @Mapping("/test/job/houseExpireRemind")
    public CommonResult<String> testHouseExpireRemind() {
        try {
            log.info("🧪 开始手动测试房屋租赁到期提醒任务...");
            long startTime = System.currentTimeMillis();
            
            reHouseServiceJob.action();
            
            long endTime = System.currentTimeMillis();
            String message = String.format("✅ 房屋租赁到期提醒任务执行完成，耗时：%d ms", endTime - startTime);
            log.info(message);
            
            return CommonResult.data(message);
        } catch (Exception e) {
            String errorMessage = "❌ 房屋租赁到期提醒任务执行失败：" + e.getMessage();
            log.error(errorMessage, e);
            return CommonResult.error(errorMessage);
        }
    }

    /**
     * 手动执行园区房屋到期提醒任务
     */
    @ApiOperation("手动执行园区房屋到期提醒任务")
    @Post
    @Mapping("/test/job/gardenHouseExpireRemind")
    public CommonResult<String> testGardenHouseExpireRemind() {
        try {
            log.info("🧪 开始手动测试园区房屋到期提醒任务...");
            long startTime = System.currentTimeMillis();
            
            reGardenHouseServiceJob.action();
            
            long endTime = System.currentTimeMillis();
            String message = String.format("✅ 园区房屋到期提醒任务执行完成，耗时：%d ms", endTime - startTime);
            log.info(message);
            
            return CommonResult.data(message);
        } catch (Exception e) {
            String errorMessage = "❌ 园区房屋到期提醒任务执行失败：" + e.getMessage();
            log.error(errorMessage, e);
            return CommonResult.error(errorMessage);
        }
    }

    /**
     * 手动执行园区房屋到期台账历史化任务
     */
    @ApiOperation("手动执行园区房屋到期台账历史化任务")
    @Post
    @Mapping("/test/job/gardenHouseEndService")
    public CommonResult<String> testGardenHouseEndService() {
        try {
            log.info("🧪 开始手动测试园区房屋到期台账历史化任务...");
            long startTime = System.currentTimeMillis();
            
            reGardenHouseEndServiceJob.action();
            
            long endTime = System.currentTimeMillis();
            String message = String.format("✅ 园区房屋到期台账历史化任务执行完成，耗时：%d ms", endTime - startTime);
            log.info(message);
            
            return CommonResult.data(message);
        } catch (Exception e) {
            String errorMessage = "❌ 园区房屋到期台账历史化任务执行失败：" + e.getMessage();
            log.error(errorMessage, e);
            return CommonResult.error(errorMessage);
        }
    }

    /**
     * 执行所有房地产相关定时任务
     */
    @ApiOperation("执行所有房地产相关定时任务")
    @Post
    @Mapping("/test/job/runAllRealtyJobs")
    public CommonResult<String> runAllRealtyJobs() {
        StringBuilder result = new StringBuilder();
        int successCount = 0;
        int totalCount = 3;
        
        try {
            log.info("🧪 开始批量测试所有房地产定时任务...");
            long totalStartTime = System.currentTimeMillis();
            
            // 1. 房屋租赁到期提醒
            try {
                log.info("执行任务 1/3: 房屋租赁到期提醒");
                reHouseServiceJob.action();
                result.append("✅ 房屋租赁到期提醒任务执行成功\n");
                successCount++;
            } catch (Exception e) {
                result.append("❌ 房屋租赁到期提醒任务执行失败: ").append(e.getMessage()).append("\n");
                log.error("房屋租赁到期提醒任务执行失败", e);
            }
            
            // 2. 园区房屋到期提醒
            try {
                log.info("执行任务 2/3: 园区房屋到期提醒");
                reGardenHouseServiceJob.action();
                result.append("✅ 园区房屋到期提醒任务执行成功\n");
                successCount++;
            } catch (Exception e) {
                result.append("❌ 园区房屋到期提醒任务执行失败: ").append(e.getMessage()).append("\n");
                log.error("园区房屋到期提醒任务执行失败", e);
            }
            
            // 3. 园区房屋到期台账历史化
            try {
                log.info("执行任务 3/3: 园区房屋到期台账历史化");
                reGardenHouseEndServiceJob.action();
                result.append("✅ 园区房屋到期台账历史化任务执行成功\n");
                successCount++;
            } catch (Exception e) {
                result.append("❌ 园区房屋到期台账历史化任务执行失败: ").append(e.getMessage()).append("\n");
                log.error("园区房屋到期台账历史化任务执行失败", e);
            }
            
            long totalEndTime = System.currentTimeMillis();
            String summary = String.format("\n📊 执行总结：成功 %d/%d 个任务，总耗时：%d ms", 
                successCount, totalCount, totalEndTime - totalStartTime);
            result.append(summary);
            log.info(summary);
            
            return CommonResult.data(result.toString());
            
        } catch (Exception e) {
            String errorMessage = "❌ 批量执行定时任务失败：" + e.getMessage();
            log.error(errorMessage, e);
            return CommonResult.error(errorMessage);
        }
    }

    /**
     * 获取定时任务状态信息
     */
    @ApiOperation("获取定时任务状态信息")
    @Post
    @Mapping("/test/job/getJobStatus")
    public CommonResult<String> getJobStatus() {
        StringBuilder status = new StringBuilder();
        
        status.append("🔧 房地产定时任务状态检查\n\n");
        status.append("📋 已配置的定时任务：\n");
        status.append("1. ReHouseServiceJob - 房屋租赁到期提醒\n");
        status.append("2. ReGardenHouseServiceJob - 园区房屋到期提醒\n");
        status.append("3. ReGardenHouseEndServiceJob - 园区房屋到期台账历史化\n\n");
        
        status.append("⚙️ 配置建议：\n");
        status.append("- 执行 realty_job_config.sql 脚本添加定时任务配置\n");
        status.append("- 在管理后台的任务调度菜单中启动相应任务\n");
        status.append("- 建议在测试环境先验证任务执行效果\n\n");
        
        status.append("🕐 推荐执行时间：\n");
        status.append("- 房屋租赁到期提醒：每天上午9点\n");
        status.append("- 园区房屋到期提醒：每天上午9点30分\n");
        status.append("- 台账历史化：每天凌晨1点\n");
        
        return CommonResult.data(status.toString());
    }
}
