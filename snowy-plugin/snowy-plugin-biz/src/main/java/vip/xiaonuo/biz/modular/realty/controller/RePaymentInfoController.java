/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.RePaymentInfo;
import vip.xiaonuo.biz.modular.realty.param.RePaymentInfoAddParam;
import vip.xiaonuo.biz.modular.realty.param.RePaymentInfoEditParam;
import vip.xiaonuo.biz.modular.realty.param.RePaymentInfoIdParam;
import vip.xiaonuo.biz.modular.realty.param.RePaymentInfoPageParam;
import vip.xiaonuo.biz.modular.realty.service.RePaymentInfoService;

/**
 * 交款信息控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 */
@Api(tags = "交款信息控制器")
@Controller
@Valid
public class RePaymentInfoController {

    @Inject
    private RePaymentInfoService rePaymentInfoService;

    /**
     * 获取交款信息分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取交款信息分页")
    @SaCheckPermission("/biz/repaymentinfo/page")
    @Get
    @Mapping("/biz/repaymentinfo/page")
    public CommonResult<Page<RePaymentInfo>> page(RePaymentInfoPageParam rePaymentInfoPageParam) {
        return CommonResult.data(rePaymentInfoService.page(rePaymentInfoPageParam));
    }

    /**
     * 添加交款信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("添加交款信息")
    @CommonLog("添加交款信息")
    @SaCheckPermission("/biz/repaymentinfo/add")
    @Post
    @Mapping("/biz/repaymentinfo/add")
    public CommonResult<String> add(RePaymentInfoAddParam rePaymentInfoAddParam) {
        return CommonResult.data(rePaymentInfoService.add(rePaymentInfoAddParam));
    }

    /**
     * 编辑交款信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("编辑交款信息")
    @CommonLog("编辑交款信息")
    @SaCheckPermission("/biz/repaymentinfo/edit")
    @Post
    @Mapping("/biz/repaymentinfo/edit")
    public CommonResult<String> edit(RePaymentInfoEditParam rePaymentInfoEditParam) {
        rePaymentInfoService.edit(rePaymentInfoEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除交款信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("删除交款信息")
    @CommonLog("删除交款信息")
    @SaCheckPermission("/biz/repaymentinfo/delete")
    @Post
    @Mapping("/biz/repaymentinfo/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<RePaymentInfoIdParam> rePaymentInfoIdParamList) {
        rePaymentInfoService.delete(rePaymentInfoIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取交款信息详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取交款信息详情")
    @SaCheckPermission("/biz/repaymentinfo/detail")
    @Get
    @Mapping("/biz/repaymentinfo/detail")
    public CommonResult<RePaymentInfo> detail(RePaymentInfoIdParam rePaymentInfoIdParam) {
        return CommonResult.data(rePaymentInfoService.detail(rePaymentInfoIdParam));
    }
}
