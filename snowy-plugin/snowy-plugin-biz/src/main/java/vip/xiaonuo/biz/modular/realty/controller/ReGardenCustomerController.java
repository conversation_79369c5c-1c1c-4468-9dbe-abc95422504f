/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReGardenCustomer;
import vip.xiaonuo.biz.modular.realty.param.ReGardenCustomerAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenCustomerEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenCustomerIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenCustomerPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReGardenCustomerService;

/**
 * 园区客户管理控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 */
@Api(tags = "园区客户管理控制器")
@Controller
@Valid
public class ReGardenCustomerController {

    @Inject
    private ReGardenCustomerService reGardenCustomerService;

    /**
     * 获取园区客户管理分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取园区客户管理分页")
    @SaCheckPermission("/biz/regardencustomer/page")
    @Get
    @Mapping("/biz/regardencustomer/page")
    public CommonResult<Page<ReGardenCustomer>> page(ReGardenCustomerPageParam reGardenCustomerPageParam) {
        return CommonResult.data(reGardenCustomerService.page(reGardenCustomerPageParam));
    }

    /**
     * 添加园区客户管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("添加园区客户管理")
    @CommonLog("添加园区客户管理")
    @SaCheckPermission("/biz/regardencustomer/add")
    @Post
    @Mapping("/biz/regardencustomer/add")
    public CommonResult<String> add(ReGardenCustomerAddParam reGardenCustomerAddParam) {
        reGardenCustomerService.add(reGardenCustomerAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑园区客户管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("编辑园区客户管理")
    @CommonLog("编辑园区客户管理")
    @SaCheckPermission("/biz/regardencustomer/edit")
    @Post
    @Mapping("/biz/regardencustomer/edit")
    public CommonResult<String> edit(ReGardenCustomerEditParam reGardenCustomerEditParam) {
        reGardenCustomerService.edit(reGardenCustomerEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除园区客户管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("删除园区客户管理")
    @CommonLog("删除园区客户管理")
    @SaCheckPermission("/biz/regardencustomer/delete")
    @Post
    @Mapping("/biz/regardencustomer/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReGardenCustomerIdParam> reGardenCustomerIdParamList) {
        reGardenCustomerService.delete(reGardenCustomerIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取园区客户管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取园区客户管理详情")
    @SaCheckPermission("/biz/regardencustomer/detail")
    @Get
    @Mapping("/biz/regardencustomer/detail")
    public CommonResult<ReGardenCustomer> detail(ReGardenCustomerIdParam reGardenCustomerIdParam) {
        return CommonResult.data(reGardenCustomerService.detail(reGardenCustomerIdParam));
    }
}
