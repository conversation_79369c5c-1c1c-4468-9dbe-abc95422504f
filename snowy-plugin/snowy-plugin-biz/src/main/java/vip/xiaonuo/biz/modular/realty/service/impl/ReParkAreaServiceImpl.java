/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.RePark;
import vip.xiaonuo.biz.modular.realty.entity.ReParkArea;
import vip.xiaonuo.biz.modular.realty.mapper.ReParkAreaMapper;
import vip.xiaonuo.biz.modular.realty.param.ReParkAreaAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkAreaEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkAreaIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkAreaPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReParkAreaService;
import vip.xiaonuo.biz.modular.realty.service.ReParkService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;

import java.util.List;

/**
 * 车位区域管理Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
@Component
public class ReParkAreaServiceImpl extends ServiceImpl<ReParkAreaMapper, ReParkArea> implements ReParkAreaService {

    @Inject
    private ReParkService parkService;


    @Override
    public Page<ReParkArea> page(ReParkAreaPageParam reParkAreaPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq(ReParkArea::getProjectId, reParkAreaPageParam.getProjectId(), StringUtil.isNotBlank(reParkAreaPageParam.getProjectId()));
        if(ObjectUtil.isAllNotEmpty(reParkAreaPageParam.getSortField(), reParkAreaPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reParkAreaPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reParkAreaPageParam.getSortField()),reParkAreaPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReParkArea::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public void add(ReParkAreaAddParam reParkAreaAddParam) {
        ReParkArea reParkArea = BeanUtil.toBean(reParkAreaAddParam, ReParkArea.class);
        this.save(reParkArea);
    }

    @Tran
    @Override
    public void edit(ReParkAreaEditParam reParkAreaEditParam) {
        ReParkArea reParkArea = this.queryEntity(reParkAreaEditParam.getId());
        BeanUtil.copyProperties(reParkAreaEditParam, reParkArea);
        this.updateById(reParkArea);
    }

    @Tran
    @Override
    public void delete(List<ReParkAreaIdParam> reParkAreaIdParamList) {
        List<String> list = CollStreamUtil.toList(reParkAreaIdParamList, ReParkAreaIdParam::getId);
        // 执行删除
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in(RePark::getAreaId, list);
        List<RePark> parks = parkService.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(parks)) {
            throw new CommonException("有车位未删除,不允许删除该区域");
        }
        this.removeByIds(list);
    }

    @Override
    public ReParkArea detail(ReParkAreaIdParam reParkAreaIdParam) {
        return this.queryEntity(reParkAreaIdParam.getId());
    }

    @Override
    public ReParkArea queryEntity(String id) {
        ReParkArea reParkArea = this.getById(id);
        if(ObjectUtil.isEmpty(reParkArea)) {
            throw new CommonException("车位区域管理不存在，id值为：{}", id);
        }
        return reParkArea;
    }
}
