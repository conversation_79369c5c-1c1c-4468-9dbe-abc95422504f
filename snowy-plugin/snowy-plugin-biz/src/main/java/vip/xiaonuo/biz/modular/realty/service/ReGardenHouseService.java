/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service;

import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import org.noear.solon.core.handle.Context;
import org.noear.solon.core.handle.UploadedFile;
import vip.xiaonuo.biz.modular.realty.entity.ReGardenHouse;
import vip.xiaonuo.biz.modular.realty.param.ReGardenHouseAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenHouseEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenHouseIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenHousePageParam;

import java.util.List;

/**
 * 园区房屋管理Service接口
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
public interface ReGardenHouseService extends IService<ReGardenHouse> {

    /**
     * 获取园区房屋管理分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    Page<ReGardenHouse> page(ReGardenHousePageParam reGardenHousePageParam);

    /**
     * 添加园区房屋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void add(ReGardenHouseAddParam reGardenHouseAddParam);

    /**
     * 编辑园区房屋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void edit(ReGardenHouseEditParam reGardenHouseEditParam);

    /**
     * 删除园区房屋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void delete(List<ReGardenHouseIdParam> reGardenHouseIdParamList);

    /**
     * 获取园区房屋管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    ReGardenHouse detail(ReGardenHouseIdParam reGardenHouseIdParam);

    /**
     * 获取园区房屋管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     **/
    ReGardenHouse queryEntity(String id);

    JSONObject importExcel(UploadedFile file,ReGardenHouseIdParam reGardenHouseIdParam);

    JSONObject soldAndUnsoldArea(ReGardenHouseIdParam reGardenHouseIdParam);

    void downloadTemplate(Context context);
}
