/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReContractInfo;
import vip.xiaonuo.biz.modular.realty.param.ReContractInfoAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReContractInfoEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReContractInfoIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReContractInfoPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReContractInfoService;

/**
 * 合同信息控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 */
@Api(tags = "合同信息控制器")
@Controller
@Valid
public class ReContractInfoController {

    @Inject
    private ReContractInfoService reContractInfoService;

    /**
     * 获取合同信息分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取合同信息分页")
    @SaCheckPermission("/biz/recontractinfo/page")
    @Get
    @Mapping("/biz/recontractinfo/page")
    public CommonResult<Page<ReContractInfo>> page(ReContractInfoPageParam reContractInfoPageParam) {
        Page<ReContractInfo> page = reContractInfoService.page(reContractInfoPageParam);
        return CommonResult.data(page);
    }

    /**
     * 添加合同信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("添加合同信息")
    @CommonLog("添加合同信息")
    @SaCheckPermission("/biz/recontractinfo/add")
    @Post
    @Mapping("/biz/recontractinfo/add")
    public CommonResult<String> add(ReContractInfoAddParam reContractInfoAddParam) {
        reContractInfoService.add(reContractInfoAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑合同信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("编辑合同信息")
    @CommonLog("编辑合同信息")
    @SaCheckPermission("/biz/recontractinfo/edit")
    @Post
    @Mapping("/biz/recontractinfo/edit")
    public CommonResult<String> edit(ReContractInfoEditParam reContractInfoEditParam) {
        reContractInfoService.edit(reContractInfoEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除合同信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("删除合同信息")
    @CommonLog("删除合同信息")
    @SaCheckPermission("/biz/recontractinfo/delete")
    @Post
    @Mapping("/biz/recontractinfo/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReContractInfoIdParam> reContractInfoIdParamList) {
        reContractInfoService.delete(reContractInfoIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取合同信息详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取合同信息详情")
    @SaCheckPermission("/biz/recontractinfo/detail")
    @Get
    @Mapping("/biz/recontractinfo/detail")
    public CommonResult<ReContractInfo> detail(ReContractInfoIdParam reContractInfoIdParam) {
        return CommonResult.data(reContractInfoService.detail(reContractInfoIdParam));
    }
}
