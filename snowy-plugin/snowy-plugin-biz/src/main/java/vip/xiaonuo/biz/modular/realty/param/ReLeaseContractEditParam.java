/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import org.noear.solon.validation.annotation.NotBlank;
import org.noear.solon.validation.annotation.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 租赁签约编辑参数
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
@Getter
@Setter
public class ReLeaseContractEditParam {

    /** ID */
    @ApiModelProperty(value = "ID", required = true, position = 1)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 租赁保证金 */
    @ApiModelProperty(value = "租赁保证金", position = 2)
    private BigDecimal leaseDeposit;

    /** 租赁起始时间 */
    @ApiModelProperty(value = "租赁起始时间", position = 3)
    private Date leaseStartTime;

    /** 租赁结束时间 */
    @ApiModelProperty(value = "租赁结束时间", position = 4)
    private Date leaseEndTime;

    /** 到期提醒 */
    @ApiModelProperty(value = "到期提醒", position = 5)
    private Date expireRemind;

    /** 租期 */
    @ApiModelProperty(value = "租期", position = 6)
    private Integer leaseTerm;

    /** 免租期 */
    @ApiModelProperty(value = "免租期", position = 7)
    private Integer rentFreePeriod;

    /** 执行优惠 */
    @ApiModelProperty(value = "执行优惠", position = 8)
    private BigDecimal discount;

    /** 优惠类型--字典（直减、折扣） */
    @ApiModelProperty(value = "优惠类型--字典（直减、折扣）", position = 9)
    private String discountType;

    /** 优惠备注 */
    @ApiModelProperty(value = "优惠备注", position = 10)
    private String discountRemark;

    /** 租赁总价--计算 租赁单价*真实租期*面积 */
    @ApiModelProperty(value = "租赁总价--计算 租赁单价*真实租期*面积", position = 11)
    private BigDecimal leaseTotalPrice;

    /** 租赁单价--计算 租赁总价/租期/面积 */
    @ApiModelProperty(value = "租赁单价--计算 租赁总价/租期/面积", position = 12)
    private BigDecimal leaseUnitPrice;

    /** 付款形式--字典（一次性付款、全款分期、贷款） */
    @ApiModelProperty(value = "付款形式--字典（一次性付款、全款分期、贷款）", position = 13)
    private String paymentMethod;

    /** 房款合计 */
    @ApiModelProperty(value = "房款合计", position = 14)
    private BigDecimal totalHousePrice;

    /** 首付款--付款形式为贷款 */
    @ApiModelProperty(value = "首付款--付款形式为贷款", position = 15)
    private BigDecimal firstPayment;

    /** 贷款--付款形式为贷款 */
    @ApiModelProperty(value = "贷款--付款形式为贷款", position = 16)
    private BigDecimal loan;

    /** 贷款银行--付款形式为贷款 */
    @ApiModelProperty(value = "贷款银行--付款形式为贷款", position = 17)
    private String loanBank;

    /** 办理贷款时间--付款形式为贷款 */
    @ApiModelProperty(value = "办理贷款时间--付款形式为贷款", position = 18)
    private Date loanTime;

    /** 贷款回款时间--付款形式为贷款 */
    @ApiModelProperty(value = "贷款回款时间--付款形式为贷款", position = 19)
    private Date loanReturnTime;

    /** 定金--付款形式为全款分期 */
    @ApiModelProperty(value = "定金--付款形式为全款分期", position = 20)
    private BigDecimal earnestMoney;

    /** 台账id */
    @ApiModelProperty(value = "台账id", position = 21)
    private String ledgerId;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息", position = 22)
    private String extJson;

    @ApiModelProperty(value = "分期金额", position = 21)
    private BigDecimal installmentAmount;

}
