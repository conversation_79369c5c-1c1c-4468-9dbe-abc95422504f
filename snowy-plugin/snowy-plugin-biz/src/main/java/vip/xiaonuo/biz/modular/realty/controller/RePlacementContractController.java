/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.RePlacementContract;
import vip.xiaonuo.biz.modular.realty.param.RePlacementContractAddParam;
import vip.xiaonuo.biz.modular.realty.param.RePlacementContractEditParam;
import vip.xiaonuo.biz.modular.realty.param.RePlacementContractIdParam;
import vip.xiaonuo.biz.modular.realty.param.RePlacementContractPageParam;
import vip.xiaonuo.biz.modular.realty.service.RePlacementContractService;

/**
 * 安置签约控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 */
@Api(tags = "安置签约控制器")
@Controller
@Valid
public class RePlacementContractController {

    @Inject
    private RePlacementContractService rePlacementContractService;

    /**
     * 获取安置签约分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取安置签约分页")
    @SaCheckPermission("/biz/replacementcontract/page")
    @Get
    @Mapping("/biz/replacementcontract/page")
    public CommonResult<Page<RePlacementContract>> page(RePlacementContractPageParam rePlacementContractPageParam) {
        return CommonResult.data(rePlacementContractService.page(rePlacementContractPageParam));
    }

    /**
     * 添加安置签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("添加安置签约")
    @CommonLog("添加安置签约")
    @SaCheckPermission("/biz/replacementcontract/add")
    @Post
    @Mapping("/biz/replacementcontract/add")
    public CommonResult<String> add(RePlacementContractAddParam rePlacementContractAddParam) {
        rePlacementContractService.add(rePlacementContractAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑安置签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("编辑安置签约")
    @CommonLog("编辑安置签约")
    @SaCheckPermission("/biz/replacementcontract/edit")
    @Post
    @Mapping("/biz/replacementcontract/edit")
    public CommonResult<String> edit(RePlacementContractEditParam rePlacementContractEditParam) {
        rePlacementContractService.edit(rePlacementContractEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除安置签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("删除安置签约")
    @CommonLog("删除安置签约")
    @SaCheckPermission("/biz/replacementcontract/delete")
    @Post
    @Mapping("/biz/replacementcontract/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<RePlacementContractIdParam> rePlacementContractIdParamList) {
        rePlacementContractService.delete(rePlacementContractIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取安置签约详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取安置签约详情")
    @SaCheckPermission("/biz/replacementcontract/detail")
    @Get
    @Mapping("/biz/replacementcontract/detail")
    public CommonResult<RePlacementContract> detail(RePlacementContractIdParam rePlacementContractIdParam) {
        return CommonResult.data(rePlacementContractService.detail(rePlacementContractIdParam));
    }
}
