package vip.xiaonuo.biz.modular.realty.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 通用查询条件
 *
 * <AUTHOR>
 * @date 2024/9/5 17:09
 */
@Getter
@Setter
public class ReQueryByDateParam {

    /**
     * 开始时间--日
     */
    @ApiModelProperty(value = "开始时间--日", position = 1)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String startDateDay;

    /**
     * 结束时间--日
     */
    @ApiModelProperty(value = "结束时间--日", position = 2)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String endDateDay;

    /**
     * 开始时间--时分秒
     */
    @ApiModelProperty(value = "开始时间--时分秒", position = 3)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String startDateTime;

    /**
     * 结束时间--时分秒
     */
    @ApiModelProperty(value = "结束时间--时分秒", position = 4)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String endDateTime;

    /**
     * 开始时间--时分
     */
    @ApiModelProperty(value = "开始时间--时分", position = 5)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private String startDateTimeMinute;

    /**
     * 结束时间--时分
     */
    @ApiModelProperty(value = "结束时间--时分", position = 6)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private String endDateTimeMinute;


    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id", position = 7, required = true)
    private String projectId;


}
