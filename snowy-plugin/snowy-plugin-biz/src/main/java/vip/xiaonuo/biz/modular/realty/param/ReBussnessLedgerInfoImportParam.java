package vip.xiaonuo.biz.modular.realty.param;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@ToString
public class ReBussnessLedgerInfoImportParam {

    @ExcelProperty("序号")
    private String serialNumber;

    @ExcelProperty("客户姓名")
    private String customerName;

    @ExcelProperty("身份证号码")
    private String idCardNumber;

    @ExcelProperty("共有人")
    private String coOwner;

    @ExcelProperty("共有人身份证号码")
    private String coOwnerIdCardNumber;

    @ExcelProperty("电话1")
    private String phone1;

    @ExcelProperty("电话2")
    private String phone2;

    @ExcelProperty("地址")
    private String address;

    @ExcelProperty("认购时间")
    @DateTimeFormat("yyyy/MM/dd")
    private Date subscriptionTime;

    @ExcelProperty("楼号")
    private String buildingNumber;

    @ExcelProperty("层数")
    private String floorNumber;

    @ExcelProperty("房号")
    private String roomNumber;

    @ExcelProperty("房源位置")
    private String houseLocation;

    @ExcelProperty("面积")
    private String area;

    @ExcelProperty("签约日期")
    @DateTimeFormat("yyyy/MM/dd")
    private Date contractDate;

    @ExcelProperty("付款方式")
    private String paymentMethod;

    @ExcelProperty("表单价")
    private String unitPrice;

    @ExcelProperty("表总价")
    private String totalPrice;

    @ExcelProperty("执行优惠")
    private String discount;

    @ExcelProperty("签约总价")
    private String contractTotalPrice;

    @ExcelProperty("签约单价")
    private String contractUnitPrice;

    @ExcelProperty("首付")
    private String downPayment;

    @ExcelProperty("贷款")
    private String loanAmount;

    @ExcelProperty("维修基金")
    private String maintenanceFund;

    @ExcelProperty("房款合计")
    private String totalHousePayment;

    @ExcelProperty("定金")
    private String deposit;

    @ExcelProperty("定金交款时间")
    @DateTimeFormat("yyyy/MM/dd")
    private Date depositPaymentTime;

    @ExcelProperty("第二次交款")
    private String secondPayment;

    @ExcelProperty("第二次交款时间")
    @DateTimeFormat("yyyy/MM/dd")
    private Date secondPaymentTime;

    @ExcelProperty("第三次交款")
    private String thirdPayment;

    @ExcelProperty("第三次交款时间")
    @DateTimeFormat("yyyy/MM/dd")
    private Date thirdPaymentTime;

    @ExcelProperty("第四次交款")
    private String fourthPayment;

    @ExcelProperty("第四次交款时间")
    @DateTimeFormat("yyyy/MM/dd")
    private Date fourthPaymentTime;

    @ExcelProperty("第五次交款")
    private String fivePayment;

    @ExcelProperty("第五次交款时间")
    @DateTimeFormat("yyyy/MM/dd")
    private Date fivePaymentTime;

    @ExcelProperty("贷款回款")
    private String loanRepayment;

    @ExcelProperty("维修基金(交)")
    private String maintenanceFee;

    @ExcelProperty("合计交款")
    private String totalPayment;

    @ExcelProperty("办理贷款时间")
    @DateTimeFormat("yyyy/MM/dd")
    private Date loanProcessingTime;

    @ExcelProperty("贷款回款时间")
    @DateTimeFormat("yyyy/MM/dd")
    private Date loanRepaymentTime;

    @ExcelProperty("维修基金交款时间")
    @DateTimeFormat("yyyy/MM/dd")
    private Date maintenanceFundPaymentTime;

    @ExcelProperty("欠款情况")
    private String debtSituation;

    @ExcelProperty("结清状态")
    private String settlementStatus;

    @ExcelProperty("发票")
    private String invoice;

    @ExcelProperty("合同编号")
    private String contractNumber;


    @ExcelProperty("置业顾问")
    private String consultant;

    @ExcelProperty("备注")
    private String remarks;
}
