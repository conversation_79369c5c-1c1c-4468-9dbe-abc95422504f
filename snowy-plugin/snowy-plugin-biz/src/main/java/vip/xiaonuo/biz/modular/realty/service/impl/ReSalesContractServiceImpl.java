/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.ReHouse;
import vip.xiaonuo.biz.modular.realty.entity.ReLedgerInfo;
import vip.xiaonuo.biz.modular.realty.entity.ReSalesContract;
import vip.xiaonuo.biz.modular.realty.mapper.ReSalesContractMapper;
import vip.xiaonuo.biz.modular.realty.param.ReSalesContractAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReSalesContractEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReSalesContractIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReSalesContractPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReHouseService;
import vip.xiaonuo.biz.modular.realty.service.ReLedgerInfoService;
import vip.xiaonuo.biz.modular.realty.service.ReSalesContractService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;

import java.util.List;

/**
 * 销售签约Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
@Component
public class ReSalesContractServiceImpl extends ServiceImpl<ReSalesContractMapper, ReSalesContract> implements ReSalesContractService {

    @Inject
    private ReHouseService reHouseService;
    @Inject
    private ReLedgerInfoService reLedgerInfoService;

    @Override
    public Page<ReSalesContract> page(ReSalesContractPageParam reSalesContractPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if(ObjectUtil.isAllNotEmpty(reSalesContractPageParam.getSortField(), reSalesContractPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reSalesContractPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reSalesContractPageParam.getSortField()),reSalesContractPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReSalesContract::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public String add(ReSalesContractAddParam reSalesContractAddParam) {
        ReSalesContract reSalesContract = BeanUtil.toBean(reSalesContractAddParam, ReSalesContract.class);
        this.save(reSalesContract);
        return reSalesContract.getId();
    }

    @Tran
    @Override
    public void edit(ReSalesContractEditParam reSalesContractEditParam) {
        ReSalesContract reSalesContract = this.queryEntity(reSalesContractEditParam.getId());
        // 通过台账id查询房屋id
        ReLedgerInfo byId = reLedgerInfoService.getById(reSalesContract.getLedgerId());
        if(null!= byId){
            reHouseService.updateChain().set(ReHouse::getSalesTotalPrice,reSalesContractEditParam.getContractPrice()).set(ReHouse::getSalesUnitPrice,reSalesContractEditParam.getContractUnitPrice()).eq(ReHouse::getId,byId.getHouseId()).update();
        }
        BeanUtil.copyProperties(reSalesContractEditParam, reSalesContract);
        this.updateById(reSalesContract);
    }

    @Tran
    @Override
    public void delete(List<ReSalesContractIdParam> reSalesContractIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(reSalesContractIdParamList, ReSalesContractIdParam::getId));
    }

    @Override
    public ReSalesContract detail(ReSalesContractIdParam reSalesContractIdParam) {
        return this.queryEntity(reSalesContractIdParam.getId());
    }

    @Override
    public ReSalesContract queryEntity(String id) {
        ReSalesContract reSalesContract = this.getById(id);
        if(ObjectUtil.isEmpty(reSalesContract)) {
            throw new CommonException("销售签约不存在，id值为：{}", id);
        }
        return reSalesContract;
    }
}
