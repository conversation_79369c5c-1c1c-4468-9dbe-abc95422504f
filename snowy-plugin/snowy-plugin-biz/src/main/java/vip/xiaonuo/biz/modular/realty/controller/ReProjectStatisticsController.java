package vip.xiaonuo.biz.modular.realty.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.biz.modular.realty.entity.ReHouseSalesStatisticsDataInfo;
import vip.xiaonuo.biz.modular.realty.entity.ReHouseStatisticsDataInfo;
import vip.xiaonuo.biz.modular.realty.entity.table.ReHouseStatisticsTableInfo;
import vip.xiaonuo.biz.modular.realty.param.ReQueryByDateParam;
import vip.xiaonuo.biz.modular.realty.service.ReProjectStatisticsService;
import vip.xiaonuo.common.pojo.CommonResult;

import java.util.List;

/**
 * 项目统计控制器
 *
 * <AUTHOR>
 * @date 2024/8/30 18:28
 */
@Api(tags = "项目统计控制器")
@Controller
@Valid
public class ReProjectStatisticsController {

    @Inject
    private ReProjectStatisticsService reProjectStatisticsService;


    /**
     * 住宅统计信息
     *
     * <AUTHOR>
     * @date 2024/9/5 9:10
     */
    @ApiOperation("住宅统计信息")
    @Mapping("/biz/reproject/residentialStatistics")
    @Get
    public CommonResult<ReHouseStatisticsDataInfo> residentialStatistics(String projectId) {
        // 住宅统计信息
        ReHouseStatisticsDataInfo reHouseStatisticsDataInfo = reProjectStatisticsService.residentialStatistics(projectId);
        return CommonResult.data(reHouseStatisticsDataInfo);
    }

    /**
     * 住宅户型统计列表
     *
     * <AUTHOR>
     * @date 2024/9/5 17:07
     */
    @ApiOperation("住宅户型统计列表")
    @Mapping("/biz/reproject/houseTypeStatistics")
    @Post
    public CommonResult<List<ReHouseStatisticsTableInfo>> houseTypeStatistics(@Body ReQueryByDateParam reQueryByDateParam) {
        // 户型统计列表
        List<ReHouseStatisticsTableInfo> reHouseStatisticsDataInfo = reProjectStatisticsService.houseTypeStatistics(reQueryByDateParam);
        return CommonResult.data(reHouseStatisticsDataInfo);
    }

    /**
     * 住宅销售统计
     *
     * <AUTHOR>
     * @date 2024/9/5 17:35
     */
    @ApiOperation("住宅销售统计")
    @Mapping("/biz/reproject/houseSalesStatistics")
    @Post
    public CommonResult<ReHouseSalesStatisticsDataInfo> houseSalesStatistics(@Body ReQueryByDateParam reQueryByDateParam) {
        // 户型统计列表
        ReHouseSalesStatisticsDataInfo reHouseStatisticsDataInfo = reProjectStatisticsService.houseSalesStatistics(reQueryByDateParam);
        return CommonResult.data(reHouseStatisticsDataInfo);
    }

    /**
     * 商业和储藏间统计
     *
     * <AUTHOR>
     * @date 2024/9/5 17:52
     */
    @ApiOperation("商业和储藏间统计")
    @Mapping("/biz/reproject/businessAndStorageStatistics/{type}")
    @Post
    public CommonResult<ReHouseSalesStatisticsDataInfo> businessAndStorageStatistics(@Path String type, @Body ReQueryByDateParam reQueryByDateParam) {
        // 户型统计列表
        ReHouseSalesStatisticsDataInfo reHouseStatisticsDataInfo = reProjectStatisticsService.businessAndStorageStatistics(type, reQueryByDateParam);
        return CommonResult.data(reHouseStatisticsDataInfo);
    }

    /**
     * 商业和储藏间统计总数
     *
     * <AUTHOR>
     * @date 2024/9/5 17:53
     */
    @ApiOperation("商业和储藏间统计总数")
    @Mapping("/biz/reproject/businessAndStorageStatisticsTotal/{type}")
    @Get
    public CommonResult<ReHouseStatisticsDataInfo> businessAndStorageStatisticsTotal(@Path String type, String projectId) {
        // 户型统计列表
        ReHouseStatisticsDataInfo reHouseStatisticsDataInfo = reProjectStatisticsService.businessAndStorageStatisticsTotal(type, projectId);
        return CommonResult.data(reHouseStatisticsDataInfo);
    }

    /**
     * 车位统计
     *
     * <AUTHOR>
     * @date 2024/9/5 18:18
     */
    @ApiOperation("车位统计")
    @Mapping("/biz/reproject/parkingSpaceStatistics")
    @Post
    public CommonResult<ReHouseSalesStatisticsDataInfo> parkingSpaceStatistics(@Body ReQueryByDateParam reQueryByDateParam) {
        // 户型统计列表
        ReHouseSalesStatisticsDataInfo reHouseStatisticsDataInfo = reProjectStatisticsService.parkingSpaceStatistics(reQueryByDateParam);
        return CommonResult.data(reHouseStatisticsDataInfo);
    }

    /**
     * 车位统计总数
     *
     * <AUTHOR>
     * @date 2024/9/5 18:39
     */
    @ApiOperation("车位统计总数")
    @Mapping("/biz/reproject/parkingSpaceStatisticsTotal")
    @Get
    public CommonResult<ReHouseStatisticsDataInfo> parkingSpaceStatisticsTotal(String projectId) {
        ReHouseStatisticsDataInfo reHouseStatisticsDataInfo = reProjectStatisticsService.parkingSpaceStatisticsTotal(projectId);
        return CommonResult.data(reHouseStatisticsDataInfo);
    }


}
