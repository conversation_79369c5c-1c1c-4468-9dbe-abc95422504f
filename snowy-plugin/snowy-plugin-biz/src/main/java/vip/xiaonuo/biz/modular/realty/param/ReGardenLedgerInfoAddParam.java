/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import org.noear.solon.validation.annotation.NotBlank;
import org.noear.solon.validation.annotation.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 园区房屋台账信息添加参数
 *
 * <AUTHOR>
 * @date  2024/08/28 15:32
 **/
@Getter
@Setter
public class ReGardenLedgerInfoAddParam {

    /** 企业联络人 */
    @ApiModelProperty(value = "企业联络人", position = 2)
    private String name;

    /** 入住企业 */
    @ApiModelProperty(value = "入住企业", position = 3)
    private String enterprise;

    /** 身份证号 */
    @ApiModelProperty(value = "身份证号", position = 4)
    private String idCard;

    /** 客户电话 */
    @ApiModelProperty(value = "客户电话", position = 5)
    private String phone;

    /** 楼号 */
    @ApiModelProperty(value = "楼号", position = 6)
    private String buildCode;

    /** 楼层 */
    @ApiModelProperty(value = "楼层", position = 7)
    private Integer floor;

    /** 房号 */
    @ApiModelProperty(value = "房号", position = 8)
    private String houseNumber;

    /** 面积 */
    @ApiModelProperty(value = "面积", position = 9)
    private BigDecimal area;

    /** 签约类型--字典（销售、租赁、安置） */
    @ApiModelProperty(value = "签约类型--字典（销售、租赁、安置）", position = 10)
    private String contractType;

    /** 成交单价 */
    @ApiModelProperty(value = "成交单价", position = 11)
    private BigDecimal dealUnitPrice;

    /** 签约日期 */
    @ApiModelProperty(value = "签约日期", position = 12)
    private Date contractTime;

    /** 签约价 */
    @ApiModelProperty(value = "签约价", position = 13)
    private BigDecimal contractPrice;

    /** 合计交款 */
    @ApiModelProperty(value = "合计交款", position = 14)
    private BigDecimal totalPayment;

    /** 房屋id */
    @ApiModelProperty(value = "房屋id", position = 15)
    private String houseId;

    /** 是否历史 */
    @ApiModelProperty(value = "是否历史", position = 16)
    private Boolean isHistory;

    /** 项目id */
    @ApiModelProperty(value = "项目id", position = 17)
    private String projectId;

    /** 房屋类型--字典（住宅、商业、储藏间） */
    @ApiModelProperty(value = "房屋类型--字典（住宅、商业、储藏间）", position = 18)
    private String houseType;

    /** 房屋状态--字典（待售、售出-未结、售出-已结） */
    @ApiModelProperty(value = "房屋状态--字典（待售、售出-未结、售出-已结）", position = 19)
    private String status;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息", position = 20)
    private String extJson;

}
