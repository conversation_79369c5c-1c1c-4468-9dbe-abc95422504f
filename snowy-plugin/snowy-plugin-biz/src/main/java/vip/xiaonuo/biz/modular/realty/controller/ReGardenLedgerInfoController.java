/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.core.handle.Context;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import org.noear.solon.validation.annotation.Validated;
import vip.xiaonuo.biz.modular.realty.param.*;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReGardenLedgerInfo;
import vip.xiaonuo.biz.modular.realty.service.ReGardenLedgerInfoService;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 园区房屋台账信息控制器
 *
 * <AUTHOR>
 * @date  2024/08/28 15:32
 */
@Api(tags = "园区房屋台账信息控制器")
@Controller
@Valid
public class ReGardenLedgerInfoController {

    @Inject
    private ReGardenLedgerInfoService reGardenLedgerInfoService;

    /**
     * 获取园区房屋台账信息分页
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    @ApiOperation("获取园区房屋台账信息分页")
    @SaCheckPermission("/biz/regardenledgerinfo/page")
    @Get
    @Mapping("/biz/regardenledgerinfo/page")
    public CommonResult<Page<ReGardenLedgerInfo>> page(ReGardenLedgerInfoPageParam reGardenLedgerInfoPageParam) {
        return CommonResult.data(reGardenLedgerInfoService.page(reGardenLedgerInfoPageParam));
    }

    /**
     * 添加园区房屋台账信息
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    @ApiOperation("添加园区房屋台账信息")
    @CommonLog("添加园区房屋台账信息")
    @SaCheckPermission("/biz/regardenledgerinfo/add")
    @Post
    @Mapping("/biz/regardenledgerinfo/add")
    public CommonResult<String> add(ReGardenLedgerInfoAddParam reGardenLedgerInfoAddParam) {
        reGardenLedgerInfoService.add(reGardenLedgerInfoAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑园区房屋台账信息
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    @ApiOperation("编辑园区房屋台账信息")
    @CommonLog("编辑园区房屋台账信息")
    @SaCheckPermission("/biz/regardenledgerinfo/edit")
    @Post
    @Mapping("/biz/regardenledgerinfo/edit")
    public CommonResult<String> edit(ReGardenLedgerInfoEditParam reGardenLedgerInfoEditParam) {
        reGardenLedgerInfoService.edit(reGardenLedgerInfoEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除园区房屋台账信息
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    @ApiOperation("删除园区房屋台账信息")
    @CommonLog("删除园区房屋台账信息")
    @SaCheckPermission("/biz/regardenledgerinfo/delete")
    @Post
    @Mapping("/biz/regardenledgerinfo/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReGardenLedgerInfoIdParam> reGardenLedgerInfoIdParamList) {
        reGardenLedgerInfoService.delete(reGardenLedgerInfoIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取园区房屋台账信息详情
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    @ApiOperation("获取园区房屋台账信息详情")
    @SaCheckPermission("/biz/regardenledgerinfo/detail")
    @Get
    @Mapping("/biz/regardenledgerinfo/detail")
    public CommonResult<ReGardenLedgerInfo> detail(ReGardenLedgerInfoIdParam reGardenLedgerInfoIdParam) {
        return CommonResult.data(reGardenLedgerInfoService.detail(reGardenLedgerInfoIdParam));
    }

    /**
     * 根据台账ID获取园区房屋台账信息详情及其附属信息
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    @ApiOperation("获取园区房屋台账信息详情及其附属信息")
    @SaCheckPermission("/biz/regardenledgerinfo/detail")
    @Get
    @Mapping("/biz/regardenledgerinfo/detailWithSub")
    public CommonResult<ReGardenLedgerInfoWithParam> detailWithSub(ReGardenLedgerInfoIdParam reGardenLedgerInfoIdParam) {
        return CommonResult.data(reGardenLedgerInfoService.detailWithSub(reGardenLedgerInfoIdParam));
    }


    /**
     * 根据房屋ID获取园区房屋台账信息详情及其附属信息
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    @ApiOperation("根据房屋ID获取园区房屋台账信息详情及其附属信息")
    @SaCheckPermission("/biz/regardenledgerinfo/detail")
    @Get
    @Mapping("/biz/regardenledgerinfo/detailByHouseId")
    public CommonResult<ReGardenLedgerInfoWithParam> detailByHouseId(@Validated ReGardenHouseIdParam reGardenHouseIdParam) {
        return CommonResult.data(reGardenLedgerInfoService.detailByHouseId(reGardenHouseIdParam));
    }

    /**
     * 添加园区房屋台账信息详情及其附属信息
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    @ApiOperation("添加园区房屋台账信息详情及其附属信息")
    @SaCheckPermission("/biz/regardenledgerinfo/detail")
    @Post
    @Mapping("/biz/regardenledgerinfo/AddDetail")
    public CommonResult<String> AddDetail(@Body ReGardenLedgerInfoWithParam reGardenLedgerInfoWithParam) {
        reGardenLedgerInfoService.AddDetail(reGardenLedgerInfoWithParam);
        return CommonResult.ok();
    }

    /**
     * 更新园区房屋台账信息详情及其附属信息
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    @ApiOperation("更新园区房屋台账信息详情及其附属信息")
    @SaCheckPermission("/biz/regardenledgerinfo/detail")
    @Post
    @Mapping("/biz/regardenledgerinfo/UpdateDetail")
    public CommonResult<String> UpdateDetail(@Body ReGardenLedgerInfoWithParam reGardenLedgerInfoWithParam) {
        reGardenLedgerInfoService.UpdateDetail(reGardenLedgerInfoWithParam);
        return CommonResult.ok();
    }

    /**
     *  将房屋台账所有状态重置 / 退房
     **/
    @ApiOperation(" 将房屋台账所有状态重置 / 退房")
    @SaCheckPermission("/biz/regardenledgerinfo/restore")
    @Post
    @Mapping("/biz/regardenledgerinfo/restore")
    public CommonResult<String> restore(ReGardenHouseIdParam reGardenHouseIdParam) {
        reGardenLedgerInfoService.restore(reGardenHouseIdParam);
        return CommonResult.ok();
    }


    /**
     * 续租(新增台账,其他台账变为历史)
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    @ApiOperation("续租(新增台账,其他台账变为历史)")
    @SaCheckPermission("/biz/regardenledgerinfo/detail")
    @Post
    @CommonLog("房屋续租")
    @Mapping("/biz/regardenledgerinfo/relet")
    public CommonResult<String> relet(@Body ReGardenLedgerInfoWithParam reGardenLedgerInfoWithParam) {
        reGardenLedgerInfoService.relet(reGardenLedgerInfoWithParam);
        return CommonResult.ok();
    }



    /**
     * 园区统计
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    @ApiOperation("园区统计")
    @SaCheckPermission("/biz/regardenledgerinfo/detail")
    @Post
    @Mapping("/biz/regardenledgerinfo/statistics")
    public CommonResult<Map<String,List<ReGardenStatisticsParam>>> statistics(@Body ReGardenHouseIdParam reGardenHouseIdParam) {
        return CommonResult.data(reGardenLedgerInfoService.statistics(reGardenHouseIdParam));
    }

    /**
     * 园区首页统计
     *
     * <AUTHOR>
     * @date  2024/09/3 15:32
     */
    @ApiOperation("园区首页统计")
    @SaCheckPermission("/biz/regardenledgerinfo/detail")
    @Post
    @Mapping("/biz/regardenledgerinfo/indexStatistics")
    public CommonResult<Map<String,List<ReGardenIndexProject>>> indexStatistics() {
        return CommonResult.data(reGardenLedgerInfoService.indexStatistics());
    }

    /**
     *  导出园区台账
     **/
    @ApiOperation("导出园区台账")
    @CommonLog("导出园区台账")
    @Post
    @Mapping("/biz/regardenhouse/export")
    public void export(ReGardenHouseIdParam reGardenHouseIdParam, Context context) {
        try {
            reGardenLedgerInfoService.export(reGardenHouseIdParam, context,false);
        } catch (IOException e) {
            throw new CommonException("导出失败");
        }
    }
}
