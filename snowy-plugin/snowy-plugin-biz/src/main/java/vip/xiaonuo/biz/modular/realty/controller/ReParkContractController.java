/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReParkContract;
import vip.xiaonuo.biz.modular.realty.param.ReParkContractAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkContractEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkContractIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkContractPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReParkContractService;

/**
 * 车位签约控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 */
@Api(tags = "车位签约控制器")
@Controller
@Valid
public class ReParkContractController {

    @Inject
    private ReParkContractService reParkContractService;

    /**
     * 获取车位签约分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取车位签约分页")
    @SaCheckPermission("/biz/reparkcontract/page")
    @Get
    @Mapping("/biz/reparkcontract/page")
    public CommonResult<Page<ReParkContract>> page(ReParkContractPageParam reParkContractPageParam) {
        return CommonResult.data(reParkContractService.page(reParkContractPageParam));
    }

    /**
     * 添加车位签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("添加车位签约")
    @CommonLog("添加车位签约")
    @SaCheckPermission("/biz/reparkcontract/add")
    @Post
    @Mapping("/biz/reparkcontract/add")
    public CommonResult<String> add(ReParkContractAddParam reParkContractAddParam) {
        reParkContractService.add(reParkContractAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑车位签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("编辑车位签约")
    @CommonLog("编辑车位签约")
    @SaCheckPermission("/biz/reparkcontract/edit")
    @Post
    @Mapping("/biz/reparkcontract/edit")
    public CommonResult<String> edit(ReParkContractEditParam reParkContractEditParam) {
        reParkContractService.edit(reParkContractEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除车位签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("删除车位签约")
    @CommonLog("删除车位签约")
    @SaCheckPermission("/biz/reparkcontract/delete")
    @Post
    @Mapping("/biz/reparkcontract/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReParkContractIdParam> reParkContractIdParamList) {
        reParkContractService.delete(reParkContractIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取车位签约详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取车位签约详情")
    @SaCheckPermission("/biz/reparkcontract/detail")
    @Get
    @Mapping("/biz/reparkcontract/detail")
    public CommonResult<ReParkContract> detail(ReParkContractIdParam reParkContractIdParam) {
        return CommonResult.data(reParkContractService.detail(reParkContractIdParam));
    }
}
