/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.biz.modular.realty.entity.*;

import java.util.List;

/**
 * 房屋台账信息添加参数
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
@Getter
@Setter
public class ReLedgerInfoXiaoKongParamVo {

    /** 台账详情 **/
    @ApiModelProperty(value = "台账详情", position = 1)
    private ReLedgerInfo ledgerInfo;

    /** 客户信息 **/
    @ApiModelProperty(value = "客户信息", position = 2)
    private ReCustomer customerInfo;

    /** 销售签约 **/
    @ApiModelProperty(value = "销售签约", position = 4)
    private ReSalesContract saleSign;

    /** 安置签约 **/
    @ApiModelProperty(value = "安置签约", position = 5)
    private RePlacementContract placeSign;

    /** 租赁签约 **/
    @ApiModelProperty(value = "租赁签约", position = 6)
    private ReLeaseContract leaseSign;

    /** 交款信息 交款信息的交款类型不能为-1 **/
    @ApiModelProperty(value = "交款信息", position = 8)
    private List<RePaymentInfo> paymentInfos;

    /** 合同信息 **/
    @ApiModelProperty(value = "合同信息", position = 8)
    private ReContractInfo contractInfo;

    /** 历史台账 **/
    @ApiModelProperty(value = "历史台账", position = 9)
    private List<ReLedgerInfo> historyLedgerInfo;

}
