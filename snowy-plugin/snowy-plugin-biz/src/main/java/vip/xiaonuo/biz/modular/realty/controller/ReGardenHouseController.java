/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.noear.solon.annotation.*;
import org.noear.solon.core.handle.Context;
import org.noear.solon.core.handle.UploadedFile;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReGardenHouse;
import vip.xiaonuo.biz.modular.realty.param.ReGardenHouseAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenHouseEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenHouseIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenHousePageParam;
import vip.xiaonuo.biz.modular.realty.service.ReGardenHouseService;

/**
 * 园区房屋管理控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 */
@Api(tags = "园区房屋管理控制器")
@Controller
@Valid
public class ReGardenHouseController {

    @Inject
    private ReGardenHouseService reGardenHouseService;

    /**
     * 获取园区房屋管理分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取园区房屋管理分页")
    @SaCheckPermission("/biz/regardenhouse/page")
    @Get
    @Mapping("/biz/regardenhouse/page")
    public CommonResult<Page<ReGardenHouse>> page(ReGardenHousePageParam reGardenHousePageParam) {
        return CommonResult.data(reGardenHouseService.page(reGardenHousePageParam));
    }

    /**
     * 添加园区房屋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("添加园区房屋管理")
    @CommonLog("添加园区房屋管理")
    @SaCheckPermission("/biz/regardenhouse/add")
    @Post
    @Mapping("/biz/regardenhouse/add")
    public CommonResult<String> add(ReGardenHouseAddParam reGardenHouseAddParam) {
        reGardenHouseService.add(reGardenHouseAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑园区房屋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("编辑园区房屋管理")
    @CommonLog("编辑园区房屋管理")
    @SaCheckPermission("/biz/regardenhouse/edit")
    @Post
    @Mapping("/biz/regardenhouse/edit")
    public CommonResult<String> edit(ReGardenHouseEditParam reGardenHouseEditParam) {
        reGardenHouseService.edit(reGardenHouseEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除园区房屋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("删除园区房屋管理")
    @CommonLog("删除园区房屋管理")
    @SaCheckPermission("/biz/regardenhouse/delete")
    @Post
    @Mapping("/biz/regardenhouse/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReGardenHouseIdParam> reGardenHouseIdParamList) {
        reGardenHouseService.delete(reGardenHouseIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取园区房屋管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取园区房屋管理详情")
    @SaCheckPermission("/biz/regardenhouse/detail")
    @Get
    @Mapping("/biz/regardenhouse/detail")
    public CommonResult<ReGardenHouse> detail(ReGardenHouseIdParam reGardenHouseIdParam) {
        return CommonResult.data(reGardenHouseService.detail(reGardenHouseIdParam));
    }



    /**
     *  从excel批量导入房源信息
     **/
    @ApiOperation("园区房源导入")
    @CommonLog("园区房源导入")
    @Post
    @Mapping("/biz/regardenhouse/import")
    public CommonResult<JSONObject> importExcel(@ApiParam(value="文件", required = true) UploadedFile file,ReGardenHouseIdParam reGardenHouseIdParam) {
        return CommonResult.data(reGardenHouseService.importExcel(file,reGardenHouseIdParam));
    }

    /**
     *   下载excel导入模板
     **/
    @ApiOperation("下载园区房源导入模板")
    @CommonLog("下载园区房源导入模板")
    @Get
    @Mapping("/biz/regardenhouse/downloadTemplate")
    public void downloadTemplate(Context context) {
        reGardenHouseService.downloadTemplate(context);
    }

    /**
     * 获取园区房屋已售未售面积
     * @param buildId 传buildId 不要传Id
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取园区房屋已售未售面积")
    @SaCheckPermission("/biz/regardenhouse/detail")
    @Get
    @Mapping("/biz/regardenhouse/soldAndUnsoldArea")
    public CommonResult<JSONObject> soldAndUnsoldArea(ReGardenHouseIdParam reGardenHouseIdParam) {
        return CommonResult.data(reGardenHouseService.soldAndUnsoldArea(reGardenHouseIdParam));
    }
}
