/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.util.StringUtil;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Bean;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.core.handle.Context;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.mapper.*;
import vip.xiaonuo.biz.modular.realty.param.*;
import vip.xiaonuo.biz.modular.realty.service.*;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.common.util.CommonDownloadUtil;
import vip.xiaonuo.common.util.CommonUaUtil;
import vip.xiaonuo.dev.api.DevDictApi;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 园区房屋台账信息Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/08/28 15:32
 **/
@Component
public class ReGardenLedgerInfoServiceImpl extends ServiceImpl<ReGardenLedgerInfoMapper, ReGardenLedgerInfo> implements ReGardenLedgerInfoService {

    @Inject
    private ReGardenCustomerMapper reGardenCustomerMapper;

    @Inject
    private ReGardenContractMapper reGardenContractMapper;

    @Inject
    private ReGardenOtherInfoMapper reGardenOtherInfoMapper;

    @Inject
    private RePaymentInfoMapper rePaymentInfoMapper;

    @Inject
    private ReGardenHouseMapper reGardenHouseMapper;

    @Inject
    private ReProjectMapper reProjectMapper;

    @Inject
    private DevDictApi devDictApi;


    QueryWrapper buildQueryWapper(ReGardenLedgerInfoPageParam reGardenLedgerInfoPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if (ObjectUtil.isAllNotEmpty(reGardenLedgerInfoPageParam.getSortField(), reGardenLedgerInfoPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reGardenLedgerInfoPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reGardenLedgerInfoPageParam.getSortField()), reGardenLedgerInfoPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReGardenLedgerInfo::getId);
        }
        //查询
        if (StrUtil.isNotEmpty(reGardenLedgerInfoPageParam.getHouseType())) {
            queryWrapper.eq(ReGardenLedgerInfo::getHouseType, reGardenLedgerInfoPageParam.getHouseType());
        }
        queryWrapper.like(ReGardenLedgerInfo::getName, reGardenLedgerInfoPageParam.getName(), StringUtil.isNotBlank(reGardenLedgerInfoPageParam.getName()));
        queryWrapper.like(ReGardenLedgerInfo::getIdCard, reGardenLedgerInfoPageParam.getIdCard(), StringUtil.isNotBlank(reGardenLedgerInfoPageParam.getIdCard()));
        queryWrapper.like(ReGardenLedgerInfo::getPhone, reGardenLedgerInfoPageParam.getPhone(), StringUtil.isNotBlank(reGardenLedgerInfoPageParam.getPhone()));
        queryWrapper.eq(ReGardenLedgerInfo::getHouseNumber, reGardenLedgerInfoPageParam.getHouseNumber(), StringUtil.isNotBlank(reGardenLedgerInfoPageParam.getHouseNumber()));
        queryWrapper.eq(ReGardenLedgerInfo::getHouseId, reGardenLedgerInfoPageParam.getHouseId(), StringUtil.isNotBlank(reGardenLedgerInfoPageParam.getHouseId()));
        queryWrapper.eq(ReGardenLedgerInfo::getContractType, reGardenLedgerInfoPageParam.getContractType(), StringUtil.isNotBlank(reGardenLedgerInfoPageParam.getContractType()));
        queryWrapper.eq(ReGardenLedgerInfo::getProjectId, reGardenLedgerInfoPageParam.getProjectId(), StringUtil.isNotBlank(reGardenLedgerInfoPageParam.getProjectId()));
        queryWrapper.eq(ReGardenLedgerInfo::getFloor, reGardenLedgerInfoPageParam.getFloor(), StringUtil.isNotBlank(reGardenLedgerInfoPageParam.getFloor()));
        queryWrapper.like(ReGardenLedgerInfo::getBuildCode, reGardenLedgerInfoPageParam.getBuildCode(), StringUtil.isNotBlank(reGardenLedgerInfoPageParam.getBuildCode()));
        if (null != reGardenLedgerInfoPageParam.getContractTime()) {
            queryWrapper.where("date_format(contract_time,'%Y-%m-%d') = ?", reGardenLedgerInfoPageParam.getContractTime());
        }
        return queryWrapper;

    }


    @Override
    public Page<ReGardenLedgerInfo> page(ReGardenLedgerInfoPageParam reGardenLedgerInfoPageParam) {
        QueryWrapper queryWrapper = buildQueryWapper(reGardenLedgerInfoPageParam);
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public void add(ReGardenLedgerInfoAddParam reGardenLedgerInfoAddParam) {
        ReGardenLedgerInfo reGardenLedgerInfo = BeanUtil.toBean(reGardenLedgerInfoAddParam, ReGardenLedgerInfo.class);
        String projectId = reGardenLedgerInfo.getProjectId();
        if (StrUtil.isEmpty(projectId)) {
            throw new CommonException("所属项目不能为空");
        }
        this.save(reGardenLedgerInfo);
    }

    @Tran
    @Override
    public void edit(ReGardenLedgerInfoEditParam reGardenLedgerInfoEditParam) {
        ReGardenLedgerInfo reGardenLedgerInfo = this.queryEntity(reGardenLedgerInfoEditParam.getId());
        BeanUtil.copyProperties(reGardenLedgerInfoEditParam, reGardenLedgerInfo);
        String projectId = reGardenLedgerInfo.getProjectId();
        if (StrUtil.isEmpty(projectId)) {
            throw new CommonException("所属项目不能为空");
        }
        this.updateById(reGardenLedgerInfo);
    }

    @Tran
    @Override
    public void delete(List<ReGardenLedgerInfoIdParam> reGardenLedgerInfoIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(reGardenLedgerInfoIdParamList, ReGardenLedgerInfoIdParam::getId));
    }

    @Override
    public ReGardenLedgerInfo detail(ReGardenLedgerInfoIdParam reGardenLedgerInfoIdParam) {
        return this.queryEntity(reGardenLedgerInfoIdParam.getId());
    }

    @Override
    public ReGardenLedgerInfo queryEntity(String id) {
        ReGardenLedgerInfo reGardenLedgerInfo = this.getById(id);
        if (ObjectUtil.isEmpty(reGardenLedgerInfo)) {
            throw new CommonException("园区房屋台账信息不存在，id值为：{}", id);
        }
        return reGardenLedgerInfo;
    }

    @Override
    public ReGardenLedgerInfoWithParam detailWithSub(ReGardenLedgerInfoIdParam reGardenLedgerInfoIdParam) {
        ReGardenLedgerInfoWithParam reGardenLedgerInfoWithParam = new ReGardenLedgerInfoWithParam();
        String id = reGardenLedgerInfoIdParam.getId();
        //基本信息
        ReGardenLedgerInfo reGardenLedgerInfo = this.queryEntity(id);
        BeanUtil.copyProperties(reGardenLedgerInfo, reGardenLedgerInfoWithParam);
        //扩展信息
        //查询所需的Map
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("LEDGER_ID", id);
        //客户
        ReGardenCustomer reGardenCustomer = reGardenCustomerMapper.selectOneByMap(queryMap);
        //签约
        ReGardenContract reGardenContract = reGardenContractMapper.selectOneByMap(queryMap);
        //其他
        ReGardenOtherInfo reGardenOtherInfo = reGardenOtherInfoMapper.selectOneByMap(queryMap);
        //交款
        if (reGardenContract != null) {
            queryMap.put("CONTRACT_ID", reGardenContract.getId());
        }
        List<RePaymentInfo> rePaymentInfoList = rePaymentInfoMapper.selectListByMap(queryMap);
        //设置
        if (ObjectUtil.isNotNull(reGardenCustomer)) {
            ReGardenCustomerEditParam bean = BeanUtil.toBean(reGardenCustomer, ReGardenCustomerEditParam.class);
            reGardenLedgerInfoWithParam.setCustomer(bean);
        }
        if (ObjectUtil.isNotNull(reGardenContract)) {
            ReGardenContractEditParam bean = BeanUtil.toBean(reGardenContract, ReGardenContractEditParam.class);
            reGardenLedgerInfoWithParam.setContract(bean);
            reGardenLedgerInfoWithParam.setContractStatus("0");
        } else {
            reGardenLedgerInfoWithParam.setContractStatus("2");
        }
        if (ObjectUtil.isNotNull(reGardenOtherInfo)) {
            ReGardenOtherInfoEditParam bean = BeanUtil.toBean(reGardenOtherInfo, ReGardenOtherInfoEditParam.class);
            reGardenLedgerInfoWithParam.setOther(bean);
        }
        if (CollectionUtil.isNotEmpty(rePaymentInfoList)) {
            List<RePaymentInfoEditParam> beans = new ArrayList<>();
            for (RePaymentInfo rePaymentInfo : rePaymentInfoList) {
                RePaymentInfoEditParam bean = BeanUtil.toBean(rePaymentInfo, RePaymentInfoEditParam.class);
                beans.add(bean);
            }
            reGardenLedgerInfoWithParam.setPayments(beans);
        }
        return reGardenLedgerInfoWithParam;
    }

    @Override
    public ReGardenLedgerInfoWithParam detailByHouseId(ReGardenHouseIdParam reGardenHouseIdParam) {

        ReGardenLedgerInfoWithParam res = new ReGardenLedgerInfoWithParam();
        List<ReGardenLedgerInfoWithParam> history = new ArrayList<>();

        String id = reGardenHouseIdParam.getId();
        //查询出当前房屋
        ReGardenHouse reGardenHouse = reGardenHouseMapper.selectOneById(id);
        //查询台账的Map
        Map<String, Object> queryLedgerMap = new HashMap<>();
        queryLedgerMap.put("HOUSE_ID", id);
        List<ReGardenLedgerInfo> reGardenLedgerInfos = this.listByMap(queryLedgerMap);
        //处理历史与当前版本
        if (CollectionUtil.isNotEmpty(reGardenLedgerInfos)) {
            for (ReGardenLedgerInfo reGardenLedgerInfo : reGardenLedgerInfos) {
                ReGardenLedgerInfoIdParam reGardenLedgerInfoIdParam = new ReGardenLedgerInfoIdParam();
                reGardenLedgerInfoIdParam.setId(reGardenLedgerInfo.getId());
                ReGardenLedgerInfoWithParam reGardenLedgerInfoWithParam = this.detailWithSub(reGardenLedgerInfoIdParam);
                if (!reGardenLedgerInfoWithParam.getIsHistory()) {
                    res = reGardenLedgerInfoWithParam;
                } else {
                    history.add(reGardenLedgerInfoWithParam);
                }
            }
        }
        res.setHistory(history);
        return res;
    }

    @Tran
    @Override
    public void AddDetail(ReGardenLedgerInfoWithParam reGardenLedgerInfoWithParam) {
        ReGardenLedgerInfo bean = BeanUtil.toBean(reGardenLedgerInfoWithParam, ReGardenLedgerInfo.class);
        String projectId = bean.getProjectId();
        if (StrUtil.isEmpty(projectId)) {
            throw new CommonException("所属项目不能为空");
        }
        //从houseId 查询house 赋值到台账
        ReGardenHouseIdParam reGardenHouseIdParam = new ReGardenHouseIdParam();
        reGardenHouseIdParam.setId(reGardenLedgerInfoWithParam.getHouseId());
        ReGardenHouse reGardenHouse = reGardenHouseMapper.selectOneById(reGardenHouseIdParam.getId());

        bean.setFloor(reGardenHouse.getFloor());
        bean.setHouseNumber(reGardenHouse.getHouseNum());
        bean.setArea(reGardenHouse.getBuildArea());
        bean.setHouseType(reGardenHouse.getHouseType());
        bean.setStatus(reGardenHouse.getStatus());
        bean.setCreateTime(new Date());

        bean.setIsHistory(false);
        if (this.save(bean)) {
            String ledgerId = bean.getId();
            //插入客户
            ReGardenCustomerEditParam customer = reGardenLedgerInfoWithParam.getCustomer();
            if (customer != null) {
                //客户信息 赋值到台账并且更新
                bean.setName(customer.getContactPerson());
                bean.setEnterprise(customer.getCompany());
                bean.setIdCard(customer.getIdCard());
                bean.setPhone(customer.getPhone());
                this.updateById(bean);

                customer.setLedgerId(ledgerId);
                ReGardenCustomer insert = BeanUtil.toBean(customer, ReGardenCustomer.class);
                int customerInsert = reGardenCustomerMapper.insert(insert);

                //更新房屋展示信息
                reGardenHouse.setCustomerName(customer.getContactPerson());
                reGardenHouse.setCustomerPhone(customer.getPhone());
                reGardenHouseMapper.update(reGardenHouse);
            }

            //插入签约信息
            ReGardenContractEditParam contract = reGardenLedgerInfoWithParam.getContract();
            if (contract != null) {
                //签约信息 赋值到台账并且更新
                bean.setContractType("2");// 固定为租赁
                bean.setDealUnitPrice(contract.getLeasePrice());
                bean.setContractTime(contract.getContractTime());

                this.updateById(bean);

                contract.setLedgerId(ledgerId);
                ReGardenContract insert = BeanUtil.toBean(contract, ReGardenContract.class);
                int contractInsert = reGardenContractMapper.insert(insert);
                if (contractInsert > 0) {
                    // todo 如果需要计算的话 计算交付期限  插入付款信息
                    String contractId = contract.getId();
                }
                //更新房屋展示信息
                reGardenHouse.setStatus("1");
                reGardenHouse.setLeaseTime(contract.getLeaseEndTime());
                reGardenHouse.setLeaseUnitPrice(contract.getLeasePrice());
                reGardenHouseMapper.update(reGardenHouse);


                // 判断租金 如果 已缴租金和应缴租金都不为空且 已缴租金大于等于应缴租金 则台账状态为已结清
                if (contract.getRentPayable() != null && contract.getRentPaid() != null && contract.getRentPaid().compareTo(contract.getRentPayable()) >= 0) {
                    bean.setStatus("3");  // 修复：已结清应该是状态"3"，不是"2"
                    this.updateById(bean);
                    reGardenHouse.setStatus("3");  // 修复：房屋状态也应该是"3"
                    reGardenHouseMapper.update(reGardenHouse);
                }
            }

            //插入其他信息
            ReGardenOtherInfoEditParam other = reGardenLedgerInfoWithParam.getOther();
            if (other != null) {
                other.setLedgerId(ledgerId);

                ReGardenOtherInfo insert = BeanUtil.toBean(other, ReGardenOtherInfo.class);
                int otherInsert = reGardenOtherInfoMapper.insert(insert);
            }
            //变更上条信息为历史
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("HOUSE_ID", bean.getHouseId());
            queryWrapper.eq("IS_HISTORY", false);
            queryWrapper.ne("ID", ledgerId);
            ReGardenLedgerInfo reGardenLedgerInfo = new ReGardenLedgerInfo();
            reGardenLedgerInfo.setIsHistory(true);
            this.update(reGardenLedgerInfo, queryWrapper);


        }
    }

    @Override
    public Map<String, List<ReGardenStatisticsParam>> statistics(ReGardenHouseIdParam reGardenHouseIdParam) {
        if (ObjectUtil.isAllEmpty(reGardenHouseIdParam.getStartTime(), reGardenHouseIdParam.getEndTime())) {
            throw new CommonException("开始时间和结束时间不能为空");
        }
        List<ReGardenStatisticsParam> list = this.mapper.selectIndexStatisticsTime(reGardenHouseIdParam.getProjectId(), reGardenHouseIdParam.getStartTime(), reGardenHouseIdParam.getEndTime());
        list.forEach(f->{
            f.setFlag("down");
        });
        List<ReGardenStatisticsParam> list2 = this.mapper.selectIndexStatisticsNotTime(reGardenHouseIdParam.getProjectId(), reGardenHouseIdParam.getStartTime(), reGardenHouseIdParam.getEndTime());
        list2.forEach(f->{
            f.setFlag("up");
        });
        list.addAll(list2);
        if (CollectionUtil.isNotEmpty(list)) {
            return list.stream().collect(Collectors.groupingBy(ReGardenStatisticsParam::getHouseType));
        }else {
            return new HashMap<>();
        }
    }


    @Override
    public Map<String, List<ReGardenIndexProject>> indexStatistics() {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq(ReProject::getCategory, "181216370270269440");
        List<ReProject> reProjects = reProjectMapper.selectListByQuery(queryWrapper);
        List<String> collect = reProjects.stream().map(ReProject::getId).collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(collect)){
            List<ReGardenIndexStatisticsParam> reGardenIndexStatisticsParams = this.mapper.selectIndexStatistics(collect);
            //根据ID获取项目名称
            return reProjects.stream().map(reProject -> {
                ReGardenIndexProject bean = BeanUtil.toBean(reProject, ReGardenIndexProject.class);
                List<ReGardenIndexStatisticsParam> res = reGardenIndexStatisticsParams.stream().filter(result -> {
                    return result.getProjectId().equals(reProject.getId());
                }).collect(Collectors.toList());
                bean.setList(res);
                return bean;
            }).collect(Collectors.groupingBy(ReGardenIndexProject::getId));
        }
        return new HashMap<>();
    }

    @Override
    public void export(ReGardenHouseIdParam reGardenHouseIdParam, Context context,Boolean isHistory) throws IOException {
        String projectId = reGardenHouseIdParam.getProjectId();
        if (StrUtil.isEmpty(projectId)){
            throw new CommonException("项目ID不能为空");
        }
        String houseType = reGardenHouseIdParam.getHouseType();
        if (StrUtil.isEmpty(houseType)){
            throw new CommonException("房屋类型不能为空");
        }
        ReGardenLedgerInfoPageParam reGardenLedgerInfoPageParam = new ReGardenLedgerInfoPageParam();
        reGardenLedgerInfoPageParam.setProjectId(projectId);
        reGardenLedgerInfoPageParam.setHouseType(houseType);
        QueryWrapper queryWrapper = buildQueryWapper(reGardenLedgerInfoPageParam);
//        去掉查询条件 历史台账
//        queryWrapper.eq("IS_HISTORY", isHistory);
        List<ReGardenLedgerInfo> reGardenLedgerInfos = this.mapper.selectListByQuery(queryWrapper);
        if (reGardenLedgerInfos.isEmpty()){
            throw new CommonException("没有数据可以导出!");
        }
        //处理数据到excel实体类
        //查找字典 项目 表 拿到园区名称 和 房屋类型
        ReProject reProject = reProjectMapper.selectOneById(projectId);
        if (reProject == null){
            throw new CommonException("项目不存在!");
        }
        String projectName = reProject.getName();
        JSONArray reHouseType = devDictApi.getDictListByType("RE_HOUSE_TYPE");
        String houseTypeName = "";
        if (reHouseType == null){
            houseTypeName = "未知";
        }else {
            houseTypeName = reHouseType.stream().map(obj -> {
                return JSONUtil.parseObj(obj.toString());
            }).filter(obj -> {
                return obj.getStr("dictValue").equals(houseType);
            }).findFirst().orElse(new JSONObject()).getStr("dictLabel");
        }
        //定义好表头
        // 定义表头
        List<List<String>> exportHeaders = getExportHeaders(projectName, houseTypeName);
        // 定义导出样式（可选）
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, new WriteCellStyle());

        //循环处理数据
        List<RegardenLedgerExport> data = new ArrayList<>();
        for (ReGardenLedgerInfo reGardenLedgerInfo : reGardenLedgerInfos) {
            RegardenLedgerExport r = new RegardenLedgerExport();
            //查询相关的客户 房屋 签约 其他信息
            Map<String,Object> map = new HashMap<>();
            map.put("LEDGER_ID",reGardenLedgerInfo.getId());
            ReGardenOtherInfo reGardenOtherInfo = reGardenOtherInfoMapper.selectOneByMap(map);
            ReGardenCustomer reGardenCustomer = reGardenCustomerMapper.selectOneByMap(map);
            ReGardenContract reGardenContract = reGardenContractMapper.selectOneByMap(map);

            //赋值
            r.setId(reGardenLedgerInfo.getId());
            r.setProjectName(projectName);
            r.setBuildingNo(reGardenLedgerInfo.getBuildCode() == null ? "" : reGardenLedgerInfo.getBuildCode());
            r.setRoomNo(reGardenLedgerInfo.getHouseNumber() == null ? "" : reGardenLedgerInfo.getHouseNumber());
            r.setBuildArea(StrUtil.isEmpty(String.valueOf(reGardenLedgerInfo.getArea()))? "" : String.valueOf(reGardenLedgerInfo.getArea()));
            r.setCorridorArea(StrUtil.isEmpty(String.valueOf(reGardenLedgerInfo.getCorridorArea())) ? "" : String.valueOf(reGardenLedgerInfo.getCorridorArea()));
            r.setSettledEnterprise(StrUtil.isEmpty(reGardenLedgerInfo.getEnterprise()) ? "" : reGardenLedgerInfo.getEnterprise());
            r.setEnterpriseLegalPerson(StrUtil.isEmpty(reGardenCustomer.getLegalPerson()) ? "" : reGardenCustomer.getLegalPerson());
            r.setIdCardNumber(StrUtil.isEmpty(reGardenLedgerInfo.getIdCard()) ? "" : reGardenLedgerInfo.getIdCard());
            r.setEnterpriseContact(StrUtil.isEmpty(reGardenLedgerInfo.getName()) ? "" : reGardenLedgerInfo.getName());
            r.setContactPhone(StrUtil.isEmpty(reGardenLedgerInfo.getPhone()) ? "" : reGardenLedgerInfo.getPhone());
            r.setLeaseContractSigned("是");
            r.setZoneAgreementSigned("是");
            r.setLeasePrice(reGardenLedgerInfo.getDealUnitPrice() == null ? "" : reGardenLedgerInfo.getDealUnitPrice().toString());
            r.setCorridorRent(reGardenContract.getCorridorRent() == null ? "" : reGardenContract.getCorridorRent().toString());
            r.setMonthlyRent(reGardenContract.getMonthlyRent() == null ? "" : reGardenContract.getMonthlyRent().toString());
            r.setLeaseDeposit("0");
            r.setLeaseTerm(reGardenContract.getLeaseTerm() == null ? "" : reGardenContract.getLeaseTerm().toString());
            r.setLeaseStartDate(reGardenContract.getLeaseStartTime() == null ? null : reGardenContract.getLeaseStartTime());
            r.setLeaseEndDate(reGardenContract.getLeaseEndTime() == null ? null : reGardenContract.getLeaseEndTime());
            r.setRentCalculationDate(reGardenContract.getLeaseCalculationDate() == null ? null : reGardenContract.getLeaseCalculationDate());
            r.setRentFreePeriod(reGardenContract.getRentFreePeriod() == null ? "" : reGardenContract.getRentFreePeriod().toString());
            r.setPayableRent(reGardenContract.getRentPayable() == null ? "" : reGardenContract.getRentPayable().toString());
            r.setPaidRent(reGardenContract.getRentPaid() == null ? "" : reGardenContract.getRentPaid().toString());
            r.setUnpaidRent(reGardenContract.getRentArrears() == null ? "" : reGardenContract.getRentArrears().toString());
            r.setRemarks(reGardenContract.getRemark() == null ? "" : reGardenContract.getRemark());
            r.setEnterprisePolicy(reGardenOtherInfo.getCompanyPolicy() == null ? "" : reGardenOtherInfo.getCompanyPolicy());
            r.setBusinessProducts(reGardenOtherInfo.getBusinessProduct() == null ? "" : reGardenOtherInfo.getBusinessProduct());
            r.setInvestmentAmount(reGardenOtherInfo.getInvestmentAmount() == null ? "" : String.valueOf(reGardenOtherInfo.getInvestmentAmount()));
            r.setNumberOfEmployees(reGardenOtherInfo.getStaffNumber() == null ? "" : String.valueOf(reGardenOtherInfo.getStaffNumber()));
            r.setAnnualTurnover(reGardenOtherInfo.getAnnualTurnover() == null ? "" : String.valueOf(reGardenOtherInfo.getAnnualTurnover()));
            r.setSupportRequirements(reGardenOtherInfo.getSupportingRequirements() == null ? "" : reGardenOtherInfo.getSupportingRequirements());
            r.setExistingProblems(reGardenOtherInfo.getExistingProblems() == null ? "" : reGardenOtherInfo.getExistingProblems());
            data.add(r);
        }

        //导出
        context.headerSet("Content-Disposition", "attachment;filename=" + URLUtil.encode("园区台账.xlsx"));
        context.headerSet("Access-Control-Allow-Origin", "*");
        context.headerSet("Access-Control-Expose-Headers", "Content-Disposition");
        context.contentType("application/octet-stream;charset=UTF-8");
        EasyExcel.write(context.outputStream())
                .head(exportHeaders)
                .registerWriteHandler(horizontalCellStyleStrategy)
                .sheet("台账")
                .doWrite(data);
    }

    @Override
    public void restore(ReGardenHouseIdParam reGardenHouseIdParam) {
        String id = reGardenHouseIdParam.getId();
        if (StrUtil.isEmpty(id)) {
            throw new CommonException("更新时台账id不能为空");
        }
        ReGardenHouse reGardenHouse = reGardenHouseMapper.selectOneById(reGardenHouseIdParam.getId());
        if (Optional.ofNullable(reGardenHouse).isEmpty()) {
            throw new CommonException("房屋不存在");
        }
        reGardenHouse.setStatus("0");
        reGardenHouseMapper.update(reGardenHouse);
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("HOUSE_ID", reGardenHouseIdParam.getId());
        queryWrapper.eq("IS_HISTORY", false);
        ReGardenLedgerInfo reGardenLedgerInfo = new ReGardenLedgerInfo();
        reGardenLedgerInfo.setIsHistory(true);
        this.update(reGardenLedgerInfo, queryWrapper);
    }

    @Tran
    @Override
    public void UpdateDetail(ReGardenLedgerInfoWithParam reGardenLedgerInfoWithParam) {
        String id = reGardenLedgerInfoWithParam.getId();
        if (StrUtil.isEmpty(id)) {
            throw new CommonException("更新时台账id不能为空");
        }
        ReGardenLedgerInfo reGardenLedgerInfo = queryEntity(id);
        //更新客户信息
        ReGardenCustomerEditParam customer = reGardenLedgerInfoWithParam.getCustomer();
        if (customer != null) {
            ReGardenCustomer bean = BeanUtil.toBean(customer, ReGardenCustomer.class);
            String customerId = bean.getId();
            if (StrUtil.isEmpty(customerId)) {
                bean.setLedgerId(id);
                int customerInsert = reGardenCustomerMapper.insert(bean);
            }
            //更新园区中的客户信息
            reGardenLedgerInfo.setName(bean.getContactPerson());
            reGardenLedgerInfo.setEnterprise(bean.getCompany());
            reGardenLedgerInfo.setIdCard(bean.getIdCard());
            reGardenLedgerInfo.setPhone(bean.getPhone());
            //更新
            reGardenCustomerMapper.update(bean);
            //更新房屋的客户信息
            ReGardenHouse house = reGardenHouseMapper.selectOneById(reGardenLedgerInfo.getHouseId());
            house.setCustomerName(bean.getContactPerson());
            house.setCustomerPhone(bean.getPhone());
            reGardenHouseMapper.update(house);

            this.mapper.update(reGardenLedgerInfo);
        }
        //更新签约信息
        ReGardenContractEditParam contract = reGardenLedgerInfoWithParam.getContract();
        if (contract != null) {
            ReGardenContract bean = BeanUtil.toBean(contract, ReGardenContract.class);
            String contractId = bean.getId();
            if (StrUtil.isEmpty(contractId)) {
                bean.setLedgerId(id);
                int contractInsert = reGardenContractMapper.insert(bean);
            }
            //更新台账中的签约信息
            reGardenLedgerInfo.setContractType("2");// 固定为租赁
            reGardenLedgerInfo.setDealUnitPrice(bean.getLeasePrice());
            reGardenLedgerInfo.setContractTime(bean.getContractTime());
            //更新
            reGardenContractMapper.update(bean);
            this.mapper.update(reGardenLedgerInfo);
            //todo 付款信息


            //更新房屋中的签约信息
            ReGardenHouse house = reGardenHouseMapper.selectOneById(reGardenLedgerInfo.getHouseId());
            house.setStatus(contract.getContractTime()!= null ? "1":"2");
            house.setLeaseTime(bean.getLeaseEndTime());
            house.setLeaseUnitPrice(bean.getLeasePrice());
            reGardenHouseMapper.update(house);

            // 判断租金 如果 已缴租金和应缴租金都不为空且 已缴租金大于等于应缴租金 则台账状态为已结清
            assert contract != null;
            if (contract.getRentPayable() != null && contract.getRentPaid() != null && contract.getRentPaid().compareTo(contract.getRentPayable()) >= 0) {
                reGardenLedgerInfo.setStatus("3");  // 修复：已结清应该是状态"3"，不是"2"
                this.updateById(reGardenLedgerInfo);
                ReGardenHouse reGardenHouse = reGardenHouseMapper.selectOneById(reGardenLedgerInfo.getHouseId());
                reGardenHouse.setStatus("3");  // 修复：房屋状态也应该是"3"
                reGardenHouseMapper.update(reGardenHouse);
            }
        }
        //更新其他信息
        ReGardenOtherInfoEditParam other = reGardenLedgerInfoWithParam.getOther();
        if (other != null) {
            ReGardenOtherInfo bean = BeanUtil.toBean(other, ReGardenOtherInfo.class);
            String otherId = bean.getId();
            if (StrUtil.isEmpty(otherId)) {
                bean.setLedgerId(id);
                int otherInsert = reGardenOtherInfoMapper.insert(bean);
            }
            reGardenOtherInfoMapper.update(bean);
        }

        return;
    }

    @Override
    public void relet(ReGardenLedgerInfoWithParam reGardenLedgerInfoWithParam) {
        if (StrUtil.isEmpty(reGardenLedgerInfoWithParam.getHouseId())) {
            throw new CommonException("房屋id不能为空");
        }

        //变为历史
        ReGardenHouseIdParam reGardenHouseIdParam = new ReGardenHouseIdParam();
        reGardenHouseIdParam.setId(reGardenLedgerInfoWithParam.getHouseId());
        restore(reGardenHouseIdParam);
        //新增台账
        //新增的ID全部置空
        reGardenLedgerInfoWithParam.setId(null);
        if (reGardenLedgerInfoWithParam.getCustomer() != null) {
            reGardenLedgerInfoWithParam.getCustomer().setId(null);
        }
        if (reGardenLedgerInfoWithParam.getContract() != null) {
            reGardenLedgerInfoWithParam.getContract().setId(null);
        }
        if (reGardenLedgerInfoWithParam.getOther() != null) {
            reGardenLedgerInfoWithParam.getOther().setId(null);
        }
        AddDetail(reGardenLedgerInfoWithParam);
    }
    
    List<List<String>> getExportHeaders(String projectName,String houseTypeName){
        // 定义表头
        List<List<String>> headers = new ArrayList<>();
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "序号"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "项目名称"));
//        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "物业类别"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "楼栋号"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "房号"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "建筑面积"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "连廊面积"));
//        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "物业现状"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "入驻企业"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "企业法人"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "身份证号码"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "企业联络人"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "联系电话"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "租赁合同是否签订"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "入区协议是否签订"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "租赁价格（元/月/㎡）"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "连廊租金"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "月租金"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "租赁保证金"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "租赁起始日期"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "租赁截止日期"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "租赁计算日期"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "租期"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "免租期"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "应交租金"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "已交租金"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "欠交租金"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "备注"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "企业优惠政策"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "经营产品"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "投资额"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "员工人数"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "配套需求"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "存在问题"));
        headers.add(Arrays.asList(projectName + houseTypeName + "台账", "年营业额"));
        return headers;
    }
}
