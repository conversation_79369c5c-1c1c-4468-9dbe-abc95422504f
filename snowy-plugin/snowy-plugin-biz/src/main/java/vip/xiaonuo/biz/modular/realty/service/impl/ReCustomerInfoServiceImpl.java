/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.mapper.ReCustomerInfoMapper;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerInfoAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerInfoEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerInfoIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerInfoPageParam;
import vip.xiaonuo.biz.modular.realty.service.*;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 客户信息Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/08/17 14:34
 **/
@Component
public class ReCustomerInfoServiceImpl extends ServiceImpl<ReCustomerInfoMapper, ReCustomerInfo> implements ReCustomerInfoService {

    @Inject
    private ReLedgerInfoService reLedgerInfoService;

    @Inject
    private ReHouseService reHouseService;

    @Inject
    private ReParkLedgerInfoService reParkLedgerInfoService;

    @Inject
    private ReParkService reParkService;

    @Override
    public Page<ReCustomerInfo> page(ReCustomerInfoPageParam reCustomerInfoPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if (ObjectUtil.isAllNotEmpty(reCustomerInfoPageParam.getSortField(), reCustomerInfoPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reCustomerInfoPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reCustomerInfoPageParam.getSortField()), reCustomerInfoPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReCustomerInfo::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public String add(ReCustomerInfoAddParam reCustomerInfoAddParam) {
        ReCustomerInfo reCustomerInfo = BeanUtil.toBean(reCustomerInfoAddParam, ReCustomerInfo.class);
        Optional<ReCustomerInfo> oneOpt = this.getOneOpt(QueryWrapper.create().eq(ReCustomerInfo::getIdCard, reCustomerInfoAddParam.getIdCard(), StrUtil.isNotEmpty(reCustomerInfoAddParam.getIdCard())).eq(ReCustomerInfo::getName, reCustomerInfoAddParam.getName(), StrUtil.isNotEmpty(reCustomerInfoAddParam.getName())));
        if (oneOpt.isPresent() && reCustomerInfo.getIdCard() != null) {
            if (null == oneOpt.get().getExtJson() && null != reCustomerInfoAddParam.getExtJson()) {
                oneOpt.get().setExtJson(reCustomerInfoAddParam.getExtJson());
                updateById(oneOpt.get());
                oneOpt.get().setExtJson(null);
                return JSONUtil.toJsonStr(oneOpt.get());
            }
            reCustomerInfo.setId(oneOpt.get().getId());
            updateById(reCustomerInfo);
            return JSONUtil.toJsonStr(reCustomerInfo);
        }
        this.save(reCustomerInfo);
        return reCustomerInfo.getId();
    }

    @Tran
    @Override
    public void edit(ReCustomerInfoEditParam reCustomerInfoEditParam) {
        ReCustomerInfo reCustomerInfo = this.queryEntity(reCustomerInfoEditParam.getId());
        if (reCustomerInfoEditParam.getIsLedger()) {
            if (reCustomerInfoEditParam.getLedgerId() != null) {
                // 同时修改台账里边的名称和身份证号
                reLedgerInfoService.updateChain().set(ReLedgerInfo::getName, reCustomerInfoEditParam.getName()).set(ReLedgerInfo::getPhone, reCustomerInfoEditParam.getPhone()).set(ReLedgerInfo::getIdCard, reCustomerInfoEditParam.getIdCard()).eq(ReLedgerInfo::getId, reCustomerInfoEditParam.getLedgerId()).update();
                reLedgerInfoService.getByIdOpt(reCustomerInfoEditParam.getLedgerId()).ifPresent(reLedgerInfo -> {
                    // 同时修改房屋里边的客户名称和电话
                    reHouseService.updateChain().set(ReHouse::getCustomerName, reCustomerInfoEditParam.getName()).set(ReHouse::getCustomerPhone, reCustomerInfoEditParam.getPhone()).eq(ReHouse::getId, reLedgerInfo.getHouseId()).update();
                });
            }
            if (reCustomerInfoEditParam.getParkingLedgerId() != null) {
                reParkLedgerInfoService.updateChain().set(ReParkLedgerInfo::getName, reCustomerInfoEditParam.getName()).set(ReParkLedgerInfo::getPhone, reCustomerInfoEditParam.getPhone()).set(ReParkLedgerInfo::getIdCard, reCustomerInfoEditParam.getIdCard()).eq(ReParkLedgerInfo::getId, reCustomerInfoEditParam.getParkingLedgerId()).update();
                reParkLedgerInfoService.getByIdOpt(reCustomerInfoEditParam.getParkingLedgerId()).ifPresent(reParkLedgerInfo -> {
                    // 同时修改车位里边的客户名称和电话
                    reParkService.updateChain().set(RePark::getCustomerName, reCustomerInfoEditParam.getName()).set(RePark::getCustomerPhone, reCustomerInfoEditParam.getPhone()).eq(RePark::getId, reParkLedgerInfo.getParkId()).update();
                });
            }

        }
        BeanUtil.copyProperties(reCustomerInfoEditParam, reCustomerInfo);
        this.updateById(reCustomerInfo);
    }

    @Tran
    @Override
    public void delete(List<ReCustomerInfoIdParam> reCustomerInfoIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(reCustomerInfoIdParamList, ReCustomerInfoIdParam::getId));
    }

    @Override
    public ReCustomerInfo detail(ReCustomerInfoIdParam reCustomerInfoIdParam) {
        return this.queryEntity(reCustomerInfoIdParam.getId());
    }

    @Override
    public ReCustomerInfo queryEntity(String id) {
        ReCustomerInfo reCustomerInfo = this.getById(id);
        if (ObjectUtil.isEmpty(reCustomerInfo)) {
            throw new CommonException("客户信息不存在，id值为：{}", id);
        }
        return reCustomerInfo;
    }

    @Override
    public String getIdByName(String name) {
        if (StrUtil.isNotEmpty(name)) {
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq(ReCustomerInfo::getName, name);
            List<ReCustomerInfo> list = this.list(queryWrapper);
            if (CollectionUtil.isEmpty(list)) {
                ReCustomerInfo reCustomerInfo = new ReCustomerInfo();
                reCustomerInfo.setName(name);
                this.save(reCustomerInfo);
                return reCustomerInfo.getId();
            }else {
                return list.get(0).getId();
//                else {
//                    return list.stream().map(ReCustomerInfo::getId).collect(Collectors.joining(","));
//                }
            }
        }else {
            return "";
        }
    }

    @Override
    public String getIdByName(String name, String newStatus) {
        if (StrUtil.isNotEmpty(getIdByName(name))) {
            String id = getIdByName(name);
            if(StrUtil.isNotEmpty(newStatus)){
                ReCustomerInfo reCustomerInfo = new ReCustomerInfo();
                reCustomerInfo.setId(id);
                reCustomerInfo.setNewStatus(newStatus);
                this.updateById(reCustomerInfo);
            }
            return id;
        }else {
            return "";
        }
    }
}
