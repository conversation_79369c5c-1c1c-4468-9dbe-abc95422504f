/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import vip.xiaonuo.biz.modular.realty.entity.ReGardenContract;
import vip.xiaonuo.biz.modular.realty.param.ReGardenContractAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenContractEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenContractIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReGardenContractPageParam;

import java.util.List;

/**
 * 园区签约Service接口
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
public interface ReGardenContractService extends IService<ReGardenContract> {

    /**
     * 获取园区签约分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    Page<ReGardenContract> page(ReGardenContractPageParam reGardenContractPageParam);

    /**
     * 添加园区签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void add(ReGardenContractAddParam reGardenContractAddParam);

    /**
     * 编辑园区签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void edit(ReGardenContractEditParam reGardenContractEditParam);

    /**
     * 删除园区签约
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void delete(List<ReGardenContractIdParam> reGardenContractIdParamList);

    /**
     * 获取园区签约详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    ReGardenContract detail(ReGardenContractIdParam reGardenContractIdParam);

    /**
     * 获取园区签约详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     **/
    ReGardenContract queryEntity(String id);
}
