package vip.xiaonuo.biz.modular.realty.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.Solon;
import vip.xiaonuo.biz.modular.realty.async.ReProjectDetailServiceAsync;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.mapper.*;
import vip.xiaonuo.biz.modular.realty.param.ReBussnessLedgerInfoImportParam;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * Excel 导入监听器，用于处理商业销售台账数据导入
 * 已集成重复性检查功能
 *
 * <AUTHOR>
 * @date 2024/8/22 10:35
 * @updated Claude 4.0 sonnet 2024/12/19
 */
@Slf4j
public class ImportBussnessLedgerInfoListener extends AbstractImportListener<ReBussnessLedgerInfoImportParam> {

    private static final String HOUSE_TYPE = "2"; // 房屋类型：商业
    private static final String STATUS_PAID = "3"; // 状态：已结清
    private static final String STATUS_UNPAID = "2"; // 状态：未结清
    private static final String INVOICE_ISSUED = "1"; // 发票状态：已开发票
    private static final String INVOICE_NOT_ISSUED = "2"; // 发票状态：未开发票

    private final JSONObject res;

    private final ReHouseMapper reHouseMapper = Solon.context().getBean(ReHouseMapper.class);
    private final ReCustomerInfoMapper reCustomerInfoMapper = Solon.context().getBean(ReCustomerInfoMapper.class);
    private final ReLedgerInfoMapper reLedgerInfoMapper = Solon.context().getBean(ReLedgerInfoMapper.class);
    private final ReCustomerMapper reCustomerMapper = Solon.context().getBean(ReCustomerMapper.class);
    private final ReSalesContractMapper reSalesContractMapper = Solon.context().getBean(ReSalesContractMapper.class);
    private final RePaymentInfoMapper rePaymentInfoMapper = Solon.context().getBean(RePaymentInfoMapper.class);
    private final ReContractInfoMapper reContractInfoMapper = Solon.context().getBean(ReContractInfoMapper.class);
    private static ReProjectDetailServiceAsync reProjectDetailServiceAsync = Solon.context().getBean(ReProjectDetailServiceAsync.class);

    public ImportBussnessLedgerInfoListener(String projectId, JSONObject res) {
        super(projectId);
        this.res = res;
    }

    @Override
    public void invoke(ReBussnessLedgerInfoImportParam param, AnalysisContext context) {
        total++;
        try {
            // 🔧 统一数据验证：添加关键字段验证
            if (StrUtil.isEmpty(param.getCustomerName())) {
                log.info("跳过客户名为空的记录：第{}条", total);
                return; // 客户名为空，跳过处理
            }

            // 验证合同日期（如果有的话）
            if (param.getContractDate() == null) {
                log.info("跳过合同日期为空的记录：客户={}, 第{}条", param.getCustomerName(), total);
                return; // 合同日期为空，跳过处理
            }

            // 根据房屋信息匹配房屋
            ReHouse reHouse = findHouse(param);
            if (reHouse == null) {
                errorList.add(createQueryMap(param, "未找到房屋"));
                return;
            }

            // 🔥 新增：商业销售重复性检查
            if (checkHouseLedgerDuplicate(reHouse.getId(), param.getCustomerName(), param.getSubscriptionTime())) {
                errorList.add(createQueryMap(param, "台账记录重复，跳过导入"));
                log.info("跳过重复台账记录：房源ID={}, 客户={}, 认购时间={}",
                        reHouse.getId(), param.getCustomerName(), param.getSubscriptionTime());
                return;
            }

            // 将所有台账置为历史
            updateLedgerHistory(reHouse.getId());

            // 插入台账信息
            ReLedgerInfo reLedgerInfo = insertLedgerInfo(param, reHouse);

            // 插入客户信息
            ReCustomerInfo reCustomerInfo = insertCustomerInfo(param);

            // 插入共有人信息
            ReCustomerInfo reCustomerInfo2 = insertCoOwnerInfo(param);

            // 插入客户关系
            insertCustomerRelation(param, reHouse, reLedgerInfo, reCustomerInfo, reCustomerInfo2);

            // 插入签约信息
            ReSalesContract reSalesContract = insertSalesContract(param, reLedgerInfo);

            // 插入付款信息
            insertPaymentInfo(param, reLedgerInfo, reSalesContract);

            // 插入合同信息
            insertContractInfo(param, reLedgerInfo);

            // 更新房屋状态
            updateHouseStatus(reHouse, reLedgerInfo, reCustomerInfo, reSalesContract);

            reProjectDetailServiceAsync.updateOrInsertDetail(reHouse, projectId);
        } catch (Exception e) {
            log.error("处理数据失败: {}", e.getMessage(), e);
            errorList.add(createQueryMap(param, "系统异常:" + e.getMessage()));
        }
    }

    @Override
    protected void processData(ReBussnessLedgerInfoImportParam data) {
        // 此方法由invoke方法实现，这里留空
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (errorList.isEmpty()) {
            res.set("code", 200);
            res.set("msg", "所有导入成功");
            res.set("total",total);
        } else {
            res.set("code", 500);
            res.set("msg", "有部分数据导入失败");
            res.set("errorList", errorList);
            res.set("total",total);
        }
    }

    private ReHouse findHouse(ReBussnessLedgerInfoImportParam param) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("FLOOR", param.getFloorNumber());
        queryMap.put("BUILD_CODE", param.getBuildingNumber());
        queryMap.put("HOUSE_NUMBER", param.getRoomNumber());
        queryMap.put("PROJECT_ID", projectId);
        queryMap.put("HOUSE_TYPE", HOUSE_TYPE);
        return reHouseMapper.selectOneByMap(queryMap);
    }

    // 移除此方法，使用父类的updateLedgerHistory方法

    private ReLedgerInfo insertLedgerInfo(ReBussnessLedgerInfoImportParam param, ReHouse reHouse) {
        ReLedgerInfo reLedgerInfo = new ReLedgerInfo();
        reLedgerInfo.setHouseId(reHouse.getId());
        reLedgerInfo.setProjectId(projectId);
        reLedgerInfo.setContractType("1");
        reLedgerInfo.setBuildCode(reHouse.getBuildCode());
        reLedgerInfo.setName(param.getCustomerName());
        reLedgerInfo.setIdCard(param.getIdCardNumber());
        reLedgerInfo.setPhone(param.getPhone1());
        reLedgerInfo.setFloor(param.getFloorNumber());
        reLedgerInfo.setHouseNumber(param.getRoomNumber());
        reLedgerInfo.setSubscribeTime(param.getSubscriptionTime());
        reLedgerInfo.setContractTime(param.getContractDate());
        reLedgerInfo.setArea(parseBigDecimal(param.getArea()));
        reLedgerInfo.setContractPrice(parseBigDecimal(param.getContractTotalPrice()));
        reLedgerInfo.setHouseType(HOUSE_TYPE);
        reLedgerInfo.setIsHistory(false);
        // 🔧 修复状态判断逻辑：正确的欠款状态判断
        String debtSituation = param.getDebtSituation();
        if(StrUtil.isNotEmpty(debtSituation)){
            BigDecimal arrears = parseBigDecimal(debtSituation);
            // 正确逻辑：欠款<=0为已结清，欠款>0为未结清
            if (arrears.compareTo(BigDecimal.ZERO) <= 0) {
                reLedgerInfo.setStatus(STATUS_PAID);   // 已结清
                log.debug("设置为已结清状态：欠款={} (客户：{})", arrears, param.getCustomerName());
            } else {
                reLedgerInfo.setStatus(STATUS_UNPAID); // 未结清
                log.debug("设置为未结清状态：欠款={} (客户：{})", arrears, param.getCustomerName());
            }
        } else {
            // 如果没有欠款信息，默认设为未结清
            reLedgerInfo.setStatus(STATUS_UNPAID);
            log.debug("欠款信息为空，默认设为未结清状态 (客户：{})", param.getCustomerName());
        }
        reLedgerInfo.setTotalPayment(parseBigDecimal(param.getTotalPayment()));
        reLedgerInfoMapper.insert(reLedgerInfo);
        return reLedgerInfo;
    }

    private ReCustomerInfo insertCustomerInfo(ReBussnessLedgerInfoImportParam param) {
        ReCustomerInfo reCustomerInfo = new ReCustomerInfo();
        reCustomerInfo.setPhone(param.getPhone1());
        reCustomerInfo.setName(param.getCustomerName());
        reCustomerInfo.setIdCard(param.getIdCardNumber());
        reCustomerInfoMapper.insert(reCustomerInfo);
        return reCustomerInfo;
    }

    private ReCustomerInfo insertCoOwnerInfo(ReBussnessLedgerInfoImportParam param) {
        if (StrUtil.isEmpty(param.getCoOwner())) {
            return null;
        }
        ReCustomerInfo reCustomerInfo = new ReCustomerInfo();
        reCustomerInfo.setPhone(StrUtil.isNotEmpty(param.getPhone2()) ? param.getPhone2() : "");
        reCustomerInfo.setName(param.getCoOwner());
        reCustomerInfo.setIdCard(StrUtil.isNotEmpty(param.getCoOwnerIdCardNumber()) ? param.getCoOwnerIdCardNumber() : "");
        reCustomerInfoMapper.insert(reCustomerInfo);
        return reCustomerInfo;
    }

    private void insertCustomerRelation(ReBussnessLedgerInfoImportParam param, ReHouse reHouse, ReLedgerInfo reLedgerInfo, ReCustomerInfo reCustomerInfo, ReCustomerInfo reCustomerInfo2) {
        ReCustomer reCustomer = new ReCustomer();
        reCustomer.setAddress(param.getAddress());
        reCustomer.setCustomerId(reCustomerInfo.getId());
        reCustomer.setShareholderIds(reCustomerInfo2 == null ? "" : reCustomerInfo2.getId());
        reCustomer.setEnableArea(parseBigDecimal(param.getArea()));
        reCustomer.setHouseId(reHouse.getId());
        reCustomer.setProjectId(projectId);
        reCustomer.setSubscribeTime(param.getSubscriptionTime());
        reCustomer.setContractTime(param.getContractDate());
        reCustomer.setLedgerId(reLedgerInfo.getId());
        reCustomerMapper.insert(reCustomer);
    }

    private ReSalesContract insertSalesContract(ReBussnessLedgerInfoImportParam param, ReLedgerInfo reLedgerInfo) {
        ReSalesContract reSalesContract = new ReSalesContract();
        reSalesContract.setLedgerId(reLedgerInfo.getId());
        reSalesContract.setDiscountRemark(StrUtil.isNotEmpty(param.getDiscount()) ? param.getDiscount() : "");
        reSalesContract.setContractPrice(parseBigDecimal(param.getContractTotalPrice()));
        reSalesContract.setContractUnitPrice(parseBigDecimal(param.getContractUnitPrice()));
        reSalesContract.setTotalPrice(parseBigDecimal(param.getContractTotalPrice()).setScale(1, BigDecimal.ROUND_HALF_UP));
        reSalesContract.setMaintenanceFundUnitPrice(parseBigDecimal(param.getMaintenanceFund()).divide(parseBigDecimal(param.getArea()), 2, RoundingMode.HALF_UP));
        reSalesContract.setMaintenanceFund(parseBigDecimal(param.getMaintenanceFund()));
        reSalesContract.setUnitPrice(parseBigDecimal(param.getContractUnitPrice()));
        reSalesContract.setTotalHousePrice(parseBigDecimal(param.getTotalHousePayment()).setScale(1, BigDecimal.ROUND_HALF_UP));
        reSalesContract.setFirstPayment(parseBigDecimal(param.getDownPayment()));
        reSalesContract.setLoan(parseBigDecimal(param.getLoanAmount()));
        reSalesContract.setLoanTime(param.getLoanProcessingTime());
        reSalesContract.setLoanReturnTime(param.getLoanRepaymentTime());
        reSalesContract.setEarnestMoney(parseBigDecimal(param.getDeposit()));
        if (param.getPaymentMethod().contains("按揭")){
            reSalesContract.setPaymentMethod("3");
        }else if (param.getPaymentMethod().contains("分期")){
            reSalesContract.setPaymentMethod("2");
        }else if (param.getPaymentMethod().contains("一次性")){
            reSalesContract.setPaymentMethod("1");
        }
        reSalesContractMapper.insert(reSalesContract);
        return reSalesContract;
    }

    private void insertPaymentInfo(ReBussnessLedgerInfoImportParam param, ReLedgerInfo reLedgerInfo, ReSalesContract reSalesContract) {
        if (StrUtil.isNotEmpty(param.getDeposit())) {
            RePaymentInfo rePaymentInfo = new RePaymentInfo();
            rePaymentInfo.setPaymentType("1");
            rePaymentInfo.setLedgerId(reLedgerInfo.getId());
            rePaymentInfo.setContractId(reSalesContract.getId());
            rePaymentInfo.setPaymentAmount(parseBigDecimal(param.getDeposit()));
            rePaymentInfo.setPaymentTime(param.getDepositPaymentTime());
            rePaymentInfoMapper.insert(rePaymentInfo);
        }
        if (StrUtil.isNotEmpty(param.getSecondPayment())) {
            rePaymentInfoMapper.insert(createPaymentInfo(parseBigDecimal(param.getSecondPayment()), reLedgerInfo.getId(), reSalesContract.getId(), param.getSecondPaymentTime()));
        }
        if (StrUtil.isNotEmpty(param.getThirdPayment())) {
            rePaymentInfoMapper.insert(createPaymentInfo(parseBigDecimal(param.getThirdPayment()), reLedgerInfo.getId(), reSalesContract.getId(), param.getThirdPaymentTime()));
        }
        if (StrUtil.isNotEmpty(param.getFourthPayment())) {
            rePaymentInfoMapper.insert(createPaymentInfo(parseBigDecimal(param.getFourthPayment()), reLedgerInfo.getId(), reSalesContract.getId(), param.getFourthPaymentTime()));
        }
        if (StrUtil.isNotEmpty(param.getFivePayment())) {
            rePaymentInfoMapper.insert(createPaymentInfo(parseBigDecimal(param.getFivePayment()), reLedgerInfo.getId(), reSalesContract.getId(), param.getFivePaymentTime()));
        }
        if (StrUtil.isNotEmpty(param.getMaintenanceFee())) {
            RePaymentInfo rePaymentInfo = new RePaymentInfo();
            rePaymentInfo.setPaymentType("3");
            rePaymentInfo.setLedgerId(reLedgerInfo.getId());
            rePaymentInfo.setContractId(reSalesContract.getId());
            rePaymentInfo.setPaymentAmount(parseBigDecimal(param.getMaintenanceFee()));
            rePaymentInfo.setPaymentTime(param.getMaintenanceFundPaymentTime());
            rePaymentInfoMapper.insert(rePaymentInfo);
        }
        if (StrUtil.isNotEmpty(param.getLoanRepayment())) {
            RePaymentInfo rePaymentInfo = new RePaymentInfo();
            rePaymentInfo.setPaymentType("5");
            rePaymentInfo.setLedgerId(reLedgerInfo.getId());
            rePaymentInfo.setContractId(reSalesContract.getId());
            rePaymentInfo.setPaymentAmount(parseBigDecimal(param.getLoanRepayment()));
            rePaymentInfo.setPaymentTime(param.getLoanRepaymentTime());
            rePaymentInfoMapper.insert(rePaymentInfo);
        }
    }

    private void insertContractInfo(ReBussnessLedgerInfoImportParam param, ReLedgerInfo reLedgerInfo) {
        ReContractInfo reContractInfo = new ReContractInfo();
        reContractInfo.setLedgerId(reLedgerInfo.getId());
        reContractInfo.setInvoiceStatus("已开发票".equals(param.getInvoice()) ? INVOICE_ISSUED : INVOICE_NOT_ISSUED);
        reContractInfo.setPropertyConsultant(StrUtil.isNotEmpty(param.getConsultant()) ? param.getConsultant() : "");
        reContractInfo.setRemark(StrUtil.isNotEmpty(param.getRemarks()) ? param.getRemarks() : "");
        //计算合同日期
        if (param.getContractNumber() != null) {
            reContractInfo.setContractNumber(param.getContractNumber());
            try {
                String[] split = param.getContractNumber().split("-");
                if (split.length >= 2) {
                    Calendar calendar = Calendar.getInstance();
                    int year = Integer.parseInt(split[0]);
                    // 确保月份字符串至少有两位
                    String monthPart = split[1].length() >= 2 ? split[1].substring(0, 2) : split[1];
                    int month = Integer.parseInt(monthPart);

                    // 确保日期部分存在
                    int day = 1; // 默认值
                    if (split[1].length() >= 4) {
                        day = Integer.parseInt(split[1].substring(2, 4));
                    }

                    // 验证日期有效性
                    if (month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                        calendar.set(Calendar.YEAR, year);
                        calendar.set(Calendar.MONTH, month - 1); // 月份从0开始，需要减1
                        calendar.set(Calendar.DAY_OF_MONTH, day);

                        // 验证日期是否有效（例如2月30日是无效的）
                        int actualMonth = calendar.get(Calendar.MONTH);
                        if (actualMonth == month - 1) { // 检查月份是否被Calendar自动调整
                            reContractInfo.setContractRecordTime(calendar.getTime());
                        } else {
                            // 日期无效，可以记录日志或采取其他措施

                        }
                    }
                }
            } catch (NumberFormatException e) {
                // 处理数字解析异常

            }
        }
        reContractInfoMapper.insert(reContractInfo);
    }

    private void updateHouseStatus(ReHouse reHouse, ReLedgerInfo reLedgerInfo, ReCustomerInfo reCustomerInfo, ReSalesContract reSalesContract) {
        reHouse.setStatus(reLedgerInfo.getStatus());
        reHouse.setCustomerPhone(reCustomerInfo.getPhone());
        reHouse.setCustomerName(reCustomerInfo.getName());
        reHouse.setSalesUnitPrice(reSalesContract.getUnitPrice());
        reHouse.setSalesTotalPrice(reSalesContract.getTotalPrice());
        reHouseMapper.update(reHouse);
    }

    private RePaymentInfo createPaymentInfo(BigDecimal money, String ledgerId, String contractId, Date paymentTime) {
        RePaymentInfo rePaymentInfo = new RePaymentInfo();
        rePaymentInfo.setPaymentType("2");
        rePaymentInfo.setLedgerId(ledgerId);
        rePaymentInfo.setContractId(contractId);
        rePaymentInfo.setPaymentAmount(money);
        rePaymentInfo.setPaymentTime(paymentTime);
        return rePaymentInfo;
    }

    // 移除此方法，使用父类的parseBigDecimal方法

    private Map<String, Object> createQueryMap(ReBussnessLedgerInfoImportParam param,String msg) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("楼层", param.getFloorNumber());
        queryMap.put("楼号", param.getBuildingNumber());
        queryMap.put("房号", param.getRoomNumber());
        queryMap.put("项目ID", projectId);
        queryMap.put("房屋类型", HOUSE_TYPE);
        queryMap.put("错误信息", msg);
        return queryMap;
    }
}
