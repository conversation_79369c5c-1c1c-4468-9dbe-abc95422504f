/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import vip.xiaonuo.common.pojo.CommonEntity;

import java.math.BigDecimal;

/**
 * 房屋管理实体
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
@Getter
@Setter
@Accessors(chain = true)
@Table(value = "re_house")
public class ReHouse extends CommonEntity {

    /** ID */
    @Id
    @ApiModelProperty(value = "ID", position = 1)
    private String id;

    /** 楼号 */
    @ApiModelProperty(value = "楼号", position = 2)
    private String buildId;

    /** 楼栋编号 */
    @ApiModelProperty(value = "楼栋编号", position = 3)
    private String buildCode;

    /** 楼层 */
    @ApiModelProperty(value = "楼层", position = 4)
    private String floor;

    /** 单元 */
    @ApiModelProperty(value = "单元", position = 5)
    private String unit;

    /** 房屋编号 */
    @ApiModelProperty(value = "房屋编号", position = 6)
    private String houseNumber;

    /** 房屋类型--字典（住宅、商业、储藏间） */
    @ApiModelProperty(value = "房屋类型--字典（住宅、商业、储藏间）", position = 7)
    private String houseType;

    /** 预测建筑面积 */
    @ApiModelProperty(value = "预测建筑面积", position = 8)
    private BigDecimal forecastBuildArea;

    /** 预测套内面积 */
    @ApiModelProperty(value = "预测套内面积", position = 9)
    private BigDecimal forecastHouseArea;

    /** 实测建筑面积 */
    @ApiModelProperty(value = "实测建筑面积", position = 10)
    private BigDecimal actualBuildArea;

    /** 实测套内面积 */
    @ApiModelProperty(value = "实测套内面积", position = 11)
    private BigDecimal actualHouseArea;

    /** 房屋朝向--字典 */
    @ApiModelProperty(value = "房屋朝向--字典", position = 12)
    private String houseOrientation;

    /** 房屋户型--字典 */
    @ApiModelProperty(value = "房屋户型--字典", position = 13)
    private String houseLayout;

    /** 安置价 */
    @ApiModelProperty(value = "安置价", position = 14)
    private BigDecimal placementPrice;

    /** 市场价 */
    @ApiModelProperty(value = "市场价", position = 15)
    private BigDecimal marketPrice;

    /** 房源位置 */
    @ApiModelProperty(value = "房源位置", position = 16)
    private String houseLocation;

    /** 总价--储藏室使用 */
    @ApiModelProperty(value = "总价--储藏室使用", position = 17)
    private BigDecimal totalPrice;

    /** 单价--储藏室使用 */
    @ApiModelProperty(value = "单价--储藏室使用", position = 18)
    private BigDecimal unitPrice;

    /** 项目编号 */
    @ApiModelProperty(value = "项目编号", position = 19)
    private String projectId;

    /** 房屋状态--字典（待售、售出-未结、售出-已结，租赁） */
    @ApiModelProperty(value = "房屋状态--字典（待售、售出-未结、售出-已结，租赁）", position = 20)
    private String status;

    /** 客户姓名 */
    @ApiModelProperty(value = "客户姓名", position = 21)
    private String customerName;

    /** 客户电话 */
    @ApiModelProperty(value = "客户电话", position = 22)
    private String customerPhone;

    /** 销售单价 */
    @ApiModelProperty(value = "销售单价", position = 23)
    private BigDecimal salesUnitPrice;

    /** 销售总价 */
    @ApiModelProperty(value = "销售总价", position = 24)
    private BigDecimal salesTotalPrice;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息", position = 25)
    private String extJson;

    /** 签约状态 **/
    @ApiModelProperty(value = "签约状态", position = 31)
    @Column(ignore = true)
    private String contractStatus;

    /** 租赁总价 **/
    @ApiModelProperty(value = "租赁总价", position = 32)
    private BigDecimal leaseTotalPrice;

    /** 租赁单价 **/
    @ApiModelProperty(value = "租赁单价", position = 33)
    private BigDecimal leaseUnitPrice;
}
