/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.afterturn.easypoi.cache.manager.POICacheManager;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.core.handle.Context;
import org.noear.solon.core.handle.UploadedFile;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.ReBuilding;
import vip.xiaonuo.biz.modular.realty.entity.ReGardenLedgerInfo;
import vip.xiaonuo.biz.modular.realty.mapper.ReBuildingMapper;
import vip.xiaonuo.biz.modular.realty.mapper.ReGardenLedgerInfoMapper;
import vip.xiaonuo.biz.modular.realty.param.*;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.biz.modular.realty.entity.ReGardenHouse;
import vip.xiaonuo.biz.modular.realty.mapper.ReGardenHouseMapper;
import vip.xiaonuo.biz.modular.realty.service.ReGardenHouseService;
import vip.xiaonuo.common.util.CommonDownloadUtil;

import java.io.File;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 园区房屋管理Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/08/17 14:34
 **/

@Slf4j
@Component
public class ReGardenHouseServiceImpl extends ServiceImpl<ReGardenHouseMapper, ReGardenHouse> implements ReGardenHouseService {

    @Inject
    private ReGardenLedgerInfoMapper reGardenLedgerInfoMapper;

    @Inject
    private ReBuildingMapper buildingMapper;


    QueryWrapper buildQueryWrapper(ReGardenHousePageParam reGardenHousePageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        //排序
        if (ObjectUtil.isAllNotEmpty(reGardenHousePageParam.getSortField(), reGardenHousePageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reGardenHousePageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reGardenHousePageParam.getSortField()), reGardenHousePageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReGardenHouse::getId);
        }
        //查询字段
        if (StrUtil.isNotEmpty(reGardenHousePageParam.getHouseType())) {
            queryWrapper.eq(ReGardenHouse::getHouseType, reGardenHousePageParam.getHouseType());
        }
        if (StrUtil.isNotEmpty(reGardenHousePageParam.getHouseNum())) {
            queryWrapper.eq(ReGardenHouse::getHouseNum, reGardenHousePageParam.getHouseNum());
        }
        if (StrUtil.isNotEmpty(reGardenHousePageParam.getHouseStatus())) {
            queryWrapper.eq(ReGardenHouse::getStatus, reGardenHousePageParam.getHouseStatus());
        }
        if (StrUtil.isNotEmpty(reGardenHousePageParam.getCustomerName())) {
            queryWrapper.like(ReGardenHouse::getCustomerName, reGardenHousePageParam.getCustomerName());
        }
        if (StrUtil.isNotEmpty(reGardenHousePageParam.getBuildId())) {
            queryWrapper.eq(ReGardenHouse::getBuildId, reGardenHousePageParam.getBuildId());
        }
        if (StrUtil.isNotEmpty(reGardenHousePageParam.getFloor())) {
            queryWrapper.eq(ReGardenHouse::getFloor, reGardenHousePageParam.getFloor());
        }
        if (StrUtil.isNotEmpty(reGardenHousePageParam.getBuildCode())) {
            List<String> collect = buildingMapper.selectListByQuery(new QueryWrapper().eq("BUILD_CODE", reGardenHousePageParam.getBuildCode()))
                    .stream()
                    .map(ReBuilding::getId)
                    .collect(Collectors.toList());
            queryWrapper.in(ReGardenHouse::getBuildId, collect);
        }
        return queryWrapper;
    }


    @Override
    public Page<ReGardenHouse> page(ReGardenHousePageParam reGardenHousePageParam) {
        QueryWrapper queryWrapper = buildQueryWrapper(reGardenHousePageParam);
        Page<ReGardenHouse> page = this.page(CommonPageRequest.defaultPage(), queryWrapper);
        page.getRecords().forEach(f ->{
            //查询台账信息
            QueryWrapper ledgerQueryWrapper = new QueryWrapper();
            ledgerQueryWrapper.eq("HOUSE_ID", f.getId());
            ledgerQueryWrapper.eq("IS_HISTORY", false);
            ReGardenLedgerInfo reGardenLedgerInfo = reGardenLedgerInfoMapper.selectOneByQuery(ledgerQueryWrapper);
            if (reGardenLedgerInfo != null) {
                f.setContractStatus("0");
            }else {
                f.setContractStatus("2");
            }
        });
        return page;
    }

    @Tran
    @Override
    public void add(ReGardenHouseAddParam reGardenHouseAddParam) {
        ReGardenHouse reGardenHouse = BeanUtil.toBean(reGardenHouseAddParam, ReGardenHouse.class);
        this.save(reGardenHouse);
    }

    @Tran
    @Override
    public void edit(ReGardenHouseEditParam reGardenHouseEditParam) {
        ReGardenHouse reGardenHouse = this.queryEntity(reGardenHouseEditParam.getId());
        BeanUtil.copyProperties(reGardenHouseEditParam, reGardenHouse);
        this.updateById(reGardenHouse);
        //更新台账中的房屋信息
        ReGardenLedgerInfo bean = new ReGardenLedgerInfo();
        bean.setCorridorArea(reGardenHouse.getCorridorArea());
        bean.setFloor(reGardenHouse.getFloor());
        bean.setHouseNumber(reGardenHouse.getHouseNum());
        bean.setArea(reGardenHouse.getBuildArea());
        bean.setHouseType(reGardenHouse.getHouseType());
        bean.setStatus(reGardenHouse.getStatus());
        bean.setProjectId(reGardenHouse.getProjectId());

        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("HOUSE_ID", reGardenHouse.getId());
        reGardenLedgerInfoMapper.updateByQuery(bean, queryWrapper);

    }

    @Tran
    @Override
    public void delete(List<ReGardenHouseIdParam> reGardenHouseIdParamList) {
        // 执行删除
        //todo 删除校验
        this.removeByIds(CollStreamUtil.toList(reGardenHouseIdParamList, ReGardenHouseIdParam::getId));
    }

    @Override
    public ReGardenHouse detail(ReGardenHouseIdParam reGardenHouseIdParam) {
        return this.queryEntity(reGardenHouseIdParam.getId());
    }

    @Override
    public ReGardenHouse queryEntity(String id) {
        ReGardenHouse reGardenHouse = this.getById(id);
        if (ObjectUtil.isEmpty(reGardenHouse)) {
            throw new CommonException("园区房屋管理不存在，id值为：{}", id);
        }
        return reGardenHouse;
    }


    @Tran
    @Override
    public JSONObject importExcel(UploadedFile file,ReGardenHouseIdParam reGardenHouseIdParam) {
        String projectId = reGardenHouseIdParam.getProjectId();
        if (StrUtil.isEmpty(projectId)) {
            throw new CommonException("项目ID不能为空");
        }
        String buildId = reGardenHouseIdParam.getBuildId();
        if (StrUtil.isEmpty(buildId)) {
            throw new CommonException("楼栋ID不能为空");
        }
        String houseType = reGardenHouseIdParam.getHouseType();
        if (StrUtil.isEmpty(houseType)) {
            throw new CommonException("房屋类型不能为空");
        }
        try {
            int successCount = 0;
            int errorCount = 0;
            JSONArray errorDetail = JSONUtil.createArray();
            // 创建临时文件
            File tempFile = FileUtil.writeBytes(IoUtil.readBytes(file.getContent()), FileUtil.file(FileUtil.getTmpDir() +
                    FileUtil.FILE_SEPARATOR + "gardenHouseTemplate.xlsx"));
            // 读取excel
            List<ReGardenHouseImportParam> reGardenHouseImportParams = EasyExcel.read(tempFile).head(ReGardenHouseImportParam.class).sheet()
                    .headRowNumber(1).doReadSync();
            for (int i = 0; i < reGardenHouseImportParams.size(); i++) {
                ReGardenHouseImportParam reGardenHouseImportParam = reGardenHouseImportParams.get(i);
                reGardenHouseImportParam.setProjectId(projectId);
                reGardenHouseImportParam.setBuildId(buildId);
                reGardenHouseImportParam.setHouseType(houseType);
                //查询是否已存在
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("HOUSE_NUM", reGardenHouseImportParam.getHouseNum());
                queryWrapper.eq("BUILD_ID", buildId);
                queryWrapper.eq("floor", reGardenHouseImportParam.getFloor());
                queryWrapper.eq("PROJECT_ID", projectId);
                queryWrapper.eq("HOUSE_TYPE", houseType);
                ReGardenHouse reGardenHouse = this.mapper.selectOneByQuery(queryWrapper);
                JSONObject jsonObject = null;
                if (reGardenHouse != null){
                    //存在则更新
                    ReGardenHouseEditParam bean = BeanUtil.toBean(reGardenHouseImportParam, ReGardenHouseEditParam.class);
                    bean.setId(reGardenHouse.getId());
                    this.edit(bean);
                    jsonObject = JSONUtil.createObj().set("success", true);
                }else {
                     jsonObject = this.doImport(reGardenHouseImportParam, i);
                }
                if (jsonObject.getBool("success")) {
                    successCount += 1;
                } else {
                    errorCount += 1;
                    errorDetail.add(jsonObject);
                }
            }
            return JSONUtil.createObj()
                    .set("totalCount", reGardenHouseImportParams.size())
                    .set("successCount", successCount)
                    .set("errorCount", errorCount)
                    .set("errorDetail", errorDetail);
        } catch (Exception e) {
            log.error(">>> 导入失败：", e);
            throw new CommonException("导入失败");
        }
    }

    @Override
    public JSONObject soldAndUnsoldArea(ReGardenHouseIdParam reGardenHouseIdParam) {
        String buildId = reGardenHouseIdParam.getBuildId();
        String houseType = reGardenHouseIdParam.getHouseType();
        String projectId = reGardenHouseIdParam.getProjectId();

        JSONObject result = new JSONObject();
        try {
            Map<Long, Map<String, Object>> queryResult = this.mapper.selectSoldAndUnsoldArea(buildId,houseType,projectId);
            queryResult.forEach((k, v) -> {
                result.set("soldArea", v.get("soldArea"));
                result.set("unsoldArea", v.get("unsoldArea"));
            });
        } catch (Exception e) {
            log.error("获取房屋ID的已售出和未售出面积时出错：{}", buildId, e);
            throw new CommonException("获取房屋ID的已售出和未售出面积时出错");
        }
        return result;
    }

    @Override
    public void downloadTemplate(Context context) {
        try {
            InputStream inputStream = POICacheManager.getFile("gardenHouseTemplate.xlsx");
            byte[] bytes = IoUtil.readBytes(inputStream);
            CommonDownloadUtil.download("园区房源导入模板.xlsx", bytes, context);
        } catch (Exception e) {
            log.error(">>> 下载模板失败：", e);
            throw new CommonException("下载模板失败");
        }
    }

    private JSONObject doImport(ReGardenHouseImportParam reGardenHouseImportParam, int i) {
        if (this.save(BeanUtil.toBean(reGardenHouseImportParam, ReGardenHouse.class))) {
            return JSONUtil.createObj().set("success", true);
        } else {
            return JSONUtil.createObj().set("success", false).set("rowIndex", i + 1);
        }
    }
}
