/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReCustomer;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReCustomerPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReCustomerService;

/**
 * 客户管理控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 */
@Api(tags = "客户管理控制器")
@Controller
@Valid
public class ReCustomerController {

    @Inject
    private ReCustomerService reCustomerService;

    /**
     * 获取客户管理分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取客户管理分页")
    @SaCheckPermission("/biz/recustomer/page")
    @Get
    @Mapping("/biz/recustomer/page")
    public CommonResult<Page<ReCustomer>> page(ReCustomerPageParam reCustomerPageParam) {
        return CommonResult.data(reCustomerService.page(reCustomerPageParam));
    }

    /**
     * 添加客户管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("添加客户管理")
    @CommonLog("添加客户管理")
    @SaCheckPermission("/biz/recustomer/add")
    @Post
    @Mapping("/biz/recustomer/add")
    public CommonResult<String> add(ReCustomerAddParam reCustomerAddParam) {
        reCustomerService.add(reCustomerAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑客户管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("编辑客户管理")
    @CommonLog("编辑客户管理")
    @SaCheckPermission("/biz/recustomer/edit")
    @Post
    @Mapping("/biz/recustomer/edit")
    public CommonResult<String> edit(ReCustomerEditParam reCustomerEditParam) {
        reCustomerService.edit(reCustomerEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除客户管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("删除客户管理")
    @CommonLog("删除客户管理")
    @SaCheckPermission("/biz/recustomer/delete")
    @Post
    @Mapping("/biz/recustomer/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReCustomerIdParam> reCustomerIdParamList) {
        reCustomerService.delete(reCustomerIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取客户管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取客户管理详情")
    @SaCheckPermission("/biz/recustomer/detail")
    @Get
    @Mapping("/biz/recustomer/detail")
    public CommonResult<ReCustomer> detail(ReCustomerIdParam reCustomerIdParam) {
        return CommonResult.data(reCustomerService.detail(reCustomerIdParam));
    }
}
