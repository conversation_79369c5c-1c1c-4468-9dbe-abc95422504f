/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.common.pojo.CommonEntity;

import java.math.BigDecimal;

/**
 * 园区其他信息实体
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
@Getter
@Setter
@Table(value = "re_garden_other_info")
public class ReGardenOtherInfo extends CommonEntity {

    /** ID */
    @Id
    @ApiModelProperty(value = "ID", position = 1)
    private String id;

    /** 企业优惠政策 */
    @ApiModelProperty(value = "企业优惠政策", position = 2)
    private String companyPolicy;

    /** 经营产品 */
    @ApiModelProperty(value = "经营产品", position = 3)
    private String businessProduct;

    /** 投资额 */
    @ApiModelProperty(value = "投资额", position = 4)
    private BigDecimal investmentAmount;

    /** 员工人数 */
    @ApiModelProperty(value = "员工人数", position = 5)
    private Integer staffNumber;

    /** 年营业额 */
    @ApiModelProperty(value = "年营业额", position = 6)
    private BigDecimal annualTurnover;

    /** 配套需求 */
    @ApiModelProperty(value = "配套需求", position = 7)
    private String supportingRequirements;

    /** 存在问题 */
    @ApiModelProperty(value = "存在问题", position = 8)
    private String existingProblems;

    /** 台账id */
    @ApiModelProperty(value = "台账id", position = 9)
    private String ledgerId;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息", position = 10)
    private String extJson;

}
