package vip.xiaonuo.biz.modular.realty.entity;

import lombok.Data;

/**
 * 车位导出实体
 * 该实体类用于车位台账导出
 * <AUTHOR>
 * @date 2024/10/11 15:25
 */

@Data
public class ReParkLedgerExport {

    /** 序号 */
    private int index1;

    /** 村落编号 */
    private String villageId;

    /** 选房序号 */
    private String code;

    /** 客户姓名 */
    private String name;

    /** 身份证号 */
    private String idCard;

    /** 客户电话 */
    private String phone;

    /** 签约时间 */
    private String contractTime;

    /** 认购时间 */
    private String subscribeTime;

    /** 车位区域 */
    private String areaCode;

    /** 车位类别 */
    private String type;

    /** 车位分区 */
    private String parkPartition;

    /** 车位编号 */
    private String reParkCode;

    /** 车位总价 */
    private Double totalPrice;

    /** 执行优惠 */
    private Double discount;
    
    /** 优惠备注 */
    private String remark;
    
    /** 签约总价 */
    private Double contractPrice;
    
    /** 合计交款 */
    private Double totalPayment;
    
    /** 欠款金额 */
    private Double debtPriec;
    
    /** 结清状态 */
    private String debtStatus;


}
