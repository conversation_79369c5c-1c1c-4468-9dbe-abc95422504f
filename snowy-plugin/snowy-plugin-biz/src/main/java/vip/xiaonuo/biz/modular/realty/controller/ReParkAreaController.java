/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;
import vip.xiaonuo.biz.modular.realty.entity.ReParkArea;
import vip.xiaonuo.biz.modular.realty.param.ReParkAreaAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkAreaEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkAreaIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReParkAreaPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReParkAreaService;

/**
 * 车位区域管理控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 */
@Api(tags = "车位区域管理控制器")
@Controller
@Valid
public class ReParkAreaController {

    @Inject
    private ReParkAreaService reParkAreaService;

    /**
     * 获取车位区域管理分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取车位区域管理分页")
    @SaCheckPermission("/biz/reparkarea/page")
    @Get
    @Mapping("/biz/reparkarea/page")
    public CommonResult<Page<ReParkArea>> page(ReParkAreaPageParam reParkAreaPageParam) {
        return CommonResult.data(reParkAreaService.page(reParkAreaPageParam));
    }

    /**
     * 添加车位区域管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("添加车位区域管理")
    @CommonLog("添加车位区域管理")
    @SaCheckPermission("/biz/reparkarea/add")
    @Post
    @Mapping("/biz/reparkarea/add")
    public CommonResult<String> add(ReParkAreaAddParam reParkAreaAddParam) {
        reParkAreaService.add(reParkAreaAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑车位区域管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("编辑车位区域管理")
    @CommonLog("编辑车位区域管理")
    @SaCheckPermission("/biz/reparkarea/edit")
    @Post
    @Mapping("/biz/reparkarea/edit")
    public CommonResult<String> edit(ReParkAreaEditParam reParkAreaEditParam) {
        reParkAreaService.edit(reParkAreaEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除车位区域管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("删除车位区域管理")
    @CommonLog("删除车位区域管理")
    @SaCheckPermission("/biz/reparkarea/delete")
    @Post
    @Mapping("/biz/reparkarea/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReParkAreaIdParam> reParkAreaIdParamList) {
        reParkAreaService.delete(reParkAreaIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取车位区域管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    @ApiOperation("获取车位区域管理详情")
    @SaCheckPermission("/biz/reparkarea/detail")
    @Get
    @Mapping("/biz/reparkarea/detail")
    public CommonResult<ReParkArea> detail(ReParkAreaIdParam reParkAreaIdParam) {
        return CommonResult.data(reParkAreaService.detail(reParkAreaIdParam));
    }
}
