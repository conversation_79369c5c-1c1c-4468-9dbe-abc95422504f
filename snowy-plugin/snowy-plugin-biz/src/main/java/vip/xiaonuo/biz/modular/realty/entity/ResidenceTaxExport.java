package vip.xiaonuo.biz.modular.realty.entity;

import lombok.Data;

/**
 * 住宅税务汇报导出实体
 *
 * <AUTHOR>
 * @date 2024/10/11 17:32
 */
@Data
public class ResidenceTaxExport {

    /** 预售许可证编号 **/
    private String presalePermitNumber;

    /** 楼号 **/
    private String buildCode;

    /** 单元号 **/
    private String unit;

    /** 楼层 **/
    private String floor;

    /** 房号 **/
    private String houseNumber;

    /** 预测建筑面积 **/
    private double forecastBuildArea;

    /** 预测套内面积 **/
    private double forecastHouseArea;

    /** 实测建筑面积 **/
    private double actualBuildArea;

    /** 实测套内面积 **/
    private double actualHouseArea;

    /** 朝向 **/
    private String houseOrientation;

    /** 结构 **/
    private String houseLayout;

    /** 网签合同编号 **/
    private String contractNumber;

    /** 合同金额 **/
    private Double contractPrice;

    /** 合同签订时间  **/
    private String contractRecordTime;

    /** 购房人姓名 **/
    private String name;

    /** 购房人身份证号 **/
    private String idCard;


}
