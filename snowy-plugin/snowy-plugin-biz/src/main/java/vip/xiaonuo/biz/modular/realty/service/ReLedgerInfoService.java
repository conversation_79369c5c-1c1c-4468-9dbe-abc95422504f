/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service;

import cn.hutool.json.JSONObject;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import org.noear.solon.core.handle.UploadedFile;
import vip.xiaonuo.biz.modular.realty.entity.ReLedgerInfo;
import vip.xiaonuo.biz.modular.realty.param.*;

import java.util.List;

/**
 * 房屋台账信息Service接口
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
public interface ReLedgerInfoService extends IService<ReLedgerInfo> {

    /**
     * 获取房屋台账信息分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    Page<ReLedgerInfo> page(ReLedgerInfoPageParam reLedgerInfoPageParam);

    /**
     * 添加房屋台账信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void add(ReLedgerInfoAddParam reLedgerInfoAddParam);

    /**
     * 编辑房屋台账信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void edit(ReLedgerInfoEditParam reLedgerInfoEditParam);

    /**
     * 删除房屋台账信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    void delete(List<ReLedgerInfoIdParam> reLedgerInfoIdParamList);

    /**
     * 获取房屋台账信息详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    ReLedgerInfo detail(ReLedgerInfoIdParam reLedgerInfoIdParam);

    /**
     * 获取房屋台账信息详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     **/
    ReLedgerInfo queryEntity(String id);

    /**
     * 通过销控新增房屋台账信息
     *
     * <AUTHOR>
     * @date 2024/8/20 10:11
     */
    void addBySaleControl(ReLedgerInfoXiaoKongParam reLedgerInfoXiaoKongParam);

    /**
     * 通过房屋id查询台账信息
     *
     * <AUTHOR>
     * @date 2024/8/20 14:42
     */
    ReLedgerInfoXiaoKongParamVo queryEntityByHouseId(String id);

    /**
     * 通过台账id查询详情
     *
     * <AUTHOR>
     * @date 2024/9/20 16:51
     */
    ReLedgerInfoXiaoKongParamVo queryEntityById(String id);

    JSONObject imporBussinesstExcel(UploadedFile file, String projectId);

    JSONObject importStoreroomExcel(UploadedFile file, String projectId);

    JSONObject importLeaseBusinessExcel(UploadedFile file, String projectId);
}
