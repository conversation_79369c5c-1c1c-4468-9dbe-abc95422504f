package vip.xiaonuo.biz.modular.realty.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.Solon;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.mapper.*;
import vip.xiaonuo.biz.modular.realty.param.ReParkLedgerInfoImportParam;
import vip.xiaonuo.biz.modular.realty.service.RePaymentInfoService;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import vip.xiaonuo.biz.modular.realty.constants.LedgerConstants;

/**
 * Excel 导入监听器，用于处理车位台账数据导入
 * 已集成重复性检查功能
 *
 * <AUTHOR>
 * @date 2024/8/22 10:35
 * @updated Claude 4.0 sonnet 2024/12/19
 */
@Slf4j
public class ImportParkLedgerInfoListener extends AbstractImportListener<ReParkLedgerInfoImportParam> {

    private static final int BATCH_SIZE = 500;

    private final JSONArray carType;
    private final List<ReParkLedgerInfoImportParam> loadSet = new ArrayList<>();
    private final AtomicInteger processedCount = new AtomicInteger(0);
    private final AtomicInteger errorCount = new AtomicInteger(0);
    private final CountDownLatch latch = new CountDownLatch(1);
    private final ExecutorService executorService;

    private static final RePaymentInfoService rePaymentInfoService = Solon.context().getBean(RePaymentInfoService.class);
    private final ReCustomerMapper reCustomerMapper = Solon.context().getBean(ReCustomerMapper.class);
    private final ReCustomerInfoMapper reCustomerInfoMapper = Solon.context().getBean(ReCustomerInfoMapper.class);
    private final ReParkMapper reParkMapper = Solon.context().getBean(ReParkMapper.class);
    private final ReParkAreaMapper reParkAreaMapper = Solon.context().getBean(ReParkAreaMapper.class);
    private final ReParkContractMapper reParkContractMapper = Solon.context().getBean(ReParkContractMapper.class);
    private final ReParkLedgerInfoMapper reParkLedgerInfoMapper = Solon.context().getBean(ReParkLedgerInfoMapper.class);

    public ImportParkLedgerInfoListener(String projectId, JSONArray carType) {
        super(projectId);
        validateInputs(projectId, carType);
        this.carType = carType;
        // 创建线程池但配置更合理
        this.executorService = new ThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors(),
                Runtime.getRuntime().availableProcessors() * 2,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(5000),
                Executors.defaultThreadFactory(),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }

    private void validateInputs(String projectId, JSONArray carType) {
        Objects.requireNonNull(projectId, "项目ID不能为空");
        Objects.requireNonNull(carType, "车辆类型不能为空");
        if (carType.isEmpty()) {
            throw new IllegalArgumentException("车辆类型列表不能为空");
        }
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception {
        log.error("解析出错: {}, 行号: {}", exception.getMessage(),
                context.readRowHolder().getRowIndex() + 1, exception);
        errorCount.incrementAndGet();
    }

    @Override
    public void invoke(ReParkLedgerInfoImportParam param, AnalysisContext context) {
        if (isValidParam(param)) {
            log.debug("读取到了数据: {}", param);
            synchronized (loadSet) {
                loadSet.add(param);
                if (loadSet.size() >= BATCH_SIZE) {
                    List<ReParkLedgerInfoImportParam> batchData = new ArrayList<>(loadSet);
                    loadSet.clear();
                    processDataBatchAsync(batchData);
                }
            }
        } else {
            log.warn("数据不完整，跳过处理: {}, 行号: {}", param,
                    context.readRowHolder().getRowIndex() + 1);
            errorCount.incrementAndGet();
        }
    }

    private boolean isValidParam(ReParkLedgerInfoImportParam param) {
        return StrUtil.isNotEmpty(param.getName()) &&
                StrUtil.isNotEmpty(param.getParkingNumber()) &&
                StrUtil.isNotEmpty(param.getParkingArea());
    }

    private void processDataBatchAsync(List<ReParkLedgerInfoImportParam> batchData) {
        CompletableFuture.runAsync(() -> {
            for (ReParkLedgerInfoImportParam data : batchData) {
                processIndividualDataWithErrorHandling(data);
            }
        }, executorService);
    }

    private void processIndividualDataWithErrorHandling(ReParkLedgerInfoImportParam data) {
        try {
            processIndividualData(data);
            processedCount.incrementAndGet();
        } catch (Exception e) {
            log.error("处理数据失败: {}", data, e);
            errorCount.incrementAndGet();
        }
    }

    @Tran
    private void processIndividualData(ReParkLedgerInfoImportParam data) {
        // 处理车位区域
        ReParkArea reParkArea = findOrCreateParkArea(data);

        if (reParkArea == null) {
            errorCount.incrementAndGet();
            return;
        }

        // 处理车位
        RePark rePark = findOrCreatePark(data, reParkArea);

        // 🔧 修复：启用车位台账重复性检查，使用实际认购时间
        Date subscribeTime = data.getSubscribeTime();
        if (subscribeTime == null) {
            // 如果Excel中没有认购时间，使用当前时间
            subscribeTime = new Date();
            log.warn("认购时间为空，使用当前时间：{} (客户：{}, 车位：{})",
                    subscribeTime, data.getName(), data.getParkingNumber());
        }

        if (checkParkLedgerDuplicate(rePark.getId(), data.getName(), subscribeTime)) {
            log.info("跳过重复车位台账记录：车位ID={}, 客户={}, 认购时间={}, 车位编号={}",
                    rePark.getId(), data.getName(), subscribeTime, data.getParkingNumber());
            return;
        }

        // 更新历史台账
        updateHistoricalLedgers(rePark);

        // 插入台账信息
        ReParkLedgerInfo reParkLedgerInfo = insertParkLedgerInfo(data, rePark, reParkArea);

        // 插入签约信息
        insertParkContract(data, reParkLedgerInfo, rePark);

        // 处理客户信息
        ReCustomerInfo reCustomerInfo = findOrCreateCustomerInfo(data);

        // 插入客户关系
        insertCustomerRelation(data, reParkLedgerInfo, reCustomerInfo);
        //交款方式
        insertPaymentInfo(data, reParkLedgerInfo, rePark);
        insertPaymentInfo2(data, reParkLedgerInfo, rePark);

    }

    void insertPaymentInfo(ReParkLedgerInfoImportParam data, ReParkLedgerInfo reParkLedgerInfo, RePark rePark) {
        if (StrUtil.isNotEmpty(data.getPayment())) {
            RePaymentInfo rePaymentInfo = new RePaymentInfo();
            rePaymentInfo.setPaymentType("2");
            rePaymentInfo.setLedgerId(reParkLedgerInfo.getId());
            rePaymentInfo.setContractId(rePark.getId());
            rePaymentInfo.setPaymentAmount(parseBigDecimal(data.getPayment()));
//            rePaymentInfo.setPaymentTime(data.getSigningTime());
            rePaymentInfoService.getMapper().insert(rePaymentInfo);
        }
    }
    void insertPaymentInfo2(ReParkLedgerInfoImportParam data, ReParkLedgerInfo reParkLedgerInfo, RePark rePark) {
        if (StrUtil.isNotEmpty(data.getEarnestMoney())) {
            RePaymentInfo rePaymentInfo = new RePaymentInfo();
            rePaymentInfo.setPaymentType("1");
            rePaymentInfo.setLedgerId(reParkLedgerInfo.getId());
            rePaymentInfo.setContractId(rePark.getId());
            rePaymentInfo.setPaymentAmount(parseBigDecimal(data.getEarnestMoney()));
//            rePaymentInfo.setPaymentTime(data.getSigningTime());
            rePaymentInfoService.getMapper().insert(rePaymentInfo);
        }
    }

    private ReParkArea findOrCreateParkArea(ReParkLedgerInfoImportParam data) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("CODE", data.getParkingArea());
        queryMap.put("PROJECT_ID", projectId);

        //        if (reParkArea == null) {
//            reParkArea = new ReParkArea();
//            reParkArea.setCode(data.getParkingArea());
//            reParkArea.setProjectId(projectId);
//            //reParkArea.setName(data.getParkingArea()); // 补充名称字段
//            reParkArea.setStatus("0");
//            reParkAreaMapper.insert(reParkArea);
//            log.info("创建新车位区域: {}", data.getParkingArea());
//        }
        return reParkAreaMapper.selectOneByMap(queryMap);
    }

    private RePark findOrCreatePark(ReParkLedgerInfoImportParam data, ReParkArea reParkArea) {
        if (reParkArea == null || reParkArea.getId() == null) {
            throw new IllegalStateException("车位区域不存在或ID为空: " + data.getParkingArea());
        }

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("CODE", data.getParkingNumber());
        queryMap.put("AREA_ID", reParkArea.getId());

        String carTypeValue = getCarTypeValue(data.getParkingCategory());
        if (StrUtil.isNotEmpty(carTypeValue)) {
            queryMap.put("TYPE", carTypeValue);
        }

        RePark rePark = reParkMapper.selectOneByMap(queryMap);
        if (rePark == null) {
            rePark = new RePark();
            rePark.setAreaId(reParkArea.getId());
            rePark.setCode(data.getParkingNumber());
            rePark.setType(carTypeValue);
            // 🔧 修复：欠款为0时应该是已结清状态，不是欠费状态
            rePark.setStatus("0".equals(data.getArrears()) ? LedgerConstants.STATUS_SOLD_PAID : LedgerConstants.STATUS_SOLD_UNPAID);
            rePark.setParkPartition(data.getParkingSubarea());
            rePark.setTotalPrice(parseBigDecimal(data.getOriginalPrice()));
            rePark.setCustomerName(data.getName());
            rePark.setCustomerPhone(data.getPhoneNumber());
            //rePark.setProjectId(projectId); // 补充项目ID
            reParkMapper.insert(rePark);
            log.info("创建新车位: {}", data.getParkingNumber());
        } else {
            // 🔧 修复：欠款为0时应该是已结清状态，不是欠费状态
            rePark.setStatus("0".equals(data.getArrears()) ? LedgerConstants.STATUS_SOLD_PAID : LedgerConstants.STATUS_SOLD_UNPAID);
            rePark.setCustomerName(data.getName());
            rePark.setCustomerPhone(data.getPhoneNumber());
            rePark.setTotalPrice(parseBigDecimal(data.getOriginalPrice()));
            reParkMapper.update(rePark);
            log.info("更新车位: {}", data.getParkingNumber());
        }
        return rePark;
    }

    private void updateHistoricalLedgers(RePark rePark) {
        if (rePark == null || rePark.getId() == null) {
            throw new IllegalStateException("车位不存在或ID为空");
        }

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("PARK_ID", rePark.getId());
        queryMap.put("PROJECT_ID", projectId);
        queryMap.put("IS_HISTORY", false); // 只更新当前活动的记录

        List<ReParkLedgerInfo> reParkLedgerInfos = reParkLedgerInfoMapper.selectListByMap(queryMap);
        if (CollectionUtil.isNotEmpty(reParkLedgerInfos)) {
            for (ReParkLedgerInfo ledgerInfo : reParkLedgerInfos) {
                ledgerInfo.setIsHistory(true);
                reParkLedgerInfoMapper.update(ledgerInfo);
                log.debug("将台账记录 {} 标记为历史", ledgerInfo.getId());
            }
        }
    }

    private ReParkLedgerInfo insertParkLedgerInfo(ReParkLedgerInfoImportParam data, RePark rePark, ReParkArea reParkArea) {
        if (rePark == null || rePark.getId() == null) {
            throw new IllegalStateException("车位不存在或ID为空");
        }

        ReParkLedgerInfo reParkLedgerInfo = new ReParkLedgerInfo();
        reParkLedgerInfo.setName(data.getName());
        reParkLedgerInfo.setPhone(data.getPhoneNumber());
        reParkLedgerInfo.setParkId(rePark.getId());
        reParkLedgerInfo.setProjectId(projectId);
        reParkLedgerInfo.setIdCard(data.getIdCard());
        reParkLedgerInfo.setCode(data.getParkingNumber());
        reParkLedgerInfo.setType(getCarTypeValue(data.getParkingCategory()));
        reParkLedgerInfo.setAreaId(reParkArea.getId());
        reParkLedgerInfo.setParkPartition(data.getParkingSubarea());

        // 🔧 修复：正确设置时间字段
        reParkLedgerInfo.setSubscribeTime(data.getSubscribeTime());
        reParkLedgerInfo.setContractTime(data.getContractTime());

        // 🔧 修复：正确处理定金和欠款计算
        BigDecimal contractPrice = parseBigDecimal(data.getSigningPrice());
        BigDecimal payment = parseBigDecimal(data.getPayment());
        BigDecimal deposit = parseBigDecimal(data.getEarnestMoney()); // 定金

        reParkLedgerInfo.setContractPrice(contractPrice);
        reParkLedgerInfo.setTotalPayment(payment);
        // 🔧 修复欠款计算逻辑：参考住宅台账实现，优先计算后验证
        // 计算欠款：签约价 - 已交款 - 定金
        BigDecimal calculatedArrears = contractPrice.subtract(payment).subtract(deposit);

        // 优先使用Excel中的欠款数据，如果有的话进行对比验证
        String arrearsStr = data.getArrears();
        BigDecimal excelArrears = parseBigDecimal(arrearsStr);

        if (excelArrears != null) {
            // 对比Excel欠款与计算欠款的差异
            BigDecimal difference = calculatedArrears.subtract(excelArrears).abs();
            if (difference.compareTo(new BigDecimal("0.01")) > 0) {
                log.warn("欠款计算差异：Excel欠款={}, 计算欠款={}, 差异={} (客户：{}, 车位：{})",
                        excelArrears, calculatedArrears, difference, data.getName(), data.getParkingNumber());
            }
            // 使用Excel中的欠款数据
            calculatedArrears = excelArrears;
        }

        // 🔧 修复状态判断逻辑：基于精确计算的欠款设置状态
        if (calculatedArrears.compareTo(BigDecimal.ZERO) <= 0) {
            reParkLedgerInfo.setStatus(LedgerConstants.STATUS_SOLD_PAID); // 已结清状态
            log.debug("设置车位台账为已结清状态：计算欠款={} (客户：{}, 车位：{})",
                    calculatedArrears, data.getName(), data.getParkingNumber());
        } else {
            reParkLedgerInfo.setStatus(LedgerConstants.STATUS_SOLD_UNPAID); // 未结清状态
            log.debug("设置车位台账为未结清状态：计算欠款={} (客户：{}, 车位：{})",
                    calculatedArrears, data.getName(), data.getParkingNumber());
        }
        reParkLedgerInfo.setIsHistory(false);
        reParkLedgerInfoMapper.insert(reParkLedgerInfo);
        log.info("创建台账信息: ID={}, 车位编号={}", reParkLedgerInfo.getId(), data.getParkingNumber());
        return reParkLedgerInfo;
    }

    private void insertParkContract(ReParkLedgerInfoImportParam data, ReParkLedgerInfo reParkLedgerInfo, RePark rePark) {
        if (reParkLedgerInfo == null || reParkLedgerInfo.getId() == null) {
            throw new IllegalStateException("台账信息不存在或ID为空");
        }

        ReParkContract reParkContract = new ReParkContract();
        reParkContract.setLedgerId(reParkLedgerInfo.getId());
        reParkContract.setParkId(rePark.getId());
        reParkContract.setRemark(data.getDiscount());
        reParkContract.setContractPrice(parseBigDecimal(data.getSigningPrice()));
        //reParkContract.setProjectId(projectId); // 补充项目ID
        reParkContractMapper.insert(reParkContract);
        log.debug("创建合约信息: {}", reParkContract.getId());
    }

    private ReCustomerInfo findOrCreateCustomerInfo(ReParkLedgerInfoImportParam data) {
        Map<String, Object> queryMap = new HashMap<>();

        // 根据身份证和电话查找, 避免空字段
        if (StrUtil.isNotEmpty(data.getIdCard())) {
            queryMap.put("ID_CARD", data.getIdCard());
        }

        if (StrUtil.isNotEmpty(data.getPhoneNumber())) {
            queryMap.put("PHONE", data.getPhoneNumber());
        }

        queryMap.put("NAME", data.getName());

        ReCustomerInfo reCustomerInfo = null;
        if (!queryMap.isEmpty()) {
            reCustomerInfo = reCustomerInfoMapper.selectOneByMap(queryMap);
        }

        if (reCustomerInfo == null) {
            reCustomerInfo = new ReCustomerInfo();
            reCustomerInfo.setName(data.getName());
            reCustomerInfo.setIdCard(data.getIdCard());
            reCustomerInfo.setPhone(data.getPhoneNumber());
            reCustomerInfoMapper.insert(reCustomerInfo);
            log.info("创建客户信息: {}", data.getName());
        }
        return reCustomerInfo;
    }

    private void insertCustomerRelation(ReParkLedgerInfoImportParam data, ReParkLedgerInfo reParkLedgerInfo, ReCustomerInfo reCustomerInfo) {
        if (reParkLedgerInfo == null || reParkLedgerInfo.getId() == null) {
            throw new IllegalStateException("台账信息不存在或ID为空");
        }
        if (reCustomerInfo == null || reCustomerInfo.getId() == null) {
            throw new IllegalStateException("客户信息不存在或ID为空");
        }

        ReCustomer reCustomer = new ReCustomer();
        reCustomer.setCustomerId(reCustomerInfo.getId());
        reCustomer.setLedgerId(reParkLedgerInfo.getId());
        reCustomer.setVillageId(data.getVillage());
//        reCustomer.setContractTime(data.getSigningTime());
//        reCustomer.setSubscribeTime(data.getSubscribeTime());
//        reCustomer.setCode(data.getOrder());
        reCustomer.setProjectId(projectId);
        reCustomerMapper.insert(reCustomer);
        log.debug("创建客户关联: {}", reCustomer.getId());
    }

    private String getCarTypeValue(String parkingCategory) {
        AtomicReference<String> type = new AtomicReference<>("");
        carType.stream()
                .filter(s -> JSONUtil.parseObj(s).getStr("dictLabel").equals(parkingCategory))
                .findFirst()
                .ifPresent(s -> type.set(JSONUtil.parseObj(s).getStr("dictValue")));
        return type.get();
    }

    // 移除此方法，使用父类的parseBigDecimal方法

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 处理剩余的数据
        synchronized (loadSet) {
            if (!loadSet.isEmpty()) {
                List<ReParkLedgerInfoImportParam> remainingData = new ArrayList<>(loadSet);
                loadSet.clear();
                processDataBatchAsync(remainingData);
            }
        }

        try {
            executorService.shutdown();
            // 等待所有任务完成
            if (!executorService.awaitTermination(5, TimeUnit.MINUTES)) {
                log.warn("线程池未能在指定时间内完成所有任务，强制关闭");
                List<Runnable> unfinishedTasks = executorService.shutdownNow();
                log.warn("未完成的任务数: {}", unfinishedTasks.size());
            }

            log.info("导入完成。处理成功: {}条, 处理失败: {}条",
                    processedCount.get(), errorCount.get());

        } catch (InterruptedException e) {
            log.error("等待任务完成时被中断", e);
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        } finally {
            latch.countDown(); // 释放锁，表示处理已完成
        }
    }

    // 提供获取处理结果的方法
    public int getProcessedCount() {
        return processedCount.get();
    }

    public int getErrorCount() {
        return errorCount.get();
    }

    // 等待处理完成
    public boolean waitForCompletion(long timeout, TimeUnit unit) throws InterruptedException {
        return latch.await(timeout, unit);
    }

    @Override
    protected void processData(ReParkLedgerInfoImportParam data) {
        // 此方法由invoke方法实现，这里留空
    }
}