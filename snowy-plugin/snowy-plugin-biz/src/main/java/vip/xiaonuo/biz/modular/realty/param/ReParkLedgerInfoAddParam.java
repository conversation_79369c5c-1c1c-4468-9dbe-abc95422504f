/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 车位台账信息添加参数
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
@Getter
@Setter
public class ReParkLedgerInfoAddParam {

    /** 客户姓名 */
    @ApiModelProperty(value = "客户姓名", position = 2)
    private String name;

    /** 身份证号 */
    @ApiModelProperty(value = "身份证号", position = 3)
    private String idCard;

    /** 客户电话 */
    @ApiModelProperty(value = "客户电话", position = 4)
    private String phone;

    /** 车位编号 */
    @ApiModelProperty(value = "车位编号", position = 5)
    private String code;

    /** 车位类型--字段（标准、小型、加大） */
    @ApiModelProperty(value = "车位类型--字段（标准、小型、加大）", position = 6)
    private String type;

    /** 车位区域编号 */
    @ApiModelProperty(value = "车位区域编号", position = 7)
    private String areaId;

    /** 车位分区 */
    @ApiModelProperty(value = "车位分区", position = 8)
    private String parkPartition;

    /** 签约日期 */
    @ApiModelProperty(value = "签约日期", position = 9)
    private Date contractTime;

    /** 签约价 */
    @ApiModelProperty(value = "签约价", position = 10)
    private BigDecimal contractPrice;

    /** 合计交款 */
    @ApiModelProperty(value = "合计交款", position = 11)
    private BigDecimal totalPayment;

    /** 车位id */
    @ApiModelProperty(value = "车位id", position = 12)
    private String parkId;

    /** 是否历史 */
    @ApiModelProperty(value = "是否历史", position = 13)
    private Boolean isHistory;

    /** 项目id */
    @ApiModelProperty(value = "项目id", position = 14)
    private String projectId;

    /** 车位状态 1未结清 2未结清  */
    @ApiModelProperty(value = "车位状态 1未结清 2未结清 ", position = 15)
    private String status;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息", position = 16)
    private String extJson;


    /** 认购时间 */
    @ApiModelProperty(value = "认购时间", position = 11)
    private Date subscribeTime;
}
