<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="vip.xiaonuo.biz.modular.realty.mapper.ReGardenHouseMapper">


    <select id="selectSoldAndUnsoldArea" resultType="map">
        SELECT
            0 AS id,
            SUM( CASE WHEN STATUS != 0 THEN BUILD_AREA ELSE 0 END ) AS soldArea,
            SUM( CASE WHEN STATUS = 0 THEN BUILD_AREA ELSE 0 END ) AS unsoldArea
        FROM
            re_garden_house
        WHERE
            `delete_flag` = 'NOT_DELETE'
            AND
            BUILD_ID = #{buildId}
            AND
            HOUSE_TYPE = #{houseType}
            AND
            PROJECT_ID = #{projectId}
    </select>

</mapper>