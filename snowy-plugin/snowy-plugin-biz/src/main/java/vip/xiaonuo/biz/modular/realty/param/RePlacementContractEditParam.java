/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import org.noear.solon.validation.annotation.NotBlank;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 安置签约编辑参数
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
@Getter
@Setter
public class RePlacementContractEditParam {

    /** ID */
    @ApiModelProperty(value = "ID", required = true, position = 1)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 基准价 */
    @ApiModelProperty(value = "基准价", position = 2)
    private BigDecimal benchmarkPrice;

    /** 面积 */
    @ApiModelProperty(value = "面积", position = 3)
    private BigDecimal area;

    /** 小计 */
    @ApiModelProperty(value = "小计", position = 4)
    private BigDecimal subtotal;

    /** 补贴价 */
    @ApiModelProperty(value = "补贴价", position = 5)
    private BigDecimal subsidyPrice;

    /** 补贴面积 */
    @ApiModelProperty(value = "补贴面积", position = 6)
    private BigDecimal subsidyArea;

    /** 补贴小计 */
    @ApiModelProperty(value = "补贴小计", position = 7)
    private BigDecimal subsidySubtotal;

    /** 市场价 */
    @ApiModelProperty(value = "市场价", position = 8)
    private BigDecimal marketPrice;

    /** 市场面积 */
    @ApiModelProperty(value = "市场面积", position = 9)
    private BigDecimal marketArea;

    /** 市场小计 */
    @ApiModelProperty(value = "市场小计", position = 10)
    private BigDecimal marketSubtotal;

    /** 执行优惠 */
    @ApiModelProperty(value = "执行优惠", position = 11)
    private BigDecimal discount;

    /** 优惠类型--字典（直减、折扣） */
    @ApiModelProperty(value = "优惠类型--字典（直减、折扣）", position = 12)
    private String discountType;

    /** 签约总价--计算 基准小计+补贴小计+市场小计-执行优惠 */
    @ApiModelProperty(value = "签约总价--计算 基准小计+补贴小计+市场小计-执行优惠", position = 13)
    private BigDecimal contractPrice;

    /** 维修基金 */
    @ApiModelProperty(value = "维修基金", position = 14)
    private BigDecimal maintenanceFund;

    /** 合计--计算 签约总价+维修基金 */
    @ApiModelProperty(value = "合计--计算 签约总价+维修基金", position = 15)
    private BigDecimal total;

    /** 签约时间 */
    @ApiModelProperty(value = "签约时间", position = 16)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractTime;

    /** 付款形式--字典（一次性付款、全款分期、贷款） */
    @ApiModelProperty(value = "付款形式--字典（一次性付款、全款分期、贷款）", position = 17)
    private String paymentMethod;

    /** 房款合计 */
    @ApiModelProperty(value = "房款合计", position = 18)
    private BigDecimal totalHousePrice;

    /** 首付款--付款形式为贷款 */
    @ApiModelProperty(value = "首付款--付款形式为贷款", position = 19)
    private BigDecimal firstPayment;

    /** 贷款--付款形式为贷款 */
    @ApiModelProperty(value = "贷款--付款形式为贷款", position = 20)
    private BigDecimal loan;

    /** 贷款银行--付款形式为贷款 */
    @ApiModelProperty(value = "贷款银行--付款形式为贷款", position = 21)
    private String loanBank;

    /** 办理贷款时间--付款形式为贷款 */
    @ApiModelProperty(value = "办理贷款时间--付款形式为贷款", position = 22)
    private Date loanTime;

    /** 贷款回款时间--付款形式为贷款 */
    @ApiModelProperty(value = "贷款回款时间--付款形式为贷款", position = 23)
    private Date loanReturnTime;

    /** 定金--付款形式为全款分期 */
    @ApiModelProperty(value = "定金--付款形式为全款分期", position = 24)
    private BigDecimal earnestMoney;

    /** 台账id */
    @ApiModelProperty(value = "台账id", position = 25)
    private String ledgerId;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息", position = 26)
    private String extJson;

    /** 优惠备注 */
    @ApiModelProperty(value = "优惠备注", position = 10)
    private String discountRemark;

    /** 维修单价 **/
    @ApiModelProperty(value = "维修单价", position = 10)
    private BigDecimal maintenancePrice;

    @ApiModelProperty(value = "分期金额", position = 21)
    private BigDecimal installmentAmount;

}
