/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import vip.xiaonuo.biz.modular.realty.entity.RePaymentInfo;
import vip.xiaonuo.biz.modular.realty.param.RePaymentInfoAddParam;
import vip.xiaonuo.biz.modular.realty.param.RePaymentInfoEditParam;
import vip.xiaonuo.biz.modular.realty.param.RePaymentInfoIdParam;
import vip.xiaonuo.biz.modular.realty.param.RePaymentInfoPageParam;

import java.util.List;

/**
 * 交款信息Service接口
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
public interface RePaymentInfoService extends IService<RePaymentInfo> {

    /**
     * 获取交款信息分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    Page<RePaymentInfo> page(RePaymentInfoPageParam rePaymentInfoPageParam);

    /**
     * 添加交款信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    String add(RePaymentInfoAddParam rePaymentInfoAddParam);

    /**
     * 编辑交款信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    void edit(RePaymentInfoEditParam rePaymentInfoEditParam);

    /**
     * 删除交款信息
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    void delete(List<RePaymentInfoIdParam> rePaymentInfoIdParamList);

    /**
     * 获取交款信息详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     */
    RePaymentInfo detail(RePaymentInfoIdParam rePaymentInfoIdParam);

    /**
     * 获取交款信息详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:35
     **/
    RePaymentInfo queryEntity(String id);
}
