package vip.xiaonuo.biz.modular.realty.job;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import vip.xiaonuo.biz.modular.realty.entity.ReCommunityAlarmInfo;
import vip.xiaonuo.biz.modular.realty.entity.ReGardenContract;
import vip.xiaonuo.biz.modular.realty.entity.ReGardenLedgerInfo;
import vip.xiaonuo.biz.modular.realty.mapper.ReCommunityAlarmInfoMapper;
import vip.xiaonuo.biz.modular.realty.mapper.ReGardenContractMapper;
import vip.xiaonuo.biz.modular.realty.mapper.ReGardenLedgerInfoMapper;
import vip.xiaonuo.common.timer.CommonTimerTaskRunner;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ReGardenHouseServiceJob implements CommonTimerTaskRunner {


    @Inject
    private ReGardenContractMapper reGardenContractMapper;

    @Inject
    private ReCommunityAlarmInfoMapper reCommunityAlarmInfoMapper;

    @Inject
    private ReGardenLedgerInfoMapper reGardenLedgerInfoMapper;


    /**
     * 园区临期提示计划任务
     **/

    @Override
    public void action() {
        log.info("---园区临期提示计划任务开始执行---");
        log.info("时间:{}", System.currentTimeMillis());
        //将所有 截止时间 +3的签约信息 导入到提醒表
        QueryWrapper contractQueryWrapper = new QueryWrapper();
        DateTime dateTime = DateUtil.offsetDay(DateUtil.beginOfDay(DateUtil.date()), 3);
        contractQueryWrapper.ge("LEASE_END_TIME", dateTime);
        List<ReGardenContract> reGardenContracts = reGardenContractMapper.selectListByQuery(contractQueryWrapper);

// 筛选即将到期的合同ID
        List<String> ledgerIds = reGardenContracts.stream()
                .map(ReGardenContract::getLedgerId)
                .collect(Collectors.toList());

        if (!ledgerIds.isEmpty()) {
            // 查询对应的台账信息
            QueryWrapper ledgerQueryWrapper = new QueryWrapper();
            ledgerQueryWrapper.in("ID", ledgerIds);
            List<ReGardenLedgerInfo> reGardenLedgerInfos = reGardenLedgerInfoMapper.selectListByQuery(ledgerQueryWrapper);

            // 查询已有预警记录的台账ID
            QueryWrapper alarmQueryWrapper = new QueryWrapper();
            alarmQueryWrapper.in(ReCommunityAlarmInfo::getLedgerId, ledgerIds);
            List<String> existingIds = reCommunityAlarmInfoMapper.selectListByQuery(alarmQueryWrapper)
                    .stream()
                    .map(ReCommunityAlarmInfo::getLedgerId)
                    .collect(Collectors.toList());

            // 构建合同和到期时间的Map
            Map<String, Date> contractDeadlineMap = reGardenContracts.stream()
                    .collect(Collectors.toMap(ReGardenContract::getLedgerId, ReGardenContract::getLeaseEndTime));

            // 过滤不存在于预警表中的台账信息，并生成新的预警记录
            List<ReCommunityAlarmInfo> insertAlarms = reGardenLedgerInfos.stream()
                    .filter(ledger -> !existingIds.contains(ledger.getId())) // 排除已有预警的台账
                    .map(ledger -> {
                        ReCommunityAlarmInfo reCommunityAlarmInfo = new ReCommunityAlarmInfo();
                        reCommunityAlarmInfo.setCustomerName(ledger.getName());
                        reCommunityAlarmInfo.setContactInformation(ledger.getPhone());
                        reCommunityAlarmInfo.setLedgerId(ledger.getId());
                        reCommunityAlarmInfo.setBuilding(ledger.getBuildCode());
                        reCommunityAlarmInfo.setHouseNumber(ledger.getHouseNumber());
                        reCommunityAlarmInfo.setHouseType(ledger.getHouseType());
                        reCommunityAlarmInfo.setExtJson(ledger.getProjectId());
                        reCommunityAlarmInfo.setDeadline(contractDeadlineMap.get(ledger.getId()));
                        return reCommunityAlarmInfo;
                    })
                    .collect(Collectors.toList());

            // 批量插入新的预警信息
            if (!insertAlarms.isEmpty()) {
                reCommunityAlarmInfoMapper.insertBatch(insertAlarms);
            }
        }


     }


}
