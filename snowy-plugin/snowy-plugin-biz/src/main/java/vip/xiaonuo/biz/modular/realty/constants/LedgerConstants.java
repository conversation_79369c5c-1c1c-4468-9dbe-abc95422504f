/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.constants;

/**
 * 台账相关业务常量
 * 
 * <AUTHOR> 4.0 sonnet
 * @date 2024/12/19
 */
public class LedgerConstants {

    // ==================== 台账类型 ====================
    
    /** 房屋类型：住宅 */
    public static final String HOUSE_TYPE_RESIDENTIAL = "1";
    
    /** 房屋类型：商业 */
    public static final String HOUSE_TYPE_COMMERCIAL = "2";
    
    /** 房屋类型：储藏间 */
    public static final String HOUSE_TYPE_STORAGE = "3";

    // ==================== 签约类型 ====================
    
    /** 签约类型：销售 */
    public static final String CONTRACT_TYPE_SALES = "1";
    
    /** 签约类型：租赁 */
    public static final String CONTRACT_TYPE_LEASE = "2";
    
    /** 签约类型：安置 */
    public static final String CONTRACT_TYPE_PLACEMENT = "3";

    // ==================== 台账状态 ====================
    
    /** 状态：待售 */
    public static final String STATUS_AVAILABLE = "1";
    
    /** 状态：售出-未结清 */
    public static final String STATUS_SOLD_UNPAID = "2";
    
    /** 状态：售出-已结清 */
    public static final String STATUS_SOLD_PAID = "3";

    // ==================== 重复检查相关 ====================
    
    /** 重复处理策略：跳过 */
    public static final String DUPLICATE_STRATEGY_SKIP = "SKIP";
    
    /** 重复处理策略：报错 */
    public static final String DUPLICATE_STRATEGY_ERROR = "ERROR";
    
    /** 默认批量处理大小 */
    public static final int DEFAULT_BATCH_SIZE = 100;

    // ==================== 错误信息 ====================
    
    /** 台账记录重复错误信息 */
    public static final String ERROR_DUPLICATE_RECORD = "台账记录重复，跳过导入";
    
    /** 续签重复错误信息 */
    public static final String ERROR_RENEWAL_DUPLICATE = "续签失败：该房源在此交款日期已存在相同客户的台账记录";
    
    /** 房源不存在错误信息 */
    public static final String ERROR_HOUSE_NOT_FOUND = "未找到房屋";
    
    /** 车位不存在错误信息 */
    public static final String ERROR_PARK_NOT_FOUND = "未找到车位";
    
    /** 系统异常错误信息前缀 */
    public static final String ERROR_SYSTEM_EXCEPTION = "系统异常:";

    // ==================== 支付类型 ====================
    
    /** 支付类型：定金 */
    public static final String PAYMENT_TYPE_DEPOSIT = "1";
    
    /** 支付类型：房款 */
    public static final String PAYMENT_TYPE_HOUSE_PAYMENT = "2";
    
    /** 支付类型：维修基金 */
    public static final String PAYMENT_TYPE_MAINTENANCE_FUND = "3";
    
    /** 支付类型：其他费用 */
    public static final String PAYMENT_TYPE_OTHER = "4";
    
    /** 支付类型：贷款回款 */
    public static final String PAYMENT_TYPE_LOAN_REPAYMENT = "5";

    // ==================== 发票状态 ====================
    
    /** 发票状态：已开发票 */
    public static final String INVOICE_STATUS_ISSUED = "1";
    
    /** 发票状态：未开发票 */
    public static final String INVOICE_STATUS_NOT_ISSUED = "2";

    // ==================== 车位相关 ====================
    
    /** 车位状态：可用 */
    public static final String PARK_STATUS_AVAILABLE = "1";
    
    /** 车位状态：占用 */
    public static final String PARK_STATUS_OCCUPIED = "2";
    
    /** 车位状态：欠费 */
    public static final String PARK_STATUS_ARREARS = "3";

    // ==================== 导入相关 ====================
    
    /** 导入类型：住宅台账 */
    public static final String IMPORT_TYPE_RESIDENTIAL_LEDGER = "residential_ledger";
    
    /** 导入类型：商业销售台账 */
    public static final String IMPORT_TYPE_COMMERCIAL_SALES_LEDGER = "commercial_sales_ledger";
    
    /** 导入类型：商业租赁台账 */
    public static final String IMPORT_TYPE_COMMERCIAL_LEASE_LEDGER = "commercial_lease_ledger";
    
    /** 导入类型：储藏间台账 */
    public static final String IMPORT_TYPE_STORAGE_LEDGER = "storage_ledger";
    
    /** 导入类型：车位台账 */
    public static final String IMPORT_TYPE_PARK_LEDGER = "park_ledger";

    // ==================== 私有构造函数 ====================
    
    private LedgerConstants() {
        // 防止实例化
    }
}
