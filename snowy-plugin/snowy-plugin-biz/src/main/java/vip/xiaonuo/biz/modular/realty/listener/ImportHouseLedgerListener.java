package vip.xiaonuo.biz.modular.realty.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.Solon;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.async.ReProjectDetailServiceAsync;
import vip.xiaonuo.biz.modular.realty.constants.LedgerConstants;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.mapper.ReHouseMapper;
import vip.xiaonuo.biz.modular.realty.mapper.ReLedgerInfoMapper;
import vip.xiaonuo.biz.modular.realty.param.ReBussnessLedgerInfoImportParam;
import vip.xiaonuo.biz.modular.realty.service.*;
import vip.xiaonuo.dev.api.DevDictApi;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Excel 导入监听器，用于处理住宅台账数据导入
 * 已集成重复性检查功能
 *
 * <AUTHOR>
 * @date 2024/8/22 10:35
 * @updated Claude 4.0 sonnet 2024/12/19
 */
@Slf4j
public class ImportHouseLedgerListener extends AbstractImportListener<ReHouseLedgerPlaceImport> {

    private final String houseType;

    // 使用 ThreadLocal 缓存楼栋信息和客户信息
    private final ThreadLocal<HashMap<String, String>> threadLocal = ThreadLocal.withInitial(HashMap::new);
    private final ThreadLocal<HashMap<String, String>> threadLocalName = ThreadLocal.withInitial(HashMap::new);

    // 服务类实例化
    private static final ReHouseService reHouseService = Solon.context().getBean(ReHouseService.class);
    private static final ReBuildingService reBuildingService = Solon.context().getBean(ReBuildingService.class);
    private static final ReCustomerService reCustomerService = Solon.context().getBean(ReCustomerService.class);
    private static final ReLedgerInfoService reLedgerInfoService = Solon.context().getBean(ReLedgerInfoService.class);
    private static final ReCustomerInfoService reCustomerInfoService = Solon.context().getBean(ReCustomerInfoService.class);
    private static final RePlacementContractService rePlacementContractService = Solon.context().getBean(RePlacementContractService.class);
    private static final RePaymentInfoService rePaymentInfoService = Solon.context().getBean(RePaymentInfoService.class);
    private static final ReContractInfoService reContractInfoService = Solon.context().getBean(ReContractInfoService.class);
    private static final DevDictApi devDictApi = Solon.context().getBean(DevDictApi.class);
    private final ReHouseMapper reHouseMapper = Solon.context().getBean(ReHouseMapper.class);
    private static ReProjectDetailServiceAsync reProjectDetailServiceAsync = Solon.context().getBean(ReProjectDetailServiceAsync.class);

    public ImportHouseLedgerListener(String projectId, String houseType) {
        super(projectId);
        this.houseType = houseType;
    }

    private Map<String, Object> createQueryMap(ReHouseLedgerPlaceImport param,String msg) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("楼层", param.getFloor());
        queryMap.put("楼号", param.getBuildCode());
        queryMap.put("房号", param.getHouseNumber());
        queryMap.put("项目ID", projectId);
        queryMap.put("错误信息", msg);
        return queryMap;
    }

    @Override
    @Tran
    public void invoke(ReHouseLedgerPlaceImport data, AnalysisContext context) {
        total++;
        try {
            validateRequiredFields(data);

            // 处理房源信息
            ReHouse reHouse = saveHouseInfo(data);
            if (reHouse == null) {
                errorList.add(createQueryMap(data, LedgerConstants.ERROR_HOUSE_NOT_FOUND));
                return;
            }

            // 🔥 新增：重复性检查
            if (checkHouseLedgerDuplicate(reHouse.getId(), data.getName(), data.getSubscribeTime())) {
                errorList.add(createQueryMap(data, LedgerConstants.ERROR_DUPLICATE_RECORD));
                log.info("跳过重复台账记录：房源ID={}, 客户={}, 认购时间={}",
                        reHouse.getId(), data.getName(), data.getSubscribeTime());
                return;
            }

            // 将所有台账置为历史
            updateLedgerHistory(reHouse.getId());

            // 处理客户信息
            String customerId = saveCustomerInfo(data);
            String coOwnerId = saveOtherCustomerInfo(data);
            List<String> nameIds = saveQuotaInfo(data);

            // 处理台账信息
            ReLedgerInfo reLedgerInfo = saveLedgerInfo(data, reHouse, reHouse.getHouseType(), projectId);

            // 处理客户管理信息
            saveCustomerManagementInfo(data, reHouse, reLedgerInfo, customerId, coOwnerId, nameIds);

            // 处理签约信息
            RePlacementContract rePlacementContract = savePlacementContract(data, reLedgerInfo);

            // 处理交款信息
            savePaymentInfo(data, rePlacementContract, reLedgerInfo);

            // 处理合同信息
            saveContractInfo(data, reLedgerInfo);

            // 更新房屋信息
            updateHouseStatus(reHouse, reLedgerInfo, data.getName());

            reProjectDetailServiceAsync.updateOrInsertDetail(reHouse, projectId);
        } catch (Exception e) {
            log.error("处理数据失败: {}", e.getMessage(), e);
            errorList.add(createQueryMap(data, LedgerConstants.ERROR_SYSTEM_EXCEPTION + e.getMessage()));
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("住宅台账导入解析完成，总计处理{}条记录，错误{}条", total, errorList.size());
        cleanupResources();
    }

    @Override
    protected void cleanupResources() {
        threadLocal.remove();
        threadLocalName.remove();
        log.debug("已清理ThreadLocal资源");
    }

    @Override
    protected void processData(ReHouseLedgerPlaceImport data) {
        // 此方法由invoke方法实现，这里留空
    }

    /**
     * 保存房源信息
     */
    private ReHouse saveHouseInfo(ReHouseLedgerPlaceImport data) {

        String buildCode = data.getBuildCode();
        String unit = data.getUnit();
        String houseNumber = data.getHouseNumber();
        String floor = data.getFloor();

        // 查询房源信息
        ReHouse oneOpt = reHouseService.getOne(QueryWrapper.create()
                .eq(ReHouse::getBuildCode, buildCode)
                .eq(ReHouse::getUnit, unit)
                .eq(ReHouse::getHouseType, houseType)
                .eq(ReHouse::getProjectId, projectId)
                .eq(ReHouse::getHouseNumber, houseNumber)
                .eq(ReHouse::getFloor, floor));

        return oneOpt;
    }

    /**
     * 校验必填字段
     */
    private void validateRequiredFields(ReHouseLedgerPlaceImport data) {
        if (data.getBuildCode() == null || data.getBuildCode().isEmpty()) {
            throw new IllegalArgumentException("楼栋编号不能为空");
        }
        if (data.getUnit() == null || data.getUnit().isEmpty()) {
            throw new IllegalArgumentException("单元不能为空");
        }
        if (data.getHouseNumber() == null || data.getHouseNumber().isEmpty()) {
            throw new IllegalArgumentException("房号不能为空");
        }
        if (data.getFloor() == null || data.getFloor().isEmpty()) {
            throw new IllegalArgumentException("楼层不能为空");
        }
    }

    private void updateHouseStatus(ReHouse reHouse, ReLedgerInfo reLedgerInfo, String name) {
        reHouse.setStatus(reLedgerInfo.getStatus());
        reHouse.setCustomerName(name);
        reHouse.setCustomerPhone(reLedgerInfo.getPhone());
        reHouseMapper.update(reHouse);
    }
    // 移除此方法，使用父类的updateLedgerHistory方法

    /**
     * 获取楼栋 ID
     */
    private String getBuildingId(String buildCode) {
        if (!threadLocal.get().containsKey(buildCode)) {
            ReBuilding building = reBuildingService.getOne(QueryWrapper.create()
                    .eq(ReBuilding::getProjectId, projectId)
                    .eq(ReBuilding::getCode, buildCode));
            if (building == null) {
                throw new RuntimeException("楼栋不存在, 楼栋编号: " + buildCode);
            }
            threadLocal.get().put(buildCode, building.getId());
        }
        return threadLocal.get().get(buildCode);
    }


    private String saveCustomerInfo(ReHouseLedgerPlaceImport data) {
        ReCustomerInfo reCustomerInfo = new ReCustomerInfo();
        reCustomerInfo.setName(data.getName());
        if (StrUtil.isNotEmpty(data.getIdCard())) {
            //正则校验身份证
            if (Pattern.matches("^\\d{17}[0-9Xx]{1}$", data.getIdCard())) {
                reCustomerInfo.setIdCard(data.getIdCard());
            }
        }
        reCustomerInfo.setPhone(data.getPhone());

        if (threadLocalName.get().containsKey(data.getName())) {
            reCustomerInfo.setId(threadLocalName.get().get(data.getName()));
        } else {
            reCustomerInfoService.save(reCustomerInfo);
            threadLocalName.get().put(data.getName(), reCustomerInfo.getId());
        }

        return reCustomerInfo.getId();
    }

    /**
     * 保存客户信息
     */
    private String saveOtherCustomerInfo(ReHouseLedgerPlaceImport data) {
        ReCustomerInfo reCustomerInfo = new ReCustomerInfo();
        reCustomerInfo.setName(data.getCoOwner());
        if (StrUtil.isNotEmpty(data.getIdCard2())) {
            //正则校验身份证
            if (Pattern.matches("^\\d{17}[0-9Xx]{1}$", data.getIdCard2())) {
                reCustomerInfo.setIdCard(data.getIdCard2());
            }
        }
        reCustomerInfo.setPhone(data.getPhone2());

        if (threadLocalName.get().containsKey(data.getCoOwner())) {
            reCustomerInfo.setId(threadLocalName.get().get(data.getCoOwner()));
        } else {
            reCustomerInfoService.save(reCustomerInfo);
            threadLocalName.get().put(data.getCoOwner(), reCustomerInfo.getId());
        }

        return reCustomerInfo.getId();
    }

    /**
     * 保存名额信息
     */
    private List<String> saveQuotaInfo(ReHouseLedgerPlaceImport data) {
        List<String> nameIds = new ArrayList<>();
        if (data.getQuota1() != null) nameIds.add(reCustomerInfoService.getIdByName(data.getQuota1(),data.getNewPeople()));
        if (data.getQuota2() != null) nameIds.add(reCustomerInfoService.getIdByName(data.getQuota2()));
        if (data.getQuota3() != null) nameIds.add(reCustomerInfoService.getIdByName(data.getQuota3()));
        if (data.getQuota4() != null) nameIds.add(reCustomerInfoService.getIdByName(data.getQuota4()));
        if (data.getQuota5() != null) nameIds.add(reCustomerInfoService.getIdByName(data.getQuota5()));
        return nameIds;
    }

    /**
     * 保存台账信息
     */
    private ReLedgerInfo saveLedgerInfo(ReHouseLedgerPlaceImport data, ReHouse reHouse, String houseType,String projectId) {
        ReLedgerInfo reLedgerInfo = new ReLedgerInfo();
        reLedgerInfo.setCode(data.getCode());
        reLedgerInfo.setProjectId(projectId);
        reLedgerInfo.setHouseType(houseType);
        reLedgerInfo.setHouseId(reHouse.getId());
        reLedgerInfo.setVillageId(data.getVillageId());
        reLedgerInfo.setHouseNumber(data.getHouseNumber());
        reLedgerInfo.setBuildCode(data.getBuildCode());
        reLedgerInfo.setUnit(data.getUnit());
        reLedgerInfo.setPhone(data.getPhone());
        reLedgerInfo.setName(data.getName());
        reLedgerInfo.setIdCard(data.getIdCard());
        reLedgerInfo.setStatus("已结清".equals(data.getDebtStatus()) ? "3" : "2");
        reLedgerInfo.setFloor(data.getFloor());
        reLedgerInfo.setName(data.getName());
        reLedgerInfo.setSubscribeTime(data.getSubscribeTime());
        reLedgerInfo.setTotalPayment(data.getTotalPayment());
        reLedgerInfo.setContractPrice(data.getContractPrice());
        reLedgerInfo.setArea(data.getArea());
        reLedgerInfo.setContractType("3");
        reLedgerInfo.setIsHistory(false);
        reLedgerInfoService.save(reLedgerInfo);
        return reLedgerInfo;
    }

    /**
     * 保存客户管理信息
     */
    private void saveCustomerManagementInfo(ReHouseLedgerPlaceImport data, ReHouse reHouse, ReLedgerInfo reLedgerInfo,
                                            String customerId, String coOwnerId, List<String> nameIds) {
        ReCustomer reCustomer = new ReCustomer();
        reCustomer.setHouseId(reHouse.getId());
        reCustomer.setProjectId(projectId);
        if (data.getEnableArea() != null) {
            reCustomer.setEnableArea(BigDecimal.valueOf(parseDouble(data.getEnableArea())));
        }
        reCustomer.setVillageId(data.getVillageId());
        reCustomer.setCode(data.getCode());
        reCustomer.setAddress(data.getAddress());
        reCustomer.setSubscribeTime(data.getSubscribeTime());
        reCustomer.setLedgerId(reLedgerInfo.getId());
        reCustomer.setCustomerId(customerId);
        reCustomer.setShareholderIds(coOwnerId);
        reCustomer.setQuotaIds(String.join(",", nameIds));
        reCustomerService.save(reCustomer);
    }

    /**
     * 保存签约信息
     */
    private RePlacementContract savePlacementContract(ReHouseLedgerPlaceImport data, ReLedgerInfo reLedgerInfo) {
        String maintenanceFund = data.getMaintenanceFund();
        BigDecimal maintenanceFundPrice = BigDecimal.ZERO;
        if (StrUtil.isNotEmpty(maintenanceFund)) {
            maintenanceFundPrice = new BigDecimal(maintenanceFund).setScale(1, BigDecimal.ROUND_HALF_UP);
        }
        RePlacementContract rePlacementContract = new RePlacementContract();
        rePlacementContract.setArea(data.getBenchmarkArea());
        rePlacementContract.setBenchmarkPrice(data.getBenchmarkPrice());
        rePlacementContract.setContractPrice(data.getContractPrice());
        rePlacementContract.setTotal(maintenanceFundPrice);
        rePlacementContract.setTotalHousePrice(maintenanceFundPrice);
        rePlacementContract.setLedgerId(reLedgerInfo.getId());
        rePlacementContract.setSubtotal(data.getBenchmarkSubtotal());
        rePlacementContract.setMarketArea(data.getMarketArea());
        rePlacementContract.setMarketPrice(data.getMarketPrice());
        rePlacementContract.setMarketSubtotal(data.getMarketSubtotal());
        rePlacementContract.setSubsidyArea(data.getSubsidyArea());
        rePlacementContract.setSubsidyPrice(data.getSubsidyPrice());
        rePlacementContract.setSubsidySubtotal(data.getSubsidySubtotal());
        BigDecimal maintenancePrice = data.getMaintenancePrice();
        if(maintenancePrice==null){
            maintenancePrice = BigDecimal.ZERO;
        }
        rePlacementContract.setMaintenanceFund(maintenancePrice);

        // 🔧 修复除法运算安全性：添加空值检查和精度设置
        BigDecimal area = data.getArea();
        if (area != null && area.compareTo(BigDecimal.ZERO) > 0) {
            // 使用安全的除法运算，设置精度为2位小数，四舍五入
            BigDecimal unitMaintenancePrice = maintenancePrice.divide(area, 2, BigDecimal.ROUND_HALF_UP);
            rePlacementContract.setMaintenancePrice(unitMaintenancePrice);
            log.debug("计算维修基金单价：{} ÷ {} = {} (客户：{})",
                    maintenancePrice, area, unitMaintenancePrice, data.getName());
        } else {
            rePlacementContract.setMaintenancePrice(BigDecimal.ZERO);
            log.warn("面积为空或为0，维修基金单价设为0：面积={} (客户：{})", area, data.getName());
        }
        rePlacementContract.setPaymentMethod("1");
        rePlacementContractService.save(rePlacementContract);
        return rePlacementContract;
    }

    /**
     * 保存交款信息
     */
    private void savePaymentInfo(ReHouseLedgerPlaceImport data, RePlacementContract rePlacementContract, ReLedgerInfo reLedgerInfo) {
        savePayment(data.getEarnestMoney(), rePlacementContract, reLedgerInfo, data.getPaymentTime());
        savePayment(data.getFirstPayment(), rePlacementContract, reLedgerInfo,data.getPaymentTime());
        savePayment(data.getSecondPayment(), rePlacementContract, reLedgerInfo,data.getPaymentTime());
        if (data.getThirdPayment() != null && data.getThirdPayment().compareTo(BigDecimal.ZERO) > 0) {
            RePaymentInfo rePaymentInfo = new RePaymentInfo();
            rePaymentInfo.setContractId(rePlacementContract.getId());
            rePaymentInfo.setLedgerId(reLedgerInfo.getId());
            rePaymentInfo.setPaymentType("3");
            if (data.getPaymentTime() != null) {
                rePaymentInfo.setPaymentTime(data.getPaymentTime());
            }
            rePaymentInfo.setPaymentAmount(data.getThirdPayment());
            rePaymentInfoService.save(rePaymentInfo);
        }
    }

    private void savePayment(BigDecimal amount, RePlacementContract rePlacementContract, ReLedgerInfo reLedgerInfo, Date paymentTime) {
        if (amount != null && amount.compareTo(BigDecimal.ZERO) > 0) {
            RePaymentInfo rePaymentInfo = new RePaymentInfo();
            rePaymentInfo.setContractId(rePlacementContract.getId());
            rePaymentInfo.setLedgerId(reLedgerInfo.getId());
            rePaymentInfo.setPaymentType("2");
            if (paymentTime != null) {
                rePaymentInfo.setPaymentTime(paymentTime);
            }
            rePaymentInfo.setPaymentAmount(amount);
            rePaymentInfoService.save(rePaymentInfo);
        }
    }

    /**
     * 保存合同信息
     */
    private void saveContractInfo(ReHouseLedgerPlaceImport data, ReLedgerInfo reLedgerInfo) {
        ReContractInfo reContractInfo = new ReContractInfo();
        reContractInfo.setLedgerId(reLedgerInfo.getId());
        reContractInfo.setContractNumber(data.getContractNumber());
        reContractInfo.setInvoiceStatus(data.getInvoiceStatus() != null && data.getInvoiceStatus().contains("开发票") ? "1" : "2");
        //计算合同日期
        if (data.getContractNumber() != null) {
            try {
                String[] split = data.getContractNumber().split("-");
                if (split.length >= 2) {
                    Calendar calendar = Calendar.getInstance();
                    int year = Integer.parseInt(split[0]);
                    // 确保月份字符串至少有两位
                    String monthPart = split[1].length() >= 2 ? split[1].substring(0, 2) : split[1];
                    int month = Integer.parseInt(monthPart);

                    // 确保日期部分存在
                    int day = 1; // 默认值
                    if (split[1].length() >= 4) {
                        day = Integer.parseInt(split[1].substring(2, 4));
                    }

                    // 验证日期有效性
                    if (month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                        calendar.set(Calendar.YEAR, year);
                        calendar.set(Calendar.MONTH, month - 1); // 月份从0开始，需要减1
                        calendar.set(Calendar.DAY_OF_MONTH, day);

                        // 验证日期是否有效（例如2月30日是无效的）
                        int actualMonth = calendar.get(Calendar.MONTH);
                        if (actualMonth == month - 1) { // 检查月份是否被Calendar自动调整
                            reContractInfo.setContractRecordTime(calendar.getTime());
                        } else {
                            // 日期无效，可以记录日志或采取其他措施

                        }
                    }
                }
            } catch (NumberFormatException e) {
                // 处理数字解析异常

            }
        }
        reContractInfoService.save(reContractInfo);
    }

    // 移除此方法，使用父类的parseDouble方法
}
