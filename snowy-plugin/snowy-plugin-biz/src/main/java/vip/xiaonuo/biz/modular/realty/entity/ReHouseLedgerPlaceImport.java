package vip.xiaonuo.biz.modular.realty.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 住宅台账导出实体
 * 该实体类用于住宅台账导出
 * <AUTHOR>
 * @date 2024/10/11 15:25
 */

@Data
public class ReHouseLedgerPlaceImport {

    /** 村落编号 */
    @ExcelProperty("村落编号")
    private String villageId;

    /** 选房序号 */
    @ExcelProperty("选房序号")
    private String code;

    /** 客户姓名 */
    @ExcelProperty("客户姓名")
    private String name;

    @ExcelProperty("身份证号码")
    private String idCard;

    /** 共有人 **/
    @ExcelProperty("共有人")
    private String coOwner;

    @ExcelProperty("共有人身份证号码")
    private String idCard2;

    @ExcelProperty("电话1")
    private String phone;

    @ExcelProperty("电话2")
    private String phone2;
    /** 启用面积 */
    @ExcelProperty("启用面积")
    private String enableArea;

    /** 名额1 **/
    @ExcelProperty("名额1")
    private String quota1;

    /** 名额2 **/
    @ExcelProperty("名额2")
    private String quota2;

    /** 名额3 **/
    @ExcelProperty("名额3")
    private String quota3;

    /** 名额4 **/
    @ExcelProperty("名额4")
    private String quota4;

    /** 名额5 **/
    @ExcelProperty("名额5")
    private String quota5;

    /** 人口 **/
    @ExcelProperty("新增人口")
    private String newPeople;

    /** 地址 */
    @ExcelProperty("地址")
    private String address;

    /** 认购时间 */
    @DateTimeFormat("yyyy年MM月dd日")
    @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
    @ExcelProperty("认购时间")
    private Date subscribeTime;


    /** 单元 */
    @ExcelProperty("单元")
    private String unit;

    /** 楼号 */
    @ExcelProperty("楼号")
    private String buildCode;

    /**
     * 楼层
     **/
    @ExcelProperty("楼层")
    private String floor;

    /** 房号 */
    @ExcelProperty("房号")
    private String houseNumber;

    /** 房源位置 **/
    @ExcelProperty("房源位置")
    private String houseLocation;

    /** 面积 */
    @ExcelProperty("房源面积")
    private BigDecimal area ;

    /** 基准价 */
    @ExcelProperty("基准价")
    private BigDecimal benchmarkPrice ;

    /** 基准面积 */
    @ExcelProperty("基准面积")
    private BigDecimal benchmarkArea ;

    /** 基准小计 */
    @ExcelProperty("基准小计")
    private BigDecimal benchmarkSubtotal ;

    /** 补贴价 */
    @ExcelProperty("补贴价")
    private BigDecimal subsidyPrice ;

    /** 补贴面积 */
    @ExcelProperty("补贴面积")
    private BigDecimal subsidyArea ;

    /** 补贴小计 */
    @ExcelProperty("补贴小计")
    private BigDecimal subsidySubtotal ;

    /** 市场价 */
    @ExcelProperty("市场价")
    private BigDecimal marketPrice ;

    /** 市场面积 */
    @ExcelProperty("市场面积")
    private BigDecimal marketArea ;

    /** 市场小计 */
    @ExcelProperty("市场小计")
    private BigDecimal marketSubtotal ;

    /** 特惠面积 **/
    @ExcelProperty("特惠面积")
    private BigDecimal preferentialArea ;

    /** 特惠价 **/
    @ExcelProperty("特惠价")
    private BigDecimal preferentialPrice ;

    /** 特惠小计 **/
    @ExcelProperty("特惠小计")
    private BigDecimal preferentialSubtotal ;

    /** 签约总价 */
    @ExcelProperty("签约总价")
    private BigDecimal contractPrice ;

    /** 维修基金总价 */
    @ExcelProperty("维修基金")
    private BigDecimal maintenancePrice;

    @ExcelProperty("房款总合计")
    private String maintenanceFund;

    @ExcelProperty("交款日期")
    @DateTimeFormat("yyyy年MM月dd日")
    @JsonFormat(pattern = "yyyy年MM月dd日", timezone = "GMT+8")
    private Date paymentTime;

    /** 定金 **/
    @ExcelProperty("定金")
    private BigDecimal earnestMoney ;

    /** 第一次交款 **/
    @ExcelProperty("第一次交款")
    private BigDecimal firstPayment ;

    /** 第二次交款 **/
    @ExcelProperty("第二次交款")
    private BigDecimal secondPayment ;

    /** 第三次交款 **/
    @ExcelProperty("第三次交款/维修基金")
    private BigDecimal thirdPayment ;

    /** 房款合计 */
    @ExcelProperty("房款合计")
    private BigDecimal totalHousePrice ;

    /** 合计交款 */
    @ExcelProperty("合计交款")
    private BigDecimal totalPayment ;

    /** 欠款金额 */
    @ExcelProperty("欠款情况")
    private BigDecimal debtPayment ;

    /** 结清状态 */
    @ExcelProperty("结清情况")
    private String debtStatus;

    /** 合同编号 */
    @ExcelProperty("合同编号")
    private String contractNumber;

    /** 承诺结清日期 */
    @ExcelProperty("承诺结清日期")
    private String promiseSettlementDate;

    /** 车位状态 */
    @ExcelProperty("车位状态")
    private String parkingStatus;

    /** 发票状态 */
    @ExcelProperty("发票状态")
    private String invoiceStatus;
}
