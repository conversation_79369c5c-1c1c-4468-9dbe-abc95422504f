/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.EasyExcel;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.core.handle.UploadedFile;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.async.ReProjectDetailServiceAsync;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.listener.ImportBussnessLeaseLedgerInfoListener;
import vip.xiaonuo.biz.modular.realty.listener.ImportBussnessLedgerInfoListener;
import vip.xiaonuo.biz.modular.realty.listener.ImportParkLedgerInfoListener;
import vip.xiaonuo.biz.modular.realty.listener.ImportStoreRoomLedgerInfoListener;
import vip.xiaonuo.biz.modular.realty.mapper.ReCustomerInfoMapper;
import vip.xiaonuo.biz.modular.realty.mapper.ReLedgerInfoMapper;
import vip.xiaonuo.biz.modular.realty.param.*;
import vip.xiaonuo.biz.modular.realty.service.*;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.dev.api.DevDictApi;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 房屋台账信息Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/08/17 14:34
 **/
@Slf4j
@Component
public class ReLedgerInfoServiceImpl extends ServiceImpl<ReLedgerInfoMapper, ReLedgerInfo> implements ReLedgerInfoService {

    @Inject
    private ReCustomerInfoMapper reCustomerInfoMapper;

    @Inject
    private ReHouseService reHouseService;

    @Inject
    private ReCustomerService reCustomerService;

    @Inject
    private ReSalesContractService reSalesContractService;

    @Inject
    private ReLeaseContractService reLeaseContractService;

    @Inject
    private RePlacementContractService rePlacementContractService;

    @Inject
    private RePaymentInfoService rePaymentInfoService;

    @Inject
    private ReContractInfoService reContractInfoService;

    @Inject
    private ReProjectDetailServiceAsync reProjectDetailServiceAsync;

    @Inject
    private DevDictApi dictApi;

    @Override
    public Page<ReLedgerInfo> page(ReLedgerInfoPageParam reLedgerInfoPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if (StrUtil.isNotBlank(reLedgerInfoPageParam.getQuotaName())) {
            //查询姓名表的id
            QueryWrapper queryWrapper1 = new QueryWrapper();
            queryWrapper1.like(ReCustomerInfo::getName, reLedgerInfoPageParam.getQuotaName());
            queryWrapper1.select("id");
            List<ReCustomerInfo> reCustomerInfos = reCustomerInfoMapper.selectListByQuery(queryWrapper1);
            List<String> collect = reCustomerInfos.stream().map(s -> s.getId()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(collect)) {
                QueryWrapper queryWrapper2 = QueryWrapper.create();
                collect.forEach(s -> {
                    queryWrapper2.or("find_in_set(?,QUOTA_IDS)>0", s);
                });
                queryWrapper2.select(ReCustomer::getLedgerId);
                queryWrapper2.groupBy("id");
                List<ReCustomer> reCustomers = reCustomerService.list(queryWrapper2);
                if (CollectionUtil.isNotEmpty(reCustomers)) {
                    List<String> collect1 = reCustomers.stream().map(s -> s.getLedgerId()).collect(Collectors.toList());
                    queryWrapper.in(ReLedgerInfo::getId, collect1);
                }
            }
        }
        queryWrapper.like(ReLedgerInfo::getName, reLedgerInfoPageParam.getName(), StringUtil.isNotBlank(reLedgerInfoPageParam.getName()));
        queryWrapper.like(ReLedgerInfo::getIdCard, reLedgerInfoPageParam.getIdCard(), StringUtil.isNotBlank(reLedgerInfoPageParam.getIdCard()));
        queryWrapper.like(ReLedgerInfo::getPhone, reLedgerInfoPageParam.getPhone(), StringUtil.isNotBlank(reLedgerInfoPageParam.getPhone()));
        queryWrapper.like(ReLedgerInfo::getHouseNumber, reLedgerInfoPageParam.getHouseNumber(), StringUtil.isNotBlank(reLedgerInfoPageParam.getHouseNumber()));
        queryWrapper.eq(ReLedgerInfo::getHouseId, reLedgerInfoPageParam.getHouseId(), StringUtil.isNotBlank(reLedgerInfoPageParam.getHouseId()));
        queryWrapper.eq(ReLedgerInfo::getContractType, reLedgerInfoPageParam.getContractType(), StringUtil.isNotBlank(reLedgerInfoPageParam.getContractType()));
        queryWrapper.eq(ReLedgerInfo::getVillageId, reLedgerInfoPageParam.getVillageId(), StringUtil.isNotBlank(reLedgerInfoPageParam.getVillageId()));
        queryWrapper.eq(ReLedgerInfo::getCode, reLedgerInfoPageParam.getCode(), StringUtil.isNotBlank(reLedgerInfoPageParam.getCode()));
        queryWrapper.eq(ReLedgerInfo::getIsHistory, reLedgerInfoPageParam.getIsHistory(), null != reLedgerInfoPageParam.getIsHistory());
        queryWrapper.eq(ReLedgerInfo::getHouseType, reLedgerInfoPageParam.getHouseType(), StringUtil.isNotBlank(reLedgerInfoPageParam.getHouseType()));
        queryWrapper.eq(ReLedgerInfo::getFloor, reLedgerInfoPageParam.getFloor(), reLedgerInfoPageParam.getFloor() != null);
        queryWrapper.eq(ReLedgerInfo::getStatus, reLedgerInfoPageParam.getStatus(), StringUtil.isNotBlank(reLedgerInfoPageParam.getStatus()));
        queryWrapper.like(ReLedgerInfo::getUnit, reLedgerInfoPageParam.getUnit(), StringUtil.isNotBlank(reLedgerInfoPageParam.getUnit()));
        if (StrUtil.isNotEmpty(reLedgerInfoPageParam.getBuildCode())){
            String[] split = reLedgerInfoPageParam.getBuildCode().split(",");
            queryWrapper.in(ReLedgerInfo::getBuildCode, Arrays.asList(split));
        }
        if (ObjectUtil.isNotEmpty(reLedgerInfoPageParam.getSubscribeTime())) {
            queryWrapper.ge(ReLedgerInfo::getSubscribeTime, reLedgerInfoPageParam.getSubscribeTime()[0]);
            queryWrapper.le(ReLedgerInfo::getSubscribeTime, reLedgerInfoPageParam.getSubscribeTime()[1]);
        }
        queryWrapper.eq(ReLedgerInfo::getProjectId, reLedgerInfoPageParam.getProjectId(), StringUtil.isNotBlank(reLedgerInfoPageParam.getProjectId()));
        if (ObjectUtil.isAllNotEmpty(reLedgerInfoPageParam.getSortField(), reLedgerInfoPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reLedgerInfoPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reLedgerInfoPageParam.getSortField()), reLedgerInfoPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReLedgerInfo::getId);
        }
        Page<ReLedgerInfo> page = this.page(CommonPageRequest.defaultPage(), queryWrapper);
        page.getRecords().forEach(s -> {
            String id = s.getId();
            String contractType = s.getContractType();

            // 🔧 修复：添加租赁合同的处理逻辑
            if ("1".equals(contractType)) {
                // 销售合同
                reSalesContractService.getOneOpt(QueryWrapper.create().eq(ReSalesContract::getLedgerId, id))
                    .ifPresent(reSalesContract -> {
                        s.setTotalHousePrice(reSalesContract.getTotalHousePrice());
                    });
            } else if ("2".equals(contractType)) {
                // 租赁合同 - 之前缺失的处理逻辑
                reLeaseContractService.getOneOpt(QueryWrapper.create().eq(ReLeaseContract::getLedgerId, id))
                    .ifPresent(reLeaseContract -> {
                        // 使用租赁总价作为房款合计
                        s.setTotalHousePrice(reLeaseContract.getLeaseTotalPrice());
                    });
            } else if ("3".equals(contractType)) {
                // 安置合同
                rePlacementContractService.getOneOpt(QueryWrapper.create().eq(RePlacementContract::getLedgerId, id))
                    .ifPresent(rePlacementContract -> {
                        s.setTotalHousePrice(rePlacementContract.getTotalHousePrice());
                    });
            }

            // 兼容旧逻辑：如果按房屋类型处理
            if (s.getTotalHousePrice() == null) {
                if ("1".equals(s.getHouseType())) {
                    // 住宅 - 安置合同
                    rePlacementContractService.getOneOpt(QueryWrapper.create().eq(RePlacementContract::getLedgerId, id))
                        .ifPresent(rePlacementContract -> {
                            s.setTotalHousePrice(rePlacementContract.getTotalHousePrice());
                        });
                } else {
                    // 商业/储藏间 - 销售合同
                    reSalesContractService.getOneOpt(QueryWrapper.create().eq(ReSalesContract::getLedgerId, id))
                        .ifPresent(reSalesContract -> {
                            s.setTotalHousePrice(reSalesContract.getTotalHousePrice());
                        });
                }
            }

            // 🔧 修复：计算欠款金额，与详情页面保持一致
            calculateAndSetDebt(s);
        });
        return page;
    }

    @Tran
    @Override
    public void add(ReLedgerInfoAddParam reLedgerInfoAddParam) {
        ReLedgerInfo reLedgerInfo = BeanUtil.toBean(reLedgerInfoAddParam, ReLedgerInfo.class);
        this.save(reLedgerInfo);
    }

    @Tran
    @Override
    public void edit(ReLedgerInfoEditParam reLedgerInfoEditParam) {
        ReLedgerInfo reLedgerInfo = this.queryEntity(reLedgerInfoEditParam.getId());
        BeanUtil.copyProperties(reLedgerInfoEditParam, reLedgerInfo);
        this.updateById(reLedgerInfo);
    }

    @Tran
    @Override
    public void delete(List<ReLedgerInfoIdParam> reLedgerInfoIdParamList) {
        // 执行删除
        reLedgerInfoIdParamList.forEach(reLedgerInfoIdParam -> {
            String id = reLedgerInfoIdParam.getId();
            ReLedgerInfo byId = this.getById(id);
            if (null != byId) {
                //重置房屋
                reHouseService.resetHouse(byId.getHouseId());
            }

        });

        this.removeByIds(CollStreamUtil.toList(reLedgerInfoIdParamList, ReLedgerInfoIdParam::getId));
    }

    @Override
    public ReLedgerInfo detail(ReLedgerInfoIdParam reLedgerInfoIdParam) {
        return this.queryEntity(reLedgerInfoIdParam.getId());
    }

    @Override
    public ReLedgerInfo queryEntity(String id) {
        ReLedgerInfo reLedgerInfo = this.getById(id);
        if (ObjectUtil.isEmpty(reLedgerInfo)) {
            throw new CommonException("房屋台账信息不存在，id值为：{}", id);
        }
        return reLedgerInfo;
    }

    @Override
    @Tran
    public void addBySaleControl(ReLedgerInfoXiaoKongParam reLedgerInfoXiaoKongParam) {
        /** 通过房屋id查询房屋信息 **/
        ReHouse reHouse = reHouseService.queryEntity(reLedgerInfoXiaoKongParam.getHouseId());
        /** 先增加台账信息  其他地方需要台账 **/
        ReLedgerInfo reLedgerInfo = new ReLedgerInfo();
        reLedgerInfo.setVillageId(reLedgerInfoXiaoKongParam.getCustomerInfo().getVillageId());
        reLedgerInfo.setCode(reLedgerInfoXiaoKongParam.getCustomerInfo().getCode());
        reLedgerInfo.setName(reLedgerInfoXiaoKongParam.getCustomerInfo().getName());
        reLedgerInfo.setIdCard(reLedgerInfoXiaoKongParam.getCustomerInfo().getIdCard());
        reLedgerInfo.setPhone(reLedgerInfoXiaoKongParam.getCustomerInfo().getPhone());
        reLedgerInfo.setBuildCode(reHouse.getBuildCode());
        reLedgerInfo.setFloor(reHouse.getFloor());
        reLedgerInfo.setUnit(reHouse.getUnit());
        reLedgerInfo.setHouseNumber(reHouse.getHouseNumber());
        reLedgerInfo.setArea(reHouse.getActualBuildArea());
        reLedgerInfo.setContractType(reLedgerInfoXiaoKongParam.getSignType());
        BigDecimal dealUnitPrice = null;

        /** 查询交款信息 **/
        List<RePaymentInfo> rePaymentInfos1 = null;
        String paymentInfoIds = reLedgerInfoXiaoKongParam.getPaymentInfoIds();
        if (StrUtil.isNotEmpty(paymentInfoIds)) {
            String[] split1 = paymentInfoIds.split(",");
            rePaymentInfos1 = rePaymentInfoService.getMapper().selectListByIds(Arrays.asList(split1));
            /** 添加合计交款金额 **/
            reLedgerInfo.setTotalPayment(rePaymentInfos1.stream().map(RePaymentInfo::getPaymentAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        } else {
            reLedgerInfo.setTotalPayment(BigDecimal.ZERO);
        }
        // 房款合计
        BigDecimal totalHousePrice = null;
        /** 判断是什么类型的签约类型 获取对应的单价 **/
        if ("1".equals(reLedgerInfoXiaoKongParam.getSignType())) {
            dealUnitPrice = reLedgerInfoXiaoKongParam.getSaleSign().getContractUnitPrice();
            totalHousePrice = reLedgerInfoXiaoKongParam.getSaleSign().getTotalHousePrice();
            if (reHouse.getHouseType().equals("3")) {
                totalHousePrice = reLedgerInfoXiaoKongParam.getSaleSign().getContractPrice();
            }
        } else if ("2".equals(reLedgerInfoXiaoKongParam.getSignType())) {
            dealUnitPrice = reLedgerInfoXiaoKongParam.getLeaseSign().getLeaseUnitPrice();
            totalHousePrice = reLedgerInfoXiaoKongParam.getLeaseSign().getTotalHousePrice();
        } else if ("3".equals(reLedgerInfoXiaoKongParam.getSignType())) {
            dealUnitPrice = reLedgerInfoXiaoKongParam.getPlaceSign().getContractPrice().divide(reLedgerInfo.getArea(), 2, RoundingMode.HALF_UP);
            totalHousePrice = reLedgerInfoXiaoKongParam.getPlaceSign().getTotalHousePrice();
        }
        reLedgerInfo.setContractPrice(totalHousePrice);
        reLedgerInfo.setDealUnitPrice(null == dealUnitPrice ? null : dealUnitPrice.setScale(2, RoundingMode.HALF_UP));
        reLedgerInfo.setHouseId(reHouse.getId());
        reLedgerInfo.setIsHistory(false);
        reLedgerInfo.setProjectId(reHouse.getProjectId());
        reLedgerInfo.setHouseType(reHouse.getHouseType());
        /** 计算已经支付的记录 **/
        BigDecimal totalPayment = reLedgerInfo.getTotalPayment();
        try {
            if (totalPayment.compareTo(totalHousePrice) >= 0) {
                reLedgerInfo.setStatus("3");
            } else {
                reLedgerInfo.setStatus("2");
            }
        } catch (NullPointerException e) {
            reLedgerInfo.setStatus("2");
        }
        reLedgerInfo.setContractTime(reLedgerInfoXiaoKongParam.getCustomerInfo().getContractTime());
        reLedgerInfo.setSubscribeTime(reLedgerInfoXiaoKongParam.getCustomerInfo().getSubscribeTime());
        this.save(reLedgerInfo);
        /** 通过客户信息给客户信息数据添加数据 **/
        ReCustomerAddParam customerInfo = reLedgerInfoXiaoKongParam.getCustomerInfo();
        ReCustomerInfo reCustomerInfo = new ReCustomerInfo();
        reCustomerInfo.setName(customerInfo.getName());
        reCustomerInfo.setIdCard(customerInfo.getIdCard());
        reCustomerInfo.setPhone(customerInfo.getPhone());
        int insert = reCustomerInfoMapper.insert(reCustomerInfo);
        if (insert == 0) {
            throw new CommonException("添加客户信息失败");
        }
        customerInfo.setLedgerId(reLedgerInfo.getId());
        customerInfo.setCustomerId(reCustomerInfo.getId());
        customerInfo.setProjectId(reHouse.getProjectId());
        reCustomerService.add(customerInfo);
        String qianYueId;
        /** 添加签约信息 **/
        if ("1".equals(reLedgerInfoXiaoKongParam.getSignType())) {
            ReSalesContractAddParam saleSign = reLedgerInfoXiaoKongParam.getSaleSign();
            saleSign.setLedgerId(reLedgerInfo.getId());
            String add = reSalesContractService.add(reLedgerInfoXiaoKongParam.getSaleSign());
            qianYueId = add;
        } else if ("2".equals(reLedgerInfoXiaoKongParam.getSignType())) {
            ReLeaseContractAddParam leaseSign = reLedgerInfoXiaoKongParam.getLeaseSign();
            leaseSign.setLedgerId(reLedgerInfo.getId());
            String add = reLeaseContractService.add(reLedgerInfoXiaoKongParam.getLeaseSign());
            qianYueId = add;
        } else if ("3".equals(reLedgerInfoXiaoKongParam.getSignType())) {
            RePlacementContractAddParam placeSign = reLedgerInfoXiaoKongParam.getPlaceSign();
            placeSign.setLedgerId(reLedgerInfo.getId());
            String add = rePlacementContractService.add(reLedgerInfoXiaoKongParam.getPlaceSign());
            qianYueId = add;
        } else {
            qianYueId = "";
        }
        /** 将交款信息添加上台账id **/
        String installmentPaymentIds = reLedgerInfoXiaoKongParam.getInstallmentPaymentIds();
        if (StrUtil.isNotEmpty(installmentPaymentIds)) {
            String[] split = installmentPaymentIds.split(",");
            List<RePaymentInfo> rePaymentInfos = rePaymentInfoService.getMapper().selectListByIds(Arrays.asList(split));
            final String finalQianYueId = qianYueId;
            rePaymentInfos.forEach(rePaymentInfo -> {
                rePaymentInfo.setLedgerId(reLedgerInfo.getId());
                rePaymentInfo.setPaymentType("-1");
                rePaymentInfo.setContractId(finalQianYueId);
            });
            rePaymentInfoService.updateBatch(rePaymentInfos);
        }
        if (null != rePaymentInfos1) {
            rePaymentInfos1.forEach(rePaymentInfo -> {
                rePaymentInfo.setLedgerId(reLedgerInfo.getId());
                rePaymentInfo.setContractId(qianYueId);
            });
            rePaymentInfoService.updateBatch(rePaymentInfos1);
        }
        /** 添加合同信息 **/
        ReContractInfoAddParam contractInfo = reLedgerInfoXiaoKongParam.getContractInfo();
        if (null != contractInfo) {
            contractInfo.setLedgerId(reLedgerInfo.getId());
            reContractInfoService.add(contractInfo);
        }
        /** 修改房屋信息 **/
        reHouse.setCustomerName(reLedgerInfoXiaoKongParam.getCustomerInfo().getName());
        reHouse.setCustomerPhone(reLedgerInfoXiaoKongParam.getCustomerInfo().getPhone());
        reHouse.setSalesTotalPrice(reLedgerInfoXiaoKongParam.getSaleSign().getContractPrice());
        reHouse.setSalesUnitPrice(reLedgerInfoXiaoKongParam.getSaleSign().getContractUnitPrice());
        reHouse.setStatus(reLedgerInfo.getStatus());
        reHouseService.updateById(reHouse);
        reProjectDetailServiceAsync.updateOrInsertDetail(reHouse, reHouse.getProjectId());
    }


    @Override
    public ReLedgerInfoXiaoKongParamVo queryEntityByHouseId(String id) {
        ReLedgerInfo byId = this.getOneOpt(QueryWrapper.create().eq(ReLedgerInfo::getHouseId, id).eq(ReLedgerInfo::getIsHistory, false)).orElseThrow(() -> new CommonException("台账信息不存在"));
        if (null == byId || byId.getIsHistory()) {
            throw new CommonException("台账信息不存在");
        }
        return queryDetail(byId);
    }

    @Override
    public ReLedgerInfoXiaoKongParamVo queryEntityById(String id) {
        ReLedgerInfo byId = this.getById(id);
        return this.queryDetail(byId);
    }

    @Override
    public JSONObject imporBussinesstExcel(UploadedFile file, String projectId) {
        File tempFile = FileUtil.writeBytes(IoUtil.readBytes(file.getContent()), FileUtil.file(FileUtil.getTmpDir() +
                FileUtil.FILE_SEPARATOR +System.currentTimeMillis()+ "tempBussinessExcel.xlsx"));
//        JSONArray catSettel = dictApi.getDictListByType("car_settle");
        JSONObject res = new JSONObject();
        EasyExcel.read(tempFile).head(ReBussnessLedgerInfoImportParam.class).registerReadListener(new ImportBussnessLedgerInfoListener(projectId,res)).headRowNumber(3).sheet(0).doRead();
        return res;
    }

    @Override
    public JSONObject importStoreroomExcel(UploadedFile file, String projectId) {
        File tempFile = FileUtil.writeBytes(IoUtil.readBytes(file.getContent()), FileUtil.file(FileUtil.getTmpDir() +
                FileUtil.FILE_SEPARATOR + "tempStoreExcel.xlsx"));
//        JSONArray carType = dictApi.getDictListByType("car_type");
//        JSONArray catSettel = dictApi.getDictListByType("car_settle");
        JSONObject res = new JSONObject();
        EasyExcel.read(tempFile).head(ReStoreRoomLedgerInfoImportParam.class).registerReadListener(new ImportStoreRoomLedgerInfoListener(projectId,res)).headRowNumber(3).sheet(0).doRead();
        return res;
    }

    @Override
    public JSONObject importLeaseBusinessExcel(UploadedFile file, String projectId) {
        File tempFile = FileUtil.writeBytes(IoUtil.readBytes(file.getContent()), FileUtil.file(FileUtil.getTmpDir() +
                FileUtil.FILE_SEPARATOR +System.currentTimeMillis()+ "tempBussiness2Excel.xlsx"));
        JSONObject res = new JSONObject();
        EasyExcel.read(tempFile).head(ReBussnessLeaseLedgerInfoImportParam.class).registerReadListener(new ImportBussnessLeaseLedgerInfoListener(projectId,res)).headRowNumber(2).sheet(0).doRead();
        return res;
    }

    public ReLedgerInfoXiaoKongParamVo queryDetail(ReLedgerInfo byId) {
        ReLedgerInfoXiaoKongParamVo reLedgerInfoXiaoKongParamVo = new ReLedgerInfoXiaoKongParamVo();
        reLedgerInfoXiaoKongParamVo.setLedgerInfo(byId);

        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ReCustomer::getLedgerId, byId.getId());
        ReCustomer reCustomer = reCustomerService.getOne(queryWrapper);
        if (null != reCustomer) {
            reLedgerInfoXiaoKongParamVo.setCustomerInfo(reCustomer);
        }
        String qianYueId = null;
        String contractType = byId.getContractType();
        if ("1".equals(contractType)) {
            queryWrapper = QueryWrapper.create();
            queryWrapper.eq(ReSalesContract::getLedgerId, byId.getId());
            ReSalesContract reSalesContract = reSalesContractService.getOne(queryWrapper);
            if (null != reSalesContract) {
                qianYueId = reSalesContract.getId();
                reLedgerInfoXiaoKongParamVo.setSaleSign(reSalesContract);
            }
        } else if ("2".equals(contractType)) {
            queryWrapper = QueryWrapper.create();
            queryWrapper.eq(ReLeaseContract::getLedgerId, byId.getId());
            ReLeaseContract reLeaseContract = reLeaseContractService.getOne(queryWrapper);
            if (null != reLeaseContract) {
                qianYueId = reLeaseContract.getId();
                reLedgerInfoXiaoKongParamVo.setLeaseSign(reLeaseContract);
            }
        } else if ("3".equals(contractType)) {
            queryWrapper = QueryWrapper.create();
            queryWrapper.eq(RePlacementContract::getLedgerId, byId.getId());
            RePlacementContract rePlacementContract = rePlacementContractService.getOne(queryWrapper);
            if (null != rePlacementContract) {
                qianYueId = rePlacementContract.getId();
                reLedgerInfoXiaoKongParamVo.setPlaceSign(rePlacementContract);
            }
        }
        if (null != qianYueId) {
            queryWrapper = QueryWrapper.create();
            queryWrapper.eq(RePaymentInfo::getLedgerId, byId.getId());
            queryWrapper.eq(RePaymentInfo::getContractId, qianYueId);
            queryWrapper.eq(RePaymentInfo::getPaymentType, "-1");
            List<RePaymentInfo> rePaymentInfos = rePaymentInfoService.list(queryWrapper);
            if (CollectionUtil.isNotEmpty(rePaymentInfos)) {
                if ("1".equals(contractType)) {
                    reLedgerInfoXiaoKongParamVo.getSaleSign().setPaymentInfoList(rePaymentInfos);
                } else if ("2".equals(contractType)) {
                    reLedgerInfoXiaoKongParamVo.getLeaseSign().setPaymentInfoList(rePaymentInfos);
                } else if ("3".equals(contractType)) {
                    reLedgerInfoXiaoKongParamVo.getPlaceSign().setPaymentInfoList(rePaymentInfos);
                }
            }
        }

        queryWrapper = QueryWrapper.create();
        queryWrapper.eq(RePaymentInfo::getLedgerId, byId.getId());
        queryWrapper.ne(RePaymentInfo::getPaymentType, "-1");
        List<RePaymentInfo> rePaymentInfos1 = rePaymentInfoService.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(rePaymentInfos1)) {
            reLedgerInfoXiaoKongParamVo.setPaymentInfos(rePaymentInfos1);
        }

        queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ReContractInfo::getLedgerId, byId.getId());
        ReContractInfo reContractInfo = reContractInfoService.getOne(queryWrapper);
        if (null != reContractInfo) {
            reLedgerInfoXiaoKongParamVo.setContractInfo(reContractInfo);
        }

        queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ReLedgerInfo::getHouseId, byId.getHouseId());
        queryWrapper.eq(ReLedgerInfo::getIsHistory, true);
        List<ReLedgerInfo> reLedgerInfos = this.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(reLedgerInfos)) {
            reLedgerInfoXiaoKongParamVo.setHistoryLedgerInfo(reLedgerInfos);
        }
        return reLedgerInfoXiaoKongParamVo;
    }

    /**
     * 🔧 修复：计算并设置欠款金额，与详情页面保持一致
     * 根据合同类型使用不同的总价字段，减去实际已交款总额
     *
     * @param ledgerInfo 房屋台账信息
     */
    private void calculateAndSetDebt(ReLedgerInfo ledgerInfo) {
        if (ledgerInfo == null) {
            return;
        }

        String contractType = ledgerInfo.getContractType();
        final BigDecimal[] totalHousePrice = {BigDecimal.ZERO}; // 使用数组来解决lambda表达式中的变量修改问题
        BigDecimal totalPayment = ledgerInfo.getTotalPayment() != null ? ledgerInfo.getTotalPayment() : BigDecimal.ZERO;

        try {
            // 根据合同类型获取正确的总价，与详情页面逻辑保持一致
            if ("1".equals(contractType)) {
                // 销售合同：使用销售合同的totalHousePrice
                reSalesContractService.getOneOpt(QueryWrapper.create().eq(ReSalesContract::getLedgerId, ledgerInfo.getId()))
                    .ifPresent(reSalesContract -> {
                        totalHousePrice[0] = reSalesContract.getTotalHousePrice() != null ? reSalesContract.getTotalHousePrice() : BigDecimal.ZERO;
                    });
            } else if ("2".equals(contractType)) {
                // 租赁合同：使用租赁合同的leaseTotalPrice
                reLeaseContractService.getOneOpt(QueryWrapper.create().eq(ReLeaseContract::getLedgerId, ledgerInfo.getId()))
                    .ifPresent(reLeaseContract -> {
                        totalHousePrice[0] = reLeaseContract.getLeaseTotalPrice() != null ? reLeaseContract.getLeaseTotalPrice() : BigDecimal.ZERO;
                    });
            } else if ("3".equals(contractType)) {
                // 安置合同：使用安置合同的totalHousePrice
                rePlacementContractService.getOneOpt(QueryWrapper.create().eq(RePlacementContract::getLedgerId, ledgerInfo.getId()))
                    .ifPresent(rePlacementContract -> {
                        totalHousePrice[0] = rePlacementContract.getTotalHousePrice() != null ? rePlacementContract.getTotalHousePrice() : BigDecimal.ZERO;
                    });
            }

            // 计算实际已交款总额（从支付记录中计算，与详情页面一致）
            BigDecimal actualPayment = BigDecimal.ZERO;
            List<RePaymentInfo> paymentList = rePaymentInfoService.list(
                QueryWrapper.create().eq(RePaymentInfo::getLedgerId, ledgerInfo.getId())
            );

            if (paymentList != null && !paymentList.isEmpty()) {
                actualPayment = paymentList.stream()
                    .map(payment -> payment.getPaymentAmount() != null ? payment.getPaymentAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            // 计算欠款：总价 - 实际已交款
            BigDecimal debt = totalHousePrice[0].subtract(actualPayment);

            // 欠款不能为负数
            ledgerInfo.setDebt(debt.compareTo(BigDecimal.ZERO) >= 0 ? debt : BigDecimal.ZERO);

            log.debug("计算欠款：台账ID={}, 合同类型={}, 总价={}, 实际已交款={}, 欠款={}",
                    ledgerInfo.getId(), contractType, totalHousePrice[0], actualPayment, ledgerInfo.getDebt());

        } catch (Exception e) {
            log.warn("计算欠款金额时出错，台账ID：{}, 错误：{}", ledgerInfo.getId(), e.getMessage());
            // 出错时设置为0
            ledgerInfo.setDebt(BigDecimal.ZERO);
        }
    }
}
