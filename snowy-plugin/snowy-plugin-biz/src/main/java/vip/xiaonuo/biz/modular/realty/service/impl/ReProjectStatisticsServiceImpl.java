package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.query.QueryWrapper;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.entity.table.ReHouseStatisticsTableInfo;
import vip.xiaonuo.biz.modular.realty.param.ReQueryByDateParam;
import vip.xiaonuo.biz.modular.realty.service.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static vip.xiaonuo.biz.modular.realty.entity.table.ReContractInfoTableDef.RE_CONTRACT_INFO;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReLeaseContractTableDef.RE_LEASE_CONTRACT;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReLedgerInfoTableDef.RE_LEDGER_INFO;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReParkLedgerInfoTableDef.RE_PARK_LEDGER_INFO;
import static vip.xiaonuo.biz.modular.realty.entity.table.ReParkTableDef.RE_PARK;

/**
 * <AUTHOR>
 * @date 2024/9/5 9:11
 */
@Component
public class ReProjectStatisticsServiceImpl implements ReProjectStatisticsService {

    @Inject
    private ReHouseService reHouseService;

    @Inject
    private ReLedgerInfoService reLedgerInfoService;

    @Inject
    private RePlacementContractService rePlacementContractService;

    @Inject
    private ReSalesContractService reSalesContractService;

    @Inject
    private ReLeaseContractService reLeaseContractService;

    @Inject
    private RePaymentInfoService rePaymentInfoService;

    @Inject
    private ReCustomerService reCustomerService;

    @Inject
    private ReParkAreaService reParkAreaService;

    @Inject
    private ReContractInfoService reContractInfoService;

    /**
     * 住宅统计信息 - 优化版本，解决N+1查询问题
     */
    @Override
    public ReHouseStatisticsDataInfo residentialStatistics(String projectId) {
        // 1. 查询所有住宅类型的房屋
        List<ReHouse> houseList = reHouseService.list(QueryWrapper.create()
                .eq(ReHouse::getHouseType, "1")
                .eq(ReHouse::getProjectId, projectId));

        // 2. 创建结果对象并设置基础统计信息
        ReHouseStatisticsDataInfo reHouseStatisticsDataInfo = new ReHouseStatisticsDataInfo();
        reHouseStatisticsDataInfo.setProjectTotalNumber(houseList.size());

        // 3. 计算总面积
        BigDecimal projectTotalArea = houseList.stream()
                .map(ReHouse::getActualBuildArea)
                .filter(area -> area != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        reHouseStatisticsDataInfo.setProjectTotalArea(projectTotalArea);

        // 4. 计算未售数量和面积
        long projectUnsoldNumber = houseList.stream()
                .filter(reHouse -> "1".equals(reHouse.getStatus()))
                .count();
        reHouseStatisticsDataInfo.setProjectUnsoldNumber(projectUnsoldNumber);

        BigDecimal projectUnsoldArea = houseList.stream()
                .filter(reHouse -> "1".equals(reHouse.getStatus()))
                .map(ReHouse::getActualBuildArea)
                .filter(area -> area != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        reHouseStatisticsDataInfo.setProjectUnsoldArea(projectUnsoldArea);

        // 5. 批量查询所有相关的台账信息（优化点1：避免N+1查询）
        List<String> houseIds = houseList.stream()
                .map(ReHouse::getId)
                .collect(Collectors.toList());

        List<ReLedgerInfo> ledgerList = Collections.emptyList();
        if (!houseIds.isEmpty()) {
            ledgerList = reLedgerInfoService.list(QueryWrapper.create()
                    .in(ReLedgerInfo::getHouseId, houseIds)
                    .eq(ReLedgerInfo::getIsHistory, false));
        }

        // 如果没有台账信息，直接返回基础统计
        if (ledgerList.isEmpty()) {
            reHouseStatisticsDataInfo.setArrearsAmount(BigDecimal.ZERO);
            reHouseStatisticsDataInfo.setExpectedPaymentAmount(getExpectedPaymentAmountWithoutHistory(houseList));
            return reHouseStatisticsDataInfo;
        }

        // 6. 批量查询所有相关的合同信息（优化点2：批量查询替代循环查询）
        List<String> ledgerIds = ledgerList.stream()
                .map(ReLedgerInfo::getId)
                .collect(Collectors.toList());

        // 预先查询所有可能的合同价格
        Map<String, BigDecimal> salesContractPrices = Collections.emptyMap();
        Map<String, BigDecimal> leaseContractPrices = Collections.emptyMap();
        Map<String, BigDecimal> placementContractPrices = Collections.emptyMap();

        if (!ledgerIds.isEmpty()) {
            // 销售合同批量查询
            salesContractPrices = reSalesContractService.list(QueryWrapper.create()
                    .in(ReSalesContract::getLedgerId, ledgerIds))
                    .stream()
                    .filter(contract -> contract.getTotalHousePrice() != null)
                    .collect(Collectors.toMap(
                            ReSalesContract::getLedgerId,
                            ReSalesContract::getTotalHousePrice,
                            (existing, replacement) -> existing // 如果有重复，保留第一个
                    ));

            // 租赁合同批量查询
            leaseContractPrices = reLeaseContractService.list(QueryWrapper.create()
                    .in(ReLeaseContract::getLedgerId, ledgerIds))
                    .stream()
                    .filter(contract -> contract.getTotalHousePrice() != null)
                    .collect(Collectors.toMap(
                            ReLeaseContract::getLedgerId,
                            ReLeaseContract::getTotalHousePrice,
                            (existing, replacement) -> existing
                    ));

            // 安置合同批量查询
            placementContractPrices = rePlacementContractService.list(QueryWrapper.create()
                    .in(RePlacementContract::getLedgerId, ledgerIds))
                    .stream()
                    .filter(contract -> contract.getTotalHousePrice() != null)
                    .collect(Collectors.toMap(
                            RePlacementContract::getLedgerId,
                            RePlacementContract::getTotalHousePrice,
                            (existing, replacement) -> existing
                    ));
        }

        // 7. 计算总支付金额和总价（优化点3：在内存中进行计算，避免重复查询）
        BigDecimal totalPayment = BigDecimal.ZERO;
        BigDecimal totalPrice = BigDecimal.ZERO;

        for (ReLedgerInfo ledger : ledgerList) {
            // 累加已支付金额
            if (ledger.getTotalPayment() != null) {
                totalPayment = totalPayment.add(ledger.getTotalPayment());
            }

            // 根据合同类型获取总价
            String contractType = ledger.getContractType();
            String ledgerId = ledger.getId();
            BigDecimal contractPrice = null;

            if ("1".equals(contractType)) {
                contractPrice = salesContractPrices.get(ledgerId);
            } else if ("2".equals(contractType)) {
                contractPrice = leaseContractPrices.get(ledgerId);
            } else if ("3".equals(contractType)) {
                contractPrice = placementContractPrices.get(ledgerId);
            }

            if (contractPrice != null) {
                totalPrice = totalPrice.add(contractPrice);
            }
        }

        // 8. 设置欠款金额
        BigDecimal arrearsAmount = totalPrice.subtract(totalPayment);
        reHouseStatisticsDataInfo.setArrearsAmount(arrearsAmount);

        // 9. 设置预期支付金额（优化点4：复用已查询的房屋列表，避免重复查询）
        reHouseStatisticsDataInfo.setExpectedPaymentAmount(getExpectedPaymentAmountWithoutHistory(houseList));

        return reHouseStatisticsDataInfo;
    }

    /**
     * 计算预期支付金额（不包含历史数据）- 优化版本
     * @param houseList 已查询的房屋列表，避免重复查询
     */
    public BigDecimal getExpectedPaymentAmountWithoutHistory(List<ReHouse> houseList) {
        // 计算未售房屋的市场价总和
        BigDecimal expectedPaymentAmount = houseList.stream()
                .filter(reHouse ->
                        "1".equals(reHouse.getStatus()) && // 未售状态，使用equals避免NPE
                                reHouse.getMarketPrice() != null &&
                                reHouse.getActualBuildArea() != null
                )
                .map(reHouse ->
                        reHouse.getMarketPrice().multiply(reHouse.getActualBuildArea())
                )
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return expectedPaymentAmount.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 计算预期支付金额（不包含历史数据）- 保留原方法签名以兼容其他调用
     */
    public BigDecimal getExpectedPaymentAmountWithoutHistory(String projectId) {
        // 获取项目下的住宅列表
        List<ReHouse> list = reHouseService.list(QueryWrapper.create()
                .eq(ReHouse::getHouseType, "1")
                .eq(ReHouse::getProjectId, projectId));

        return getExpectedPaymentAmountWithoutHistory(list);
    }


    @Override
    public List<ReHouseStatisticsTableInfo> houseTypeStatistics(ReQueryByDateParam reQueryByDateParam) {
        List<ReHouse> list = reHouseService.list(QueryWrapper.create()
                .eq(ReHouse::getHouseType, "1")
                .eq(ReHouse::getProjectId, reQueryByDateParam.getProjectId()));

        List<ReHouseStatisticsTableInfo> houseStatisticsTableInfo = new ArrayList<>();

        // More efficient way to get distinct house layouts
        List<String> distinctHouseLayouts = list.stream()
                .map(ReHouse::getHouseLayout)
                .filter(houseLayout -> houseLayout != null && !houseLayout.isEmpty()) // Handle null or empty house layouts
                .distinct()
                .collect(Collectors.toList());


        for (String houseLayout : distinctHouseLayouts) { // Use a for-each loop for better readability
            ReHouseStatisticsTableInfo reHouseStatisticsTableInfo = new ReHouseStatisticsTableInfo();
            reHouseStatisticsTableInfo.setHouseLayout(houseLayout);

            List<ReHouse> filteredList = list.stream()
                    .filter(r -> houseLayout.equals(r.getHouseLayout())) // Direct comparison is cleaner
                    .collect(Collectors.toList());

            reHouseStatisticsTableInfo.setTotalNumber(filteredList.size());

            // Handle potential null actualBuildArea and provide a default value (ZERO)
            BigDecimal totalArea = filteredList.stream()
                    .map(ReHouse::getActualBuildArea)
                    .filter(area -> area != null) // Filter out null areas.
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
            reHouseStatisticsTableInfo.setTotalArea(totalArea);


            long soldNumber = filteredList.stream()
                    .filter(r -> "3".equals(r.getStatus()) || "2".equals(r.getStatus())) // Direct string comparison
                    .count();
            reHouseStatisticsTableInfo.setSoldNumber(soldNumber);

            BigDecimal soldArea = filteredList.stream()
                    .filter(r -> "3".equals(r.getStatus()) || "2".equals(r.getStatus()))
                    .map(ReHouse::getActualBuildArea)
                    .filter(area -> area != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
            reHouseStatisticsTableInfo.setSoldArea(soldArea);

            reHouseStatisticsTableInfo.setUnsoldNumber(filteredList.size() - soldNumber);
            reHouseStatisticsTableInfo.setUnsoldArea(totalArea.subtract(soldArea));

            houseStatisticsTableInfo.add(reHouseStatisticsTableInfo);
        }

        return houseStatisticsTableInfo;
    }

    @Override
    public ReHouseSalesStatisticsDataInfo houseSalesStatistics(ReQueryByDateParam reQueryByDateParam) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ReLedgerInfo::getHouseType, "1");
        queryWrapper.eq(ReLedgerInfo::getIsHistory, false);
        queryWrapper.eq(ReLedgerInfo::getProjectId, reQueryByDateParam.getProjectId());
        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateDay())) {
            queryWrapper.and("SUBSCRIBE_TIME >= ?", reQueryByDateParam.getStartDateDay());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateDay())) {
            queryWrapper.and("SUBSCRIBE_TIME <= ?", reQueryByDateParam.getEndDateDay());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateTimeMinute())) {
            queryWrapper.and("SUBSCRIBE_TIME >= ?", reQueryByDateParam.getStartDateTimeMinute());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTimeMinute())) {
            queryWrapper.and("SUBSCRIBE_TIME <= ?", reQueryByDateParam.getEndDateTimeMinute());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTimeMinute())) {
            queryWrapper.and("date_format(SUBSCRIBE_TIME,'%Y-%m-%d %H:%i:%s') >= ?", reQueryByDateParam.getStartDateTime());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTime())) {
            queryWrapper.and("date_format(SUBSCRIBE_TIME,'%Y-%m-%d %H:%i:%s') <= ?", reQueryByDateParam.getEndDateTime());
        }
        List<ReLedgerInfo> list1 = reLedgerInfoService.list(queryWrapper);
        List<String> collect2 = list1.stream().map(s -> s.getHouseId()).collect(Collectors.toList());
        List<ReHouse> list = ListUtil.empty();
        if (CollectionUtil.isNotEmpty(collect2)) {
            list = reHouseService.listByIds(collect2);
        }
        List<ReHouse> collect = list.stream().filter(r -> r.getStatus().equals("2") || r.getStatus().equals("3")).collect(Collectors.toList());
        ReHouseSalesStatisticsDataInfo reHouseSalesStatisticsDataInfo = new ReHouseSalesStatisticsDataInfo();
        reHouseSalesStatisticsDataInfo.setSoldNumber(collect.size());
        reHouseSalesStatisticsDataInfo.setSoldArea(collect.stream().map(ReHouse::getActualBuildArea).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP));
        // 安置人口
        List<String> collect1 = list1.stream().filter(s -> s.getContractType().equals("3")).map(s -> s.getId()).collect(Collectors.toList());
        List<ReCustomer> list2 = ListUtil.empty();
        if (collect1.size() > 0) {
            list2 = reCustomerService.list(QueryWrapper.create().in(ReCustomer::getLedgerId, collect1));
        }
        Optional<Integer> reduce1 = list2.stream().filter(s -> null != s.getQuotaList()).map(s -> s.getQuotaList().size()).reduce(Integer::sum);
        reHouseSalesStatisticsDataInfo.setPopulation(reduce1.orElse(0));
        //签约数量
        AtomicLong i = new AtomicLong(0);
        List<String> collect3 = list1.stream().filter(Objects::nonNull).map(ReLedgerInfo::getId).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(collect3)) {
            QueryWrapper iqw = new QueryWrapper();
            iqw.in(ReContractInfo::getLedgerId, collect3);
            List<ReContractInfo> reContractInfos = reContractInfoService.getMapper().selectListByQuery(iqw);
            if (CollectionUtil.isNotEmpty(reContractInfos)) {
                reContractInfos.forEach(s -> {
                    if (s.getContractRecordTime() != null) {
                        i.getAndIncrement();
                    }
                });
            }
        }
        reHouseSalesStatisticsDataInfo.setSignNumber(i.get());
        // 安置户数
        reHouseSalesStatisticsDataInfo.setPlacementNumber(list2.stream().map(s -> s.getVillageId() +"-"+ s.getCode()).distinct().count());
        //安置面积
        reHouseSalesStatisticsDataInfo.setPlacementArea(list1.stream().filter(s->s.getContractType().equals("3")).map(s -> s.getArea()).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP));
        //回款金额
        QueryWrapper reduceWrapper = getReduceWrapper("1", reQueryByDateParam,null);
        if (null != reduceWrapper) {
            List<RePaymentInfo> paymentInfos = rePaymentInfoService.list(reduceWrapper);
            BigDecimal reduce = paymentInfos.stream().filter(s -> s.getPaymentAmount() != null).map(s -> s.getPaymentAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
            reHouseSalesStatisticsDataInfo.setPaymentAmount(reduce);
        }else {
            reHouseSalesStatisticsDataInfo.setPaymentAmount(BigDecimal.ZERO);
        }
        return reHouseSalesStatisticsDataInfo;
    }

    @Override
    public ReHouseSalesStatisticsDataInfo businessAndStorageStatistics(String type, ReQueryByDateParam reQueryByDateParam) {
        ReHouseSalesStatisticsDataInfo reHouseSalesStatisticsDataInfo = new ReHouseSalesStatisticsDataInfo();

        // 1. 销售数据查询
        List<ReLedgerInfo> salesList = getSalesListByType(type, reQueryByDateParam, null);

        // 2. 统计销售相关数据
        // 销售套数 - 只统计销售类型（contractType="1"）的台账数据
        List<ReLedgerInfo> soldList = salesList.stream()
                .filter(s -> s.getContractType() != null && "1".equals(s.getContractType()) && !s.getStatus().equals("1"))
                .collect(Collectors.toList());
        reHouseSalesStatisticsDataInfo.setSoldNumber(soldList.size());

        // 销售面积 - 使用与销售套数相同的数据源
        reHouseSalesStatisticsDataInfo.setSoldArea(soldList.stream()
                .map(ReLedgerInfo::getArea)
                .filter(area -> area != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, BigDecimal.ROUND_HALF_UP));

        // 3. 租赁数据处理 - 明确过滤合同类型为2的记录
        List<ReLedgerInfo> rentalList = salesList.stream()
                .filter(s -> s.getContractType() != null && "2".equals(s.getContractType()) && !s.getStatus().equals("1"))
                .collect(Collectors.toList());

        // 租赁套数
        reHouseSalesStatisticsDataInfo.setRentedNumber(rentalList.size());

        // 租赁面积 - 使用与租赁套数相同的数据源
        reHouseSalesStatisticsDataInfo.setRentedArea(rentalList.stream()
                .map(ReLedgerInfo::getArea)
                .filter(area -> area != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, BigDecimal.ROUND_HALF_UP));

        // 4. 查询签约套数
        QueryWrapper queryWrapper2 = buinessAndStorageQueryWrapper2(type, reQueryByDateParam);
        queryWrapper2.eq(ReLedgerInfo::getContractType, "1");
        queryWrapper2.eq(ReLedgerInfo::getIsHistory, false);
        queryWrapper2.leftJoin(RE_CONTRACT_INFO).on(RE_CONTRACT_INFO.LEDGER_ID.eq(RE_LEDGER_INFO.ID));

        // 添加日期条件
        addDateConditions(queryWrapper2, "RE_CONTRACT_INFO.CONTRACT_RECORD_TIME", reQueryByDateParam);

        List<ReLedgerInfo> list3 = reLedgerInfoService.list(queryWrapper2);
        reHouseSalesStatisticsDataInfo.setSignNumber((long) list3.size());

        // 5. 租赁签约套数
        QueryWrapper queryWrapper3 = buinessAndStorageQueryWrapper2(type, reQueryByDateParam);
        queryWrapper3.eq(ReLedgerInfo::getContractType, "2");
        queryWrapper3.leftJoin(RE_LEASE_CONTRACT).on(RE_LEASE_CONTRACT.LEDGER_ID.eq(RE_LEDGER_INFO.ID));

        // 添加日期条件
        addDateConditions(queryWrapper3, "RE_LEASE_CONTRACT.LEASE_START_TIME", reQueryByDateParam);

        List<ReLedgerInfo> list4 = reLedgerInfoService.list(queryWrapper3);
        reHouseSalesStatisticsDataInfo.setRentalSignNumber((long) list4.size());

        // 6. 汇款金额 - 合同类型1
        QueryWrapper reduceWrapper = getReduceWrapper2(type, reQueryByDateParam, "1");
        if (null != reduceWrapper) {
            List<RePaymentInfo> paymentInfos = rePaymentInfoService.list(reduceWrapper);
            BigDecimal reduce = paymentInfos.stream()
                    .filter(s -> s.getPaymentAmount() != null)
                    .map(RePaymentInfo::getPaymentAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            reHouseSalesStatisticsDataInfo.setPaymentAmount(reduce);
        } else {
            reHouseSalesStatisticsDataInfo.setPaymentAmount(BigDecimal.ZERO);
        }

        // 7. 租赁汇款金额 - 使用传入的type参数，而不是硬编码的"3"
        reduceWrapper = getReduceWrapper2(type, reQueryByDateParam, "2");
        if (null != reduceWrapper) {
            List<RePaymentInfo> paymentInfos = rePaymentInfoService.list(reduceWrapper);
            BigDecimal reduce = paymentInfos.stream()
                    .filter(s -> s.getPaymentAmount() != null)
                    .map(RePaymentInfo::getPaymentAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            reHouseSalesStatisticsDataInfo.setRentalPaymentAmount(reduce);
        } else {
            reHouseSalesStatisticsDataInfo.setRentalPaymentAmount(BigDecimal.ZERO);
        }

        return reHouseSalesStatisticsDataInfo;
    }

    /**
     * 统一处理日期条件
     * @param queryWrapper 查询构造器
     * @param timeField 时间字段名
     * @param param 查询参数
     */
    private void addDateConditions(QueryWrapper queryWrapper, String timeField, ReQueryByDateParam param) {
        // 优先使用精确到秒的日期时间参数
        if (StrUtil.isNotEmpty(param.getStartDateTime())) {
            queryWrapper.and("date_format(" + timeField + ",'%Y-%m-%d %H:%i:%s') >= ?", param.getStartDateTime());
        }
        // 其次使用精确到分钟的参数
        else if (StrUtil.isNotEmpty(param.getStartDateTimeMinute())) {
            queryWrapper.and(timeField + " >= ?", param.getStartDateTimeMinute());
        }
        // 最后使用精确到天的参数
        else if (StrUtil.isNotEmpty(param.getStartDateDay())) {
            queryWrapper.and(timeField + " >= ?", param.getStartDateDay());
        }

        // 优先使用精确到秒的日期时间参数
        if (StrUtil.isNotEmpty(param.getEndDateTime())) {
            queryWrapper.and("date_format(" + timeField + ",'%Y-%m-%d %H:%i:%s') <= ?", param.getEndDateTime());
        }
        // 其次使用精确到分钟的参数
        else if (StrUtil.isNotEmpty(param.getEndDateTimeMinute())) {
            queryWrapper.and(timeField + " <= ?", param.getEndDateTimeMinute());
        }
        // 最后使用精确到天的参数
        else if (StrUtil.isNotEmpty(param.getEndDateDay())) {
            queryWrapper.and(timeField + " <= ?", param.getEndDateDay());
        }
    }

    /**
     * 获取销售列表数据
     */
    private List<ReLedgerInfo> getSalesListByType(String type, ReQueryByDateParam param, String contractType) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ReLedgerInfo::getHouseType, type);
        queryWrapper.eq(ReLedgerInfo::getIsHistory, false);
        queryWrapper.eq(ReLedgerInfo::getProjectId, param.getProjectId());

        if (contractType != null) {
            queryWrapper.eq(ReLedgerInfo::getContractType, contractType);
        }

        // 添加日期条件
        addDateConditions(queryWrapper, "SUBSCRIBE_TIME", param);

        return reLedgerInfoService.list(queryWrapper);
    }

    QueryWrapper buinessAndStorageQueryWrapper2(String type, ReQueryByDateParam reQueryByDateParam) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ReLedgerInfo::getHouseType, type);
        queryWrapper.select("re_ledger_info.id");
        queryWrapper.eq(ReLedgerInfo::getProjectId, reQueryByDateParam.getProjectId());
        queryWrapper.eq(ReLedgerInfo::getIsHistory, false); // 添加历史标记过滤
        return queryWrapper;
    }

    /**
     * 获取汇款金额查询条件
     */
    QueryWrapper getReduceWrapper2(String type, ReQueryByDateParam reQueryByDateParam, String contractType) {
        // 查询符合条件的账本信息
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ReLedgerInfo::getHouseType, type);
        if (StrUtil.isNotEmpty(contractType)) {
            queryWrapper.eq(ReLedgerInfo::getContractType, contractType);
        }
        queryWrapper.eq(ReLedgerInfo::getIsHistory, false);
        queryWrapper.eq(ReLedgerInfo::getProjectId, reQueryByDateParam.getProjectId());

        List<ReLedgerInfo> list = reLedgerInfoService.list(queryWrapper);

        if (CollectionUtil.isEmpty(list)) {
            return null;
        }

        // 构建支付信息查询
        QueryWrapper wrapper = QueryWrapper.create();
        wrapper.in(RePaymentInfo::getLedgerId, list.stream()
                .map(ReLedgerInfo::getId)
                .collect(Collectors.toList()));

        // 添加日期条件
        addDateConditions(wrapper, "PAYMENT_TIME", reQueryByDateParam);

        return wrapper;
    }
//    @Override
//    public ReHouseSalesStatisticsDataInfo businessAndStorageStatistics(String type, ReQueryByDateParam reQueryByDateParam) {
//        // 通过类型查询对应的数据
//        QueryWrapper queryWrapper = QueryWrapper.create();
//        queryWrapper.eq(ReLedgerInfo::getHouseType, type);
//        queryWrapper.eq(ReLedgerInfo::getIsHistory, false);
//        queryWrapper.eq(ReLedgerInfo::getProjectId, reQueryByDateParam.getProjectId());
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateDay())) {
//            queryWrapper.and("SUBSCRIBE_TIME >= ?", reQueryByDateParam.getStartDateDay());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateDay())) {
//            queryWrapper.and("SUBSCRIBE_TIME <= ?", reQueryByDateParam.getEndDateDay());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateTimeMinute())) {
//            queryWrapper.and("SUBSCRIBE_TIME >= ?", reQueryByDateParam.getStartDateTimeMinute());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTimeMinute())) {
//            queryWrapper.and("SUBSCRIBE_TIME <= ?", reQueryByDateParam.getEndDateTimeMinute());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateTime())) {
//            queryWrapper.and("date_format(SUBSCRIBE_TIME,'%Y-%m-%d %H:%i:%s') >= ?", reQueryByDateParam.getStartDateTime());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTime())) {
//            queryWrapper.and("date_format(SUBSCRIBE_TIME,'%Y-%m-%d %H:%i:%s') <= ?", reQueryByDateParam.getEndDateTime());
//        }
//        List<ReLedgerInfo> list1 = reLedgerInfoService.list(queryWrapper);
//        ReHouseSalesStatisticsDataInfo reHouseSalesStatisticsDataInfo = new ReHouseSalesStatisticsDataInfo();
//        //改为查询所有的List
//        List<ReLedgerInfo> list2 = getlist2withoutHistory(type,reQueryByDateParam);
//        List<ReLedgerInfo> collect = list1.stream().filter(s -> null != s.getContractType() && !s.getStatus().equals("1")).collect(Collectors.toList());
//        List<ReLedgerInfo> collect3 = list2.stream().filter(s -> null != s.getContractType()&&s.getContractType().equals("2") && !s.getStatus().equals("1")).collect(Collectors.toList());
//        //销售套数
//        reHouseSalesStatisticsDataInfo.setSoldNumber(collect.size());
//        //销售面积
//        reHouseSalesStatisticsDataInfo.setSoldArea(collect.stream().map(s -> s.getArea()).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP));
//        List<ReLedgerInfo> collect1 = list1.stream().filter(s -> null != s.getContractType() && s.getContractType().equals("2")).collect(Collectors.toList());
//        //租赁套数
//        reHouseSalesStatisticsDataInfo.setRentedNumber(collect3.size());
//        //租赁面积
//        reHouseSalesStatisticsDataInfo.setRentedArea(collect1.stream().map(s -> s.getArea()).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP));
//        // 签约套数
//        QueryWrapper queryWrapper2 = buinessAndStorageQueryWrapper(type, reQueryByDateParam);
//        queryWrapper2.eq(ReLedgerInfo::getContractType, "1");
//        queryWrapper2.eq(ReLedgerInfo::getIsHistory, false);
//        queryWrapper2.leftJoin(RE_CONTRACT_INFO).on(RE_CONTRACT_INFO.LEDGER_ID.eq(RE_LEDGER_INFO.ID));
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateDay())) {
//            queryWrapper2.and("RE_CONTRACT_INFO.CONTRACT_RECORD_TIME >= ?", reQueryByDateParam.getStartDateDay());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateDay())) {
//            queryWrapper2.and("RE_CONTRACT_INFO.CONTRACT_RECORD_TIME <= ?", reQueryByDateParam.getEndDateDay());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateTimeMinute())) {
//            queryWrapper2.and("RE_CONTRACT_INFO.CONTRACT_RECORD_TIME >= ?", reQueryByDateParam.getStartDateTimeMinute());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTimeMinute())) {
//            queryWrapper2.and("RE_CONTRACT_INFO.CONTRACT_RECORD_TIME <= ?", reQueryByDateParam.getEndDateTimeMinute());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTimeMinute())) {
//            queryWrapper2.and("date_format(RE_CONTRACT_INFO.CONTRACT_RECORD_TIME,'%Y-%m-%d %H:%i:%s') >= ?", reQueryByDateParam.getStartDateTime());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTime())) {
//            queryWrapper2.and("date_format(RE_CONTRACT_INFO.CONTRACT_RECORD_TIME,'%Y-%m-%d %H:%i:%s') <= ?", reQueryByDateParam.getEndDateTime());
//        }
//        List<ReLedgerInfo> list3 = reLedgerInfoService.list(queryWrapper2);
//        reHouseSalesStatisticsDataInfo.setSignNumber((long) list3.size());
//        //租赁签约套数
//        QueryWrapper queryWrapper3 = buinessAndStorageQueryWrapper(type, reQueryByDateParam);
//        queryWrapper3.eq(ReLedgerInfo::getContractType, "2");
//        queryWrapper3.leftJoin(RE_LEASE_CONTRACT).on(RE_LEASE_CONTRACT.LEDGER_ID.eq(RE_LEDGER_INFO.ID));
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateDay())) {
//            queryWrapper3.and("RE_LEASE_CONTRACT.LEASE_START_TIME >= ?", reQueryByDateParam.getStartDateDay());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateDay())) {
//            queryWrapper3.and("RE_LEASE_CONTRACT.LEASE_START_TIME <= ?", reQueryByDateParam.getEndDateDay());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateTimeMinute())) {
//            queryWrapper3.and("RE_LEASE_CONTRACT.LEASE_START_TIME >= ?", reQueryByDateParam.getStartDateTimeMinute());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTimeMinute())) {
//            queryWrapper3.and("RE_LEASE_CONTRACT.LEASE_START_TIME <= ?", reQueryByDateParam.getEndDateTimeMinute());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTimeMinute())) {
//            queryWrapper3.and("date_format(RE_LEASE_CONTRACT.LEASE_START_TIME,'%Y-%m-%d %H:%i:%s') >= ?", reQueryByDateParam.getStartDateTime());
//        }
//        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTime())) {
//            queryWrapper3.and("date_format(RE_LEASE_CONTRACT.LEASE_START_TIME,'%Y-%m-%d %H:%i:%s') <= ?", reQueryByDateParam.getEndDateTime());
//        }
//        List<ReLedgerInfo> list4 = reLedgerInfoService.list(queryWrapper3);
//        reHouseSalesStatisticsDataInfo.setRentalSignNumber((long) list4.size());
//        //汇款金额
//        QueryWrapper reduceWrapper = getReduceWrapper(type, reQueryByDateParam,"1");
//        if (null != reduceWrapper) {
//            List<RePaymentInfo> paymentInfos = rePaymentInfoService.list(reduceWrapper);
//            BigDecimal reduce = paymentInfos.stream().filter(s -> s.getPaymentAmount() != null).map(s -> s.getPaymentAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
//            reHouseSalesStatisticsDataInfo.setPaymentAmount(reduce);
//        }else {
//            reHouseSalesStatisticsDataInfo.setPaymentAmount(BigDecimal.ZERO);
//        }
//        //租赁汇款金额
//        reduceWrapper = getReduceWrapper(type, reQueryByDateParam,"2");
//        if (null != reduceWrapper) {
//            List<RePaymentInfo> paymentInfos = rePaymentInfoService.list(reduceWrapper);
//            BigDecimal reduce = paymentInfos.stream().filter(s -> s.getPaymentAmount() != null).map(s -> s.getPaymentAmount()).reduce(BigDecimal.ZERO, BigDecimal::add);
//            reHouseSalesStatisticsDataInfo.setRentalPaymentAmount(reduce);
//        }else {
//            reHouseSalesStatisticsDataInfo.setRentalPaymentAmount(BigDecimal.ZERO);
//        }
//        return reHouseSalesStatisticsDataInfo;
//    }

    QueryWrapper buinessAndStorageQueryWrapper(String type, ReQueryByDateParam reQueryByDateParam) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ReLedgerInfo::getHouseType, type);
        queryWrapper.select("re_ledger_info.id");
        queryWrapper.eq(ReLedgerInfo::getProjectId, reQueryByDateParam.getProjectId());
        return queryWrapper;
    }

    /*
    *  获取汇款金额
    * */
    QueryWrapper getReduceWrapper(String type, ReQueryByDateParam reQueryByDateParam,String contractType) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ReLedgerInfo::getHouseType, type);
        if (StrUtil.isNotEmpty(contractType)) {
            queryWrapper.eq(ReLedgerInfo::getContractType, contractType);
        }
        queryWrapper.eq(ReLedgerInfo::getIsHistory, false);
        queryWrapper.eq(ReLedgerInfo::getProjectId, reQueryByDateParam.getProjectId());
        List<ReLedgerInfo> list = reLedgerInfoService.list(queryWrapper);
        QueryWrapper wrapper = QueryWrapper.create();
        if (CollectionUtil.isEmpty(list)) {
            return null;
        }
        wrapper.in(RePaymentInfo::getLedgerId, list.stream().map(s -> s.getId()).collect(Collectors.toList()));
        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateDay())) {
            wrapper.and("PAYMENT_TIME >= ?", reQueryByDateParam.getStartDateDay());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateDay())) {
            wrapper.and("PAYMENT_TIME <= ?", reQueryByDateParam.getEndDateDay());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateTimeMinute())) {
            wrapper.and("PAYMENT_TIME >= ?", reQueryByDateParam.getStartDateTimeMinute());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTimeMinute())) {
            wrapper.and("PAYMENT_TIME <= ?", reQueryByDateParam.getEndDateTimeMinute());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateTime())) {
            wrapper.and("date_format(PAYMENT_TIME,'%Y-%m-%d %H:%i:%s') >= ?", reQueryByDateParam.getStartDateTime());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTime())) {
            wrapper.and("date_format(PAYMENT_TIME,'%Y-%m-%d %H:%i:%s') <= ?", reQueryByDateParam.getEndDateTime());
        }
        return  wrapper;
    }


    private List<ReLedgerInfo> getlist2withoutHistory(String type, ReQueryByDateParam reQueryByDateParam) {
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ReLedgerInfo::getHouseType, type);
        queryWrapper.eq(ReLedgerInfo::getIsHistory, false);
        queryWrapper.eq(ReLedgerInfo::getProjectId, reQueryByDateParam.getProjectId());
        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateDay())) {
            queryWrapper.and("create_time >= ?", reQueryByDateParam.getStartDateDay());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateDay())) {
            queryWrapper.and("create_time <= ?", reQueryByDateParam.getEndDateDay());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateTimeMinute())) {
            queryWrapper.and("create_time >= ?", reQueryByDateParam.getStartDateTimeMinute());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTimeMinute())) {
            queryWrapper.and("create_time <= ?", reQueryByDateParam.getEndDateTimeMinute());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTimeMinute())) {
            queryWrapper.and("date_format(create_time,'%Y-%m-%d %H:%i:%s') >= ?", reQueryByDateParam.getStartDateTime());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTime())) {
            queryWrapper.and("date_format(create_time,'%Y-%m-%d %H:%i:%s') <= ?", reQueryByDateParam.getEndDateTime());
        }
        return  reLedgerInfoService.list(queryWrapper);
    }

    @Override
    public ReHouseStatisticsDataInfo businessAndStorageStatisticsTotal(String type, String projectId) {
        // 通过类型查询对应的数据
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.eq(ReHouse::getHouseType, type);
        queryWrapper.eq(ReHouse::getProjectId, projectId);
        List<ReHouse> list = reHouseService.list(queryWrapper);
        ReHouseStatisticsDataInfo reHouseStatisticsDataInfo = new ReHouseStatisticsDataInfo();
        reHouseStatisticsDataInfo.setProjectTotalArea(list.stream().filter(s -> null != s.getActualBuildArea()).map(ReHouse::getActualBuildArea).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP));
        reHouseStatisticsDataInfo.setProjectTotalNumber(list.size());
        reHouseStatisticsDataInfo.setProjectUnsoldNumber(list.stream().filter(r -> r.getStatus().equals("1") || r.getStatus().equals("4")).count());
        reHouseStatisticsDataInfo.setProjectUnsoldArea(list.stream().filter(r -> null != r.getActualBuildArea()).filter(r -> r.getStatus().equals("1") || r.getStatus().equals("4")).map(ReHouse::getActualBuildArea).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, BigDecimal.ROUND_HALF_UP));
        // 预计回款
        AtomicReference<BigDecimal> arrearsAmount = new AtomicReference<>(BigDecimal.ZERO);

        list.stream().filter(r -> r.getStatus().equals("1") || r.getStatus().equals("4")).forEach(one -> {
            if (type.equals("3")) {
                BigDecimal totalPayment = one.getTotalPrice();
                arrearsAmount.set(arrearsAmount.get().add(totalPayment));
            }
        });
        reHouseStatisticsDataInfo.setExpectedPaymentAmount(arrearsAmount.get());
        return reHouseStatisticsDataInfo;
    }

    @Inject
    private ReParkLedgerInfoService reParkLedgerInfoService;

    @Inject
    private ReParkService reParkService;

    @Inject
    private ReParkContractService reParkContractService;

    @Override
    public ReHouseSalesStatisticsDataInfo parkingSpaceStatistics(ReQueryByDateParam reQueryByDateParam) {
        // 通过类型查询对应的数据
        QueryWrapper queryWrapper = QueryWrapper.create();
        queryWrapper.select(RE_PARK_LEDGER_INFO.ALL_COLUMNS).rightJoin(RE_PARK).on(RE_PARK_LEDGER_INFO.PARK_ID.eq(RE_PARK.ID));
        queryWrapper.eq(ReParkLedgerInfo::getIsHistory, false);
        queryWrapper.eq(ReParkLedgerInfo::getProjectId, reQueryByDateParam.getProjectId());
        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateDay())) {
            queryWrapper.and("re_park_ledger_info.create_time > ?", reQueryByDateParam.getStartDateDay());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateDay())) {
            queryWrapper.and("re_park_ledger_info.create_time < ?", reQueryByDateParam.getEndDateDay());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getStartDateTimeMinute())) {
            queryWrapper.and("re_park_ledger_info.create_time > ?", reQueryByDateParam.getStartDateTimeMinute());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTimeMinute())) {
            queryWrapper.and("re_park_ledger_info.create_time < ?", reQueryByDateParam.getEndDateTimeMinute());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTimeMinute())) {
            queryWrapper.and("date_format(re_park_ledger_info.create_time,'%Y-%m-%d %H:%i:%s') >= ?", reQueryByDateParam.getStartDateTime());
        }
        if (StrUtil.isNotEmpty(reQueryByDateParam.getEndDateTime())) {
            queryWrapper.and("date_format(re_park_ledger_info.create_time,'%Y-%m-%d %H:%i:%s') <= ?", reQueryByDateParam.getEndDateTime());
        }
        List<ReParkLedgerInfo> list = reParkLedgerInfoService.list(queryWrapper);

        ReHouseSalesStatisticsDataInfo reHouseSalesStatisticsDataInfo = new ReHouseSalesStatisticsDataInfo();
        // 已销售套数
        reHouseSalesStatisticsDataInfo.setSoldNumber(list.size());
        // 总价值
        BigDecimal reduce = list.stream().map(s -> s.getContractPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
        reHouseSalesStatisticsDataInfo.setPaymentAmount(reduce);
        // 查询 签约信息
        List<ReParkContract> list1 = ListUtil.empty();
        if (list.size() > 0) {
            list1 = reParkContractService.list(QueryWrapper.create().in(ReParkContract::getLedgerId, list.stream().map(s -> s.getId()).collect(Collectors.toList())));
        }
        List<ReParkContract> collect = list1.stream().filter(s -> s.getContractPrice().compareTo(BigDecimal.ZERO) == 0).collect(Collectors.toList());
        reHouseSalesStatisticsDataInfo.setGiftNumber(collect.size());
        reHouseSalesStatisticsDataInfo.setSignNumber(list.stream().filter(s -> null != s.getContractTime()).count());
        return reHouseSalesStatisticsDataInfo;
    }


    @Override
    public ReHouseStatisticsDataInfo parkingSpaceStatisticsTotal(String projectId) {
        QueryWrapper eq = QueryWrapper.create().eq(ReParkArea::getProjectId, projectId);
        List<ReParkArea> list2 = reParkAreaService.list(eq);
        QueryWrapper queryWrapper = QueryWrapper.create();
        List<RePark> list = ListUtil.empty();
        if (CollectionUtil.isNotEmpty(list2)) {
            queryWrapper.in(RePark::getAreaId, list2.stream().map(s -> s.getId()).collect(Collectors.toList()));
            list = reParkService.list(queryWrapper);
        }
        ReHouseStatisticsDataInfo reHouseStatisticsDataInfo = new ReHouseStatisticsDataInfo();
        reHouseStatisticsDataInfo.setProjectTotalNumber(list.size());
        reHouseStatisticsDataInfo.setTotalValue(list.stream().filter(s -> s.getTotalPrice() != null).map(s -> s.getTotalPrice()).reduce(BigDecimal.ZERO, BigDecimal::add));
        List<RePark> collect1 = list.stream().filter(s -> s.getStatus().equals("1")).collect(Collectors.toList());
        reHouseStatisticsDataInfo.setProjectUnsoldNumber(Long.valueOf(collect1.size()));
        BigDecimal reduce = collect1.stream().filter(s -> s.getTotalPrice() != null).map(s -> s.getTotalPrice()).reduce(BigDecimal.ZERO, BigDecimal::add);
        reHouseStatisticsDataInfo.setExpectedPaymentAmount(reduce);
        return reHouseStatisticsDataInfo;
    }
}
