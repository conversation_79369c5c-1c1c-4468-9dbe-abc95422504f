/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service;

import vip.xiaonuo.biz.modular.realty.param.ReLeaseRenewalParam;

import java.util.Date;

/**
 * 商业租赁续签服务接口
 * 
 * <AUTHOR> 4.0 sonnet
 * @date 2024/12/19
 */
public interface ReLeaseRenewalService {

    /**
     * 商业租赁续签
     * 
     * @param renewalParam 续签参数
     * @return 新台账ID
     */
    String renewLease(ReLeaseRenewalParam renewalParam);

    /**
     * 检查续签重复性
     * 
     * @param houseId 房源ID
     * @param customerName 客户姓名
     * @param paymentDate 交款日期
     * @param projectId 项目ID
     * @throws vip.xiaonuo.common.exception.CommonException 重复时抛出异常
     */
    void checkRenewalDuplicate(String houseId, String customerName, Date paymentDate, String projectId);

    /**
     * 批量检查续签重复性
     * 
     * @param renewalParams 续签参数列表
     * @return 检查结果，key为参数索引，value为是否重复
     */
    java.util.Map<Integer, Boolean> batchCheckRenewalDuplicate(java.util.List<ReLeaseRenewalParam> renewalParams);
}
