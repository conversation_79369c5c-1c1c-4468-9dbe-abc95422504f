/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 重复性检查结果封装类
 * 
 * <AUTHOR> 4.0 sonnet
 * @date 2024/12/19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DuplicateCheckResult {

    /** 是否重复 */
    private boolean isDuplicate;

    /** 重复的记录信息 */
    private Object duplicateRecord;

    /** 错误信息 */
    private String errorMessage;

    /**
     * 创建无重复结果
     * 
     * @return 无重复的检查结果
     */
    public static DuplicateCheckResult noDuplicate() {
        return new DuplicateCheckResult(false, null, null);
    }

    /**
     * 创建重复结果
     * 
     * @param record 重复的记录
     * @param message 错误信息
     * @return 重复的检查结果
     */
    public static DuplicateCheckResult duplicate(Object record, String message) {
        return new DuplicateCheckResult(true, record, message);
    }

    /**
     * 创建重复结果（仅错误信息）
     * 
     * @param message 错误信息
     * @return 重复的检查结果
     */
    public static DuplicateCheckResult duplicate(String message) {
        return new DuplicateCheckResult(true, null, message);
    }

    /**
     * 是否无重复
     * 
     * @return true-无重复，false-有重复
     */
    public boolean isNoDuplicate() {
        return !isDuplicate;
    }

    /**
     * 获取格式化的错误信息
     * 
     * @return 格式化后的错误信息
     */
    public String getFormattedErrorMessage() {
        if (!isDuplicate) {
            return "无重复记录";
        }
        return errorMessage != null ? errorMessage : "发现重复记录";
    }
}
