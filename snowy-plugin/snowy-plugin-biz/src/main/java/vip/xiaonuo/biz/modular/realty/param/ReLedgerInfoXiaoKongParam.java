/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 房屋台账信息添加参数
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
@Getter
@Setter
@ApiModel("房屋台账信息添加参数")
public class ReLedgerInfoXiaoKongParam {

    /** 房屋id **/
    @ApiModelProperty(value = "房屋id", position = 1)
    private String houseId;

    /** 客户信息 **/
    @ApiModelProperty(value = "客户信息", position = 2,dataType = "ReCustomerAddParam.class")
    private ReCustomerAddParam customerInfo;

    /** 签约类型（1销售2租赁3安置） **/
    @ApiModelProperty(value = "签约类型", position = 3)
    private String signType;

    /** 销售签约 **/
    @ApiModelProperty(value = "销售签约", position = 4,dataType = "ReSalesContractAddParam.class")
    private ReSalesContractAddParam saleSign;

    /** 安置签约 **/
    @ApiModelProperty(value = "安置签约", position = 5,dataType = "RePlacementContractAddParam.class")
    private RePlacementContractAddParam placeSign;

    /** 租赁签约 **/
    @ApiModelProperty(value = "租赁签约", position = 6,dataType = "ReLeaseContractAddParam.class")
    private ReLeaseContractAddParam leaseSign;

    /** 分期付款Ids 新增的交款类型为-1是分期付款 **/
    @ApiModelProperty(value = "分期付款Ids", position = 7)
    private String installmentPaymentIds;

    /** 交款信息 交款信息的交款类型不能为-1 **/
    @ApiModelProperty(value = "交款信息Ids", position = 8)
    private String paymentInfoIds;

    /** 合同信息 **/
    @ApiModelProperty(value = "合同信息", position = 8,dataType = "ReContractInfoAddParam.class")
    private ReContractInfoAddParam contractInfo;

}
