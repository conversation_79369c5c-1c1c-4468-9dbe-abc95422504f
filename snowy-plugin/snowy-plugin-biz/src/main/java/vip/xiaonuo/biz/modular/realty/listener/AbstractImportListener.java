/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.Solon;
import vip.xiaonuo.biz.modular.realty.constants.LedgerConstants;
import vip.xiaonuo.biz.modular.realty.dto.DuplicateCheckResult;
import vip.xiaonuo.biz.modular.realty.entity.ReLedgerInfo;
import vip.xiaonuo.biz.modular.realty.mapper.ReLedgerInfoMapper;
import vip.xiaonuo.biz.modular.realty.service.LedgerDuplicateCheckService;

import java.math.BigDecimal;
import java.util.*;
import java.util.Date;

/**
 * Excel导入监听器抽象基类
 * 提供公共的重复检查、数据转换、资源管理等功能
 * 
 * <AUTHOR> 4.0 sonnet
 * @date 2024/12/19
 */
@Slf4j
public abstract class AbstractImportListener<T> implements ReadListener<T> {

    /** 重复检查服务 */
    protected static final LedgerDuplicateCheckService duplicateCheckService = 
            Solon.context().getBean(LedgerDuplicateCheckService.class);

    /** 台账信息Mapper */
    protected static final ReLedgerInfoMapper reLedgerInfoMapper = 
            Solon.context().getBean(ReLedgerInfoMapper.class);

    /** 错误信息收集列表 */
    protected final List<Map<String, Object>> errorList = new ArrayList<>();

    /** 项目ID */
    protected final String projectId;

    /** 总处理数量 */
    protected int total = 0;

    /**
     * 构造函数
     * 
     * @param projectId 项目ID
     */
    public AbstractImportListener(String projectId) {
        this.projectId = Objects.requireNonNull(projectId, "项目ID不能为空");
    }

    /**
     * 检查房屋台账重复性（住宅、商业销售、储藏间）
     * 
     * @param houseId 房源ID
     * @param customerName 客户姓名
     * @param subscribeTime 认购时间
     * @return true-重复，false-不重复
     */
    protected boolean checkHouseLedgerDuplicate(String houseId, String customerName, Date subscribeTime) {
        if (duplicateCheckService == null) {
            log.warn("重复检查服务未初始化，跳过重复检查");
            return false;
        }

        DuplicateCheckResult result = duplicateCheckService.checkHouseLedgerDuplicate(
                houseId, customerName, subscribeTime, projectId);

        if (result.isDuplicate()) {
            log.info("发现重复台账记录：{}", result.getErrorMessage());
            return true;
        }
        return false;
    }

    /**
     * 检查商业租赁台账重复性
     * 
     * @param houseId 房源ID
     * @param customerName 客户姓名
     * @param paymentDate 交款日期
     * @return true-重复，false-不重复
     */
    protected boolean checkLeaseHouseLedgerDuplicate(String houseId, String customerName, Date paymentDate) {
        if (duplicateCheckService == null) {
            log.warn("重复检查服务未初始化，跳过重复检查");
            return false;
        }

        DuplicateCheckResult result = duplicateCheckService.checkLeaseHouseLedgerDuplicate(
                houseId, customerName, paymentDate, projectId);

        if (result.isDuplicate()) {
            log.info("发现重复租赁台账记录：{}", result.getErrorMessage());
            return true;
        }
        return false;
    }

    /**
     * 检查车位台账重复性
     * 
     * @param parkId 车位ID
     * @param customerName 客户姓名
     * @param subscribeTime 认购时间
     * @return true-重复，false-不重复
     */
    protected boolean checkParkLedgerDuplicate(String parkId, String customerName, Date subscribeTime) {
        if (duplicateCheckService == null) {
            log.warn("重复检查服务未初始化，跳过重复检查");
            return false;
        }

        DuplicateCheckResult result = duplicateCheckService.checkParkLedgerDuplicate(
                parkId, customerName, subscribeTime, projectId);

        if (result.isDuplicate()) {
            log.info("发现重复车位台账记录：{}", result.getErrorMessage());
            return true;
        }
        return false;
    }

    /**
     * 将历史台账标记为历史记录
     * 
     * @param houseId 房源ID
     */
    protected void updateLedgerHistory(String houseId) {
        if (StrUtil.isEmpty(houseId)) {
            return;
        }

        try {
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("HOUSE_ID", houseId);
            queryMap.put("PROJECT_ID", projectId);
            queryMap.put("IS_HISTORY", false);

            List<ReLedgerInfo> reLedgerInfos = reLedgerInfoMapper.selectListByMap(queryMap);
            if (CollectionUtil.isNotEmpty(reLedgerInfos)) {
                reLedgerInfos.forEach(ledgerInfo -> {
                    ledgerInfo.setIsHistory(true);
                    reLedgerInfoMapper.update(ledgerInfo);
                });
                log.debug("已将{}条台账记录标记为历史", reLedgerInfos.size());
            }
        } catch (Exception e) {
            log.error("更新台账历史状态失败：houseId={}", houseId, e);
        }
    }

    /**
     * 安全地将字符串转换为BigDecimal
     * 🔧 修复：统一精度处理，设置2位小数精度
     *
     * @param value 字符串值
     * @return BigDecimal值，转换失败时返回ZERO
     */
    protected BigDecimal parseBigDecimal(String value) {
        if (StrUtil.isEmpty(value)) {
            return BigDecimal.ZERO;
        }
        try {
            BigDecimal result = new BigDecimal(value.trim());
            // 统一设置2位小数精度，使用四舍五入
            return result.setScale(2, BigDecimal.ROUND_HALF_UP);
        } catch (NumberFormatException e) {
            log.warn("数字格式转换失败：{}", value);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 安全地将字符串转换为BigDecimal（保持原始精度）
     * 用于不需要统一精度的场景
     *
     * @param value 字符串值
     * @return BigDecimal值，转换失败时返回BigDecimal.ZERO
     */
    protected BigDecimal parseBigDecimalRaw(String value) {
        if (StrUtil.isEmpty(value)) {
            return BigDecimal.ZERO;
        }
        try {
            return new BigDecimal(value.trim());
        } catch (NumberFormatException e) {
            log.warn("数字格式转换失败：{}", value);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 安全地将字符串转换为Double
     * 
     * @param value 字符串值
     * @return Double值，转换失败时返回0.0
     */
    protected Double parseDouble(String value) {
        if (StrUtil.isEmpty(value)) {
            return 0.0;
        }
        try {
            return Double.parseDouble(value.trim());
        } catch (NumberFormatException e) {
            log.warn("数字格式转换失败：{}", value);
            return 0.0;
        }
    }

    /**
     * 创建错误信息Map
     * 
     * @param errorMessage 错误信息
     * @return 错误信息Map
     */
    protected Map<String, Object> createErrorMap(String errorMessage) {
        Map<String, Object> errorMap = new HashMap<>();
        errorMap.put("项目ID", projectId);
        errorMap.put("错误信息", errorMessage);
        errorMap.put("行号", total);
        return errorMap;
    }

    /**
     * 验证必填字段
     * 
     * @param fieldName 字段名称
     * @param fieldValue 字段值
     * @throws IllegalArgumentException 字段为空时抛出异常
     */
    protected void validateRequiredField(String fieldName, String fieldValue) {
        if (StrUtil.isEmpty(fieldValue)) {
            throw new IllegalArgumentException(fieldName + "不能为空");
        }
    }

    /**
     * 验证必填字段
     * 
     * @param fieldName 字段名称
     * @param fieldValue 字段值
     * @throws IllegalArgumentException 字段为空时抛出异常
     */
    protected void validateRequiredField(String fieldName, Object fieldValue) {
        if (fieldValue == null) {
            throw new IllegalArgumentException(fieldName + "不能为空");
        }
    }

    /**
     * 获取错误列表
     * 
     * @return 错误列表
     */
    public List<Map<String, Object>> getErrorList() {
        return new ArrayList<>(errorList);
    }

    /**
     * 获取总处理数量
     * 
     * @return 总处理数量
     */
    public int getTotal() {
        return total;
    }

    /**
     * 清理资源（子类可重写以清理特定资源）
     */
    protected void cleanupResources() {
        // 默认实现为空，子类可重写
    }

    /**
     * 抽象方法：处理单条数据
     * 
     * @param data 数据对象
     */
    protected abstract void processData(T data);
}
