package vip.xiaonuo.biz.modular.realty.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.Solon;
import vip.xiaonuo.biz.modular.realty.async.ReProjectDetailServiceAsync;
import vip.xiaonuo.biz.modular.realty.entity.*;
import vip.xiaonuo.biz.modular.realty.mapper.*;
import vip.xiaonuo.biz.modular.realty.param.ReStoreRoomLedgerInfoImportParam;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel 导入监听器，用于处理储藏室台账数据导入
 * 已集成重复性检查功能
 *
 * <AUTHOR>
 * @date 2024/8/22 10:35
 * @updated Claude 4.0 sonnet 2024/12/19
 */
@Slf4j
public class ImportStoreRoomLedgerInfoListener extends AbstractImportListener<ReStoreRoomLedgerInfoImportParam> {

    private static final String HOUSE_TYPE = "3"; // 房屋类型：储藏室
    private static final String STATUS_PAID = "3"; // 状态：已结清
    private static final String STATUS_UNPAID = "2"; // 状态：未结清

    private final JSONObject res;

    private final ReHouseMapper reHouseMapper = Solon.context().getBean(ReHouseMapper.class);
    private final ReLedgerInfoMapper reLedgerInfoMapper = Solon.context().getBean(ReLedgerInfoMapper.class);
    private final ReSalesContractMapper reSalesContractMapper = Solon.context().getBean(ReSalesContractMapper.class);
    private final RePaymentInfoMapper rePaymentInfoMapper = Solon.context().getBean(RePaymentInfoMapper.class);
    private final ReCustomerMapper reCustomerMapper = Solon.context().getBean(ReCustomerMapper.class);
    private final ReCustomerInfoMapper reCustomerInfoMapper = Solon.context().getBean(ReCustomerInfoMapper.class);
    private static ReProjectDetailServiceAsync reProjectDetailServiceAsync = Solon.context().getBean(ReProjectDetailServiceAsync.class);


    public ImportStoreRoomLedgerInfoListener(String projectId, JSONObject res) {
        super(projectId);
        this.res = res;
    }

    @Override
    public void invoke(ReStoreRoomLedgerInfoImportParam param, AnalysisContext context) {
        total++;
        try {
            if (StrUtil.isEmpty(param.getCustomerName())) {
                return; // 客户名为空，跳过处理
            }

            // 根据房屋信息匹配房屋
            ReHouse reHouse = findHouse(param);
            if (reHouse == null) {
                errorList.add(createQueryMap(param, "未找到房屋"));
                return;
            }

            // 🔥 新增：储藏室重复性检查
            if (checkHouseLedgerDuplicate(reHouse.getId(), param.getCustomerName(), param.getSubscriptionDate())) {
                errorList.add(createQueryMap(param, "台账记录重复，跳过导入"));
                log.info("跳过重复台账记录：房源ID={}, 客户={}, 认购时间={}",
                        reHouse.getId(), param.getCustomerName(), param.getSubscriptionDate());
                return;
            }

            // 将所有台账置为历史
            updateLedgerHistory(reHouse.getId());

            // 插入台账信息
            ReLedgerInfo reLedgerInfo = insertLedgerInfo(param, reHouse);

            // 插入客户信息
            ReCustomerInfo reCustomerInfo = insertCustomerInfo(param);

            //插入客户关系
            insertCustomerRelation(param, reHouse, reLedgerInfo, reCustomerInfo, null);

            // 插入签约信息
            ReSalesContract reSalesContract = insertSalesContract(param, reLedgerInfo);

            // 插入付款信息
            insertPaymentInfo(param, reLedgerInfo, reSalesContract);

            // 更新房屋状态
            updateHouseStatus(reHouse, reLedgerInfo, reSalesContract);

            reProjectDetailServiceAsync.updateOrInsertDetail(reHouse, projectId);
        } catch (Exception e) {
            log.error("处理数据失败: {}", e.getMessage(), e);
            errorList.add(createQueryMap(param, "系统异常:" + e.getMessage()));
        }
    }

    @Override
    protected void processData(ReStoreRoomLedgerInfoImportParam data) {
        // 此方法由invoke方法实现，这里留空
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (errorList.isEmpty()) {
            res.set("code", 200);
            res.set("msg", "所有导入成功");
            res.set("total",total);
        } else {
            res.set("code", 500);
            res.set("msg", "有部分数据导入失败");
            res.set("errorList", errorList);
            res.set("total",total);
        }

    }

    private ReHouse findHouse(ReStoreRoomLedgerInfoImportParam param) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("FLOOR", param.getFloor());
        queryMap.put("BUILD_CODE", param.getBuildingNumber());
        queryMap.put("HOUSE_NUMBER", param.getRoom());
        queryMap.put("PROJECT_ID", projectId);
        queryMap.put("HOUSE_TYPE", HOUSE_TYPE);
        return reHouseMapper.selectOneByMap(queryMap);
    }

    // 移除此方法，使用父类的updateLedgerHistory方法

    private ReLedgerInfo insertLedgerInfo(ReStoreRoomLedgerInfoImportParam param, ReHouse reHouse) {
        ReLedgerInfo reLedgerInfo = new ReLedgerInfo();
        reLedgerInfo.setHouseId(reHouse.getId());
        reLedgerInfo.setProjectId(projectId);
        reLedgerInfo.setIsHistory(false);
        reLedgerInfo.setHouseType(HOUSE_TYPE);
        reLedgerInfo.setArea(parseBigDecimal(param.getArea()));
        reLedgerInfo.setName(param.getCustomerName());
        reLedgerInfo.setIdCard(param.getIdCardNumber());
        reLedgerInfo.setPhone(param.getPhone());
        reLedgerInfo.setFloor(param.getFloor());
        reLedgerInfo.setHouseNumber(param.getRoom());
        reLedgerInfo.setBuildCode(param.getBuildingNumber());
        reLedgerInfo.setContractType("1");
        reLedgerInfo.setDealUnitPrice(parseBigDecimal(param.getSalesUnitPrice()));
        reLedgerInfo.setContractTime(param.getContractDate());
        reLedgerInfo.setContractPrice(parseBigDecimal(param.getContractPrice()));
        reLedgerInfo.setSubscribeTime(param.getSubscriptionDate());
        reLedgerInfo.setTotalPayment(parseBigDecimal(param.getTotalPayment()));

        // 🔧 修复状态计算：根据实际欠款计算状态，而不是仅依赖备注
        BigDecimal contractPrice = parseBigDecimal(param.getContractPrice());
        BigDecimal totalPayment = parseBigDecimal(param.getTotalPayment());
        BigDecimal arrears = contractPrice.subtract(totalPayment);

        // 正确的状态判断逻辑
        if (arrears.compareTo(BigDecimal.ZERO) <= 0) {
            reLedgerInfo.setStatus(STATUS_PAID);   // 已结清
            log.debug("储藏间设置为已结清：合同价={}, 已交款={}, 欠款={} (客户：{})",
                    contractPrice, totalPayment, arrears, param.getCustomerName());
        } else {
            reLedgerInfo.setStatus(STATUS_UNPAID); // 未结清
            log.debug("储藏间设置为未结清：合同价={}, 已交款={}, 欠款={} (客户：{})",
                    contractPrice, totalPayment, arrears, param.getCustomerName());
        }

        // 如果备注明确说明已结清，以备注为准（向后兼容）
        if ("已结清".equals(param.getRemarks())) {
            reLedgerInfo.setStatus(STATUS_PAID);
            log.debug("根据备注强制设置为已结清 (客户：{})", param.getCustomerName());
        }
        reLedgerInfoMapper.insert(reLedgerInfo);
        return reLedgerInfo;
    }

    private ReSalesContract insertSalesContract(ReStoreRoomLedgerInfoImportParam param, ReLedgerInfo reLedgerInfo) {
        ReSalesContract reSalesContract = new ReSalesContract();
        reSalesContract.setLedgerId(reLedgerInfo.getId());
//        reSalesContract.setDiscount(parseBigDecimal(param.getDiscount()));
        reSalesContract.setUnitPrice(parseBigDecimal(param.getSalesUnitPrice()));
        reSalesContract.setContractPrice(parseBigDecimal(param.getContractPrice()));
        reSalesContract.setEarnestMoney(parseBigDecimal(param.getDeposit()));
        reSalesContract.setDiscountRemark(param.getDiscount());
        reSalesContractMapper.insert(reSalesContract);
        return reSalesContract;
    }

    private ReCustomerInfo insertCustomerInfo(ReStoreRoomLedgerInfoImportParam param) {
        ReCustomerInfo reCustomerInfo = new ReCustomerInfo();
        reCustomerInfo.setPhone(param.getPhone());
        reCustomerInfo.setName(param.getCustomerName());
        reCustomerInfo.setIdCard(param.getIdCardNumber());
        reCustomerInfoMapper.insert(reCustomerInfo);
        return reCustomerInfo;
    }

    private void insertPaymentInfo(ReStoreRoomLedgerInfoImportParam param, ReLedgerInfo reLedgerInfo, ReSalesContract reSalesContract) {
        RePaymentInfo rePaymentInfo = new RePaymentInfo();
        rePaymentInfo.setPaymentType("2");
        rePaymentInfo.setLedgerId(reLedgerInfo.getId());
        rePaymentInfo.setContractId(reSalesContract.getId());
        rePaymentInfo.setPaymentAmount(parseBigDecimal(param.getTotalPayment()));
        rePaymentInfo.setExtJson(param.getRemarks());
        rePaymentInfo.setPaymentTime(param.getContractDate());
        rePaymentInfoMapper.insert(rePaymentInfo);
    }

    private void updateHouseStatus(ReHouse reHouse, ReLedgerInfo reLedgerInfo, ReSalesContract reSalesContract) {
        reHouse.setStatus(reLedgerInfo.getStatus());
        reHouse.setCustomerPhone(reLedgerInfo.getPhone());
        reHouse.setCustomerName(reLedgerInfo.getName());
        reHouse.setSalesUnitPrice(reSalesContract.getUnitPrice());
        reHouse.setSalesTotalPrice(reSalesContract.getTotalPrice());
        reHouseMapper.update(reHouse);
    }

    // 移除此方法，使用父类的parseBigDecimal方法

    private void insertCustomerRelation(ReStoreRoomLedgerInfoImportParam param, ReHouse reHouse, ReLedgerInfo reLedgerInfo, ReCustomerInfo reCustomerInfo, ReCustomerInfo reCustomerInfo2) {
        ReCustomer reCustomer = new ReCustomer();
        reCustomer.setCustomerId(reCustomerInfo.getId());
        reCustomer.setShareholderIds(reCustomerInfo2 == null ? "" : reCustomerInfo2.getId());
        reCustomer.setEnableArea(parseBigDecimal(param.getArea()));
        reCustomer.setHouseId(reHouse.getId());
        reCustomer.setProjectId(projectId);
        reCustomer.setSubscribeTime(param.getSubscriptionDate());
        reCustomer.setContractTime(param.getContractDate());
        reCustomer.setLedgerId(reLedgerInfo.getId());
        reCustomerMapper.insert(reCustomer);
    }

    private Map<String, Object> createQueryMap(ReStoreRoomLedgerInfoImportParam param,String msg) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("楼层", param.getFloor());
        queryMap.put("楼号", param.getBuildingNumber());
        queryMap.put("房号", param.getRoom());
        queryMap.put("项目ID", projectId);
        queryMap.put("房屋类型", HOUSE_TYPE);
        queryMap.put("错误信息", msg);
        return queryMap;
    }
}
