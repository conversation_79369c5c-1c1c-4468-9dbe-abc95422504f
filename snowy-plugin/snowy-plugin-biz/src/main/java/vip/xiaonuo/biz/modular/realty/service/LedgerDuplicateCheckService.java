/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service;

import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import vip.xiaonuo.biz.modular.realty.dto.DuplicateCheckResult;
import vip.xiaonuo.biz.modular.realty.entity.ReLedgerInfo;
import vip.xiaonuo.biz.modular.realty.entity.ReParkLedgerInfo;
import vip.xiaonuo.biz.modular.realty.mapper.ReLedgerInfoMapper;
import vip.xiaonuo.biz.modular.realty.mapper.ReParkLedgerInfoMapper;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 台账重复性检查服务
 * 
 * <AUTHOR> 4.0 sonnet
 * @date 2024/12/19
 */
@Slf4j
@Component
public class LedgerDuplicateCheckService {

    @Inject
    private ReLedgerInfoMapper reLedgerInfoMapper;

    @Inject
    private ReParkLedgerInfoMapper reParkLedgerInfoMapper;

    /**
     * 检查房屋台账重复性（住宅、商业销售、储藏间）
     * 检查规则：房源ID + 客户姓名 + 认购时间
     * 
     * @param houseId 房源ID
     * @param customerName 客户姓名
     * @param subscribeTime 认购时间
     * @param projectId 项目ID
     * @return 检查结果
     */
    public DuplicateCheckResult checkHouseLedgerDuplicate(String houseId, String customerName, Date subscribeTime, String projectId) {
        if (StrUtil.hasEmpty(houseId, customerName, projectId) || subscribeTime == null) {
            log.warn("重复检查参数不完整：houseId={}, customerName={}, subscribeTime={}, projectId={}", 
                    houseId, customerName, subscribeTime, projectId);
            return DuplicateCheckResult.noDuplicate();
        }

        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq(ReLedgerInfo::getHouseId, houseId)
                    .eq(ReLedgerInfo::getName, customerName)
                    .eq(ReLedgerInfo::getSubscribeTime, subscribeTime)
                    .eq(ReLedgerInfo::getProjectId, projectId)
                    .eq(ReLedgerInfo::getIsHistory, false);

            List<ReLedgerInfo> existingRecords = reLedgerInfoMapper.selectListByQuery(queryWrapper);
            
            if (!existingRecords.isEmpty()) {
                ReLedgerInfo duplicateRecord = existingRecords.get(0);
                String errorMessage = String.format("发现重复台账：房源ID=%s, 客户=%s, 认购时间=%s", 
                        houseId, customerName, subscribeTime);
                log.info("台账重复检查：{}", errorMessage);
                return DuplicateCheckResult.duplicate(duplicateRecord, errorMessage);
            }

            return DuplicateCheckResult.noDuplicate();
            
        } catch (Exception e) {
            log.error("台账重复检查异常：houseId={}, customerName={}, subscribeTime={}", 
                    houseId, customerName, subscribeTime, e);
            return DuplicateCheckResult.noDuplicate(); // 异常时不阻止导入
        }
    }

    /**
     * 检查商业租赁台账重复性
     * 检查规则：房源ID + 客户姓名 + 交款日期（存储在subscribeTime字段中）
     * 
     * @param houseId 房源ID
     * @param customerName 客户姓名
     * @param paymentDate 交款日期
     * @param projectId 项目ID
     * @return 检查结果
     */
    public DuplicateCheckResult checkLeaseHouseLedgerDuplicate(String houseId, String customerName, Date paymentDate, String projectId) {
        if (StrUtil.hasEmpty(houseId, customerName, projectId) || paymentDate == null) {
            log.warn("租赁台账重复检查参数不完整：houseId={}, customerName={}, paymentDate={}, projectId={}", 
                    houseId, customerName, paymentDate, projectId);
            return DuplicateCheckResult.noDuplicate();
        }

        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq(ReLedgerInfo::getHouseId, houseId)
                    .eq(ReLedgerInfo::getName, customerName)
                    .eq(ReLedgerInfo::getSubscribeTime, paymentDate) // 租赁台账中subscribeTime存储交款日期
                    .eq(ReLedgerInfo::getProjectId, projectId)
                    .eq(ReLedgerInfo::getContractType, "2") // 租赁类型
                    .eq(ReLedgerInfo::getIsHistory, false);

            List<ReLedgerInfo> existingRecords = reLedgerInfoMapper.selectListByQuery(queryWrapper);
            
            if (!existingRecords.isEmpty()) {
                ReLedgerInfo duplicateRecord = existingRecords.get(0);
                String errorMessage = String.format("发现重复租赁台账：房源ID=%s, 客户=%s, 交款日期=%s", 
                        houseId, customerName, paymentDate);
                log.info("租赁台账重复检查：{}", errorMessage);
                return DuplicateCheckResult.duplicate(duplicateRecord, errorMessage);
            }

            return DuplicateCheckResult.noDuplicate();
            
        } catch (Exception e) {
            log.error("租赁台账重复检查异常：houseId={}, customerName={}, paymentDate={}", 
                    houseId, customerName, paymentDate, e);
            return DuplicateCheckResult.noDuplicate(); // 异常时不阻止导入
        }
    }

    /**
     * 检查车位台账重复性
     * 检查规则：车位ID + 客户姓名 + 认购时间
     * 
     * @param parkId 车位ID
     * @param customerName 客户姓名
     * @param subscribeTime 认购时间
     * @param projectId 项目ID
     * @return 检查结果
     */
    public DuplicateCheckResult checkParkLedgerDuplicate(String parkId, String customerName, Date subscribeTime, String projectId) {
        if (StrUtil.hasEmpty(parkId, customerName, projectId) || subscribeTime == null) {
            log.warn("车位台账重复检查参数不完整：parkId={}, customerName={}, subscribeTime={}, projectId={}", 
                    parkId, customerName, subscribeTime, projectId);
            return DuplicateCheckResult.noDuplicate();
        }

        try {
            QueryWrapper queryWrapper = QueryWrapper.create()
                    .eq(ReParkLedgerInfo::getParkId, parkId)
                    .eq(ReParkLedgerInfo::getName, customerName)
                    .eq(ReParkLedgerInfo::getSubscribeTime, subscribeTime)
                    .eq(ReParkLedgerInfo::getProjectId, projectId)
                    .eq(ReParkLedgerInfo::getIsHistory, false);

            List<ReParkLedgerInfo> existingRecords = reParkLedgerInfoMapper.selectListByQuery(queryWrapper);
            
            if (!existingRecords.isEmpty()) {
                ReParkLedgerInfo duplicateRecord = existingRecords.get(0);
                String errorMessage = String.format("发现重复车位台账：车位ID=%s, 客户=%s, 认购时间=%s", 
                        parkId, customerName, subscribeTime);
                log.info("车位台账重复检查：{}", errorMessage);
                return DuplicateCheckResult.duplicate(duplicateRecord, errorMessage);
            }

            return DuplicateCheckResult.noDuplicate();
            
        } catch (Exception e) {
            log.error("车位台账重复检查异常：parkId={}, customerName={}, subscribeTime={}", 
                    parkId, customerName, subscribeTime, e);
            return DuplicateCheckResult.noDuplicate(); // 异常时不阻止导入
        }
    }

    /**
     * 批量检查房屋台账重复性
     * 
     * @param checkParams 检查参数列表
     * @return 检查结果列表
     */
    public List<DuplicateCheckResult> batchCheckHouseLedgerDuplicate(List<HouseLedgerCheckParam> checkParams) {
        return checkParams.stream()
                .map(param -> checkHouseLedgerDuplicate(
                        param.getHouseId(), 
                        param.getCustomerName(), 
                        param.getSubscribeTime(), 
                        param.getProjectId()))
                .collect(Collectors.toList());
    }

    /**
     * 房屋台账检查参数
     */
    public static class HouseLedgerCheckParam {
        private String houseId;
        private String customerName;
        private Date subscribeTime;
        private String projectId;

        // 构造函数和getter/setter方法
        public HouseLedgerCheckParam(String houseId, String customerName, Date subscribeTime, String projectId) {
            this.houseId = houseId;
            this.customerName = customerName;
            this.subscribeTime = subscribeTime;
            this.projectId = projectId;
        }

        public String getHouseId() { return houseId; }
        public String getCustomerName() { return customerName; }
        public Date getSubscribeTime() { return subscribeTime; }
        public String getProjectId() { return projectId; }
    }
}
