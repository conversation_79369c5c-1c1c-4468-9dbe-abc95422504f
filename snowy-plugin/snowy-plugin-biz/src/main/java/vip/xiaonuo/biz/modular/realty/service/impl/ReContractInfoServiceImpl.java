/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.ReContractInfo;
import vip.xiaonuo.biz.modular.realty.mapper.ReContractInfoMapper;
import vip.xiaonuo.biz.modular.realty.param.ReContractInfoAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReContractInfoEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReContractInfoIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReContractInfoPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReContractInfoService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;

import java.util.List;

/**
 * 合同信息Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
@Component
public class ReContractInfoServiceImpl extends ServiceImpl<ReContractInfoMapper, ReContractInfo> implements ReContractInfoService {

    @Override
    public Page<ReContractInfo> page(ReContractInfoPageParam reContractInfoPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if(ObjectUtil.isAllNotEmpty(reContractInfoPageParam.getSortField(), reContractInfoPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reContractInfoPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reContractInfoPageParam.getSortField()),reContractInfoPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReContractInfo::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public void add(ReContractInfoAddParam reContractInfoAddParam) {
        ReContractInfo reContractInfo = BeanUtil.toBean(reContractInfoAddParam, ReContractInfo.class);
        this.save(reContractInfo);
    }

    @Tran
    @Override
    public void edit(ReContractInfoEditParam reContractInfoEditParam) {
        ReContractInfo reContractInfo = this.queryEntity(reContractInfoEditParam.getId());
        BeanUtil.copyProperties(reContractInfoEditParam, reContractInfo);
        this.updateById(reContractInfo);
    }

    @Tran
    @Override
    public void delete(List<ReContractInfoIdParam> reContractInfoIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(reContractInfoIdParamList, ReContractInfoIdParam::getId));
    }

    @Override
    public ReContractInfo detail(ReContractInfoIdParam reContractInfoIdParam) {
        return this.queryEntity(reContractInfoIdParam.getId());
    }

    @Override
    public ReContractInfo queryEntity(String id) {
        ReContractInfo reContractInfo = this.getById(id);
        if(ObjectUtil.isEmpty(reContractInfo)) {
            throw new CommonException("合同信息不存在，id值为：{}", id);
        }
        return reContractInfo;
    }
}
