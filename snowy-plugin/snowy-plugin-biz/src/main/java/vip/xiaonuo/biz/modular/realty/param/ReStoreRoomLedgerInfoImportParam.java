package vip.xiaonuo.biz.modular.realty.param;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import lombok.Data;

import java.util.Date;

@Data
public class ReStoreRoomLedgerInfoImportParam {
    @ExcelProperty("序号")
    private String serialNumber;

    @ExcelProperty("客户姓名")
    private String customerName;

    @ExcelProperty("身份证号")
    private String idCardNumber;

    @ExcelProperty("认购日期")
    @DateTimeFormat("yyyy/MM/dd")
    private Date subscriptionDate;

    @ExcelProperty("签约日期")
    @DateTimeFormat("yyyy/MM/dd")
    private Date contractDate;

    @ExcelProperty("电话")
    private String phone;

    @ExcelProperty("楼号")
    private String buildingNumber;

    @ExcelProperty("层")
    private String floor;

    @ExcelProperty("室")
    private String room;

    @ExcelProperty("房源位置")
    private String houseLocation;

    @ExcelProperty("面积")
    private String area;

    @ExcelProperty("底总价")
    private String baseTotalPrice;

    @ExcelProperty("销售单价")
    private String salesUnitPrice;

    @ExcelProperty("优惠")
    private String discount;

    @ExcelProperty("签约价")
    private String contractPrice;

    @ExcelProperty("定金")
    private String deposit;

    @ExcelProperty("共计交款")
    private String totalPayment;

    @ExcelProperty("欠款")
    private String arrears;

    @ExcelProperty("备注")
    private String remarks;
}
