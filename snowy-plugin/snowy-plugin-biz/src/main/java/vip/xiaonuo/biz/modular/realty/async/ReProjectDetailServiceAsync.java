package vip.xiaonuo.biz.modular.realty.async;

import cn.hutool.core.collection.ListUtil;
import com.mybatisflex.core.query.QueryWrapper;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import vip.xiaonuo.biz.modular.realty.entity.ReHouse;
import vip.xiaonuo.biz.modular.realty.entity.RePark;
import vip.xiaonuo.biz.modular.realty.entity.ReParkArea;
import vip.xiaonuo.biz.modular.realty.entity.ReProjectDetail;
import vip.xiaonuo.biz.modular.realty.service.ReHouseService;
import vip.xiaonuo.biz.modular.realty.service.ReParkAreaService;
import vip.xiaonuo.biz.modular.realty.service.ReParkService;
import vip.xiaonuo.biz.modular.realty.service.ReProjectDetailService;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/30 18:13
 */
@Component
public class ReProjectDetailServiceAsync {

    @Inject
    private ReHouseService reHouseService;

    @Inject
    private ReProjectDetailService reProjectDetailService;

    @Inject
    private ReParkService reParkService;

    @Inject
    private ReParkAreaService reParkAreaService;

    /**
     * 更新或插入详情
     *
     * <AUTHOR>
     * @date 2024/8/30 18:14
     */
    public void updateOrInsertDetail(ReHouse reHouse, String projectId) {
        // 异步处理项目详情
        String houseType = reHouse.getHouseType();
        ReProjectDetail reProjectDetail = new ReProjectDetail();

        List<ReHouse> list = reHouseService.list(QueryWrapper.create().eq(ReHouse::getHouseType, houseType).eq(ReHouse::getProjectId, projectId));
        reProjectDetailService.getOneOpt(QueryWrapper.create().eq(ReProjectDetail::getProjectId, projectId).eq(ReProjectDetail::getType, houseType)).ifPresent(s -> {
            reProjectDetail.setId(s.getId());
        });
        reProjectDetail.setProjectId(projectId);
        reProjectDetail.setType(houseType);
        long count = list.stream().filter(s -> !s.getStatus().equals("1")).count();
        reProjectDetail.setSoldHouse(Integer.valueOf(String.valueOf(count)));
        reProjectDetail.setSoldArea(list.stream().filter(s -> !s.getStatus().equals("1") && s.getActualBuildArea() != null).map(ReHouse::getActualBuildArea).reduce(BigDecimal.ZERO, BigDecimal::add));
        reProjectDetail.setTotalHouse(list.size());
        reProjectDetail.setTotalArea(list.stream().filter(s -> s.getActualBuildArea() != null).map(ReHouse::getActualBuildArea).reduce(BigDecimal.ZERO, BigDecimal::add));
        reProjectDetail.setUnsoldHouse(Integer.valueOf(String.valueOf(list.size() - count)));
        reProjectDetail.setUnsoldArea(list.stream().filter(s -> s.getStatus().equals("1") && s.getActualBuildArea() != null).map(ReHouse::getActualBuildArea).reduce(BigDecimal.ZERO, BigDecimal::add));
        reProjectDetailService.saveOrUpdate(reProjectDetail);
    }

    /**
     * 更新或插入详情
     *
     * <AUTHOR>
     * @date 2024/8/30 18:14
     */
    public void updateOrInsertDetail(String projectId) {
        // 异步处理项目详情
        ReProjectDetail reProjectDetail = new ReProjectDetail();
        List<ReParkArea> list1 = reParkAreaService.list(QueryWrapper.create().eq(ReParkArea::getProjectId, projectId));
        List<RePark> list = ListUtil.empty();
        if (list1.size() > 0) {
            list = reParkService.list(QueryWrapper.create().in(RePark::getAreaId, list1.stream().map(s -> s.getId()).collect(Collectors.toList())));
        }
        reProjectDetailService.getOneOpt(QueryWrapper.create().eq(ReProjectDetail::getProjectId, projectId).eq(ReProjectDetail::getType, "4")).ifPresent(s -> {
            reProjectDetail.setId(s.getId());
        });
        reProjectDetail.setProjectId(projectId);
        reProjectDetail.setType("4");
        long count = list.stream().filter(s -> !s.getStatus().equals("1")).count();
        reProjectDetail.setSoldHouse(Integer.valueOf(String.valueOf(count)));
        reProjectDetail.setTotalHouse(list.size());
        reProjectDetail.setTotalArea(BigDecimal.valueOf(list.stream().filter(s -> null != s.getExtJson()).map(RePark::getExtJson).mapToDouble(Double::valueOf).reduce(0, Double::sum)));
        reProjectDetail.setSoldArea(BigDecimal.valueOf(list.stream().filter(s -> !s.getStatus().equals("1") && s.getExtJson() != null).map(RePark::getExtJson).mapToDouble(Double::valueOf).reduce(0, Double::sum)));
        reProjectDetail.setUnsoldArea(BigDecimal.valueOf(list.stream().filter(s -> s.getStatus().equals("1") && s.getExtJson() != null).map(RePark::getExtJson).mapToDouble(Double::valueOf).reduce(0, Double::sum)));
        reProjectDetail.setUnsoldHouse(Integer.valueOf(String.valueOf(list.size() - count)));
        reProjectDetailService.saveOrUpdate(reProjectDetail);
    }


}
