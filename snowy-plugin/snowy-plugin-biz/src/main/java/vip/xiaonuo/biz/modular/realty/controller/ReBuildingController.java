/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.mybatisflex.core.paginate.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.noear.solon.annotation.*;
import org.noear.solon.validation.annotation.NotEmpty;
import org.noear.solon.validation.annotation.Valid;
import vip.xiaonuo.biz.modular.realty.entity.ReBuilding;
import vip.xiaonuo.biz.modular.realty.entity.ReProjectDetail;
import vip.xiaonuo.biz.modular.realty.param.ReBuildingAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReBuildingEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReBuildingIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReBuildingPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReBuildingService;
import vip.xiaonuo.common.annotation.CommonLog;
import vip.xiaonuo.common.pojo.CommonResult;
import vip.xiaonuo.common.pojo.CommonValidList;

import java.util.List;

/**
 * 楼栋管理控制器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 */
@Api(tags = "楼栋管理控制器")
@Controller
@Valid
public class ReBuildingController {

    @Inject
    private ReBuildingService reBuildingService;

    /**
     * 获取楼栋管理分页
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取楼栋管理分页")
    @SaCheckPermission("/biz/rebuilding/page")
    @Get
    @Mapping("/biz/rebuilding/page")
    public CommonResult<Page<ReBuilding>> page(ReBuildingPageParam reBuildingPageParam) {
        return CommonResult.data(reBuildingService.page(reBuildingPageParam));
    }

    /**
     * 添加楼栋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("添加楼栋管理")
    @CommonLog("添加楼栋管理")
    @SaCheckPermission("/biz/rebuilding/add")
    @Post
    @Mapping("/biz/rebuilding/add")
    public CommonResult<String> add(ReBuildingAddParam reBuildingAddParam) {
        reBuildingService.add(reBuildingAddParam);
        return CommonResult.ok();
    }

    /**
     * 编辑楼栋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("编辑楼栋管理")
    @CommonLog("编辑楼栋管理")
    @SaCheckPermission("/biz/rebuilding/edit")
    @Post
    @Mapping("/biz/rebuilding/edit")
    public CommonResult<String> edit(ReBuildingEditParam reBuildingEditParam) {
        reBuildingService.edit(reBuildingEditParam);
        return CommonResult.ok();
    }

    /**
     * 删除楼栋管理
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("删除楼栋管理")
    @CommonLog("删除楼栋管理")
    @SaCheckPermission("/biz/rebuilding/delete")
    @Post
    @Mapping("/biz/rebuilding/delete")
    public CommonResult<String> delete(@NotEmpty(message = "集合不能为空")
                                                   CommonValidList<ReBuildingIdParam> reBuildingIdParamList) {
        reBuildingService.delete(reBuildingIdParamList);
        return CommonResult.ok();
    }

    /**
     * 获取楼栋管理详情
     *
     * <AUTHOR>
     * @date  2024/08/17 14:34
     */
    @ApiOperation("获取楼栋管理详情")
    @SaCheckPermission("/biz/rebuilding/detail")
    @Get
    @Mapping("/biz/rebuilding/detail")
    public CommonResult<ReBuilding> detail(ReBuildingIdParam reBuildingIdParam) {
        return CommonResult.data(reBuildingService.detail(reBuildingIdParam));
    }

    /**
     * 查询楼栋详情
     *
     * @param
     * @return
     * <AUTHOR>
     * @date 2024/10/7 10:42
     */
    @ApiOperation("查询楼栋详情")
    @SaCheckPermission("/biz/rebuilding/queryEntity")
    @Get
    @Mapping("/biz/rebuilding/queryEntity")
    public CommonResult<List<ReProjectDetail>> queryEntity(String id) {
        return CommonResult.data(reBuildingService.queryEntityById(id));
    }
}
