/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import org.noear.solon.validation.annotation.NotBlank;
import org.noear.solon.validation.annotation.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 社区信息预警编辑参数
 *
 * <AUTHOR>
 * @date  2024/08/30 10:05
 **/
@Getter
@Setter
public class ReCommunityAlarmInfoEditParam {

    /** ID */
    @ApiModelProperty(value = "ID", required = true, position = 1)
    @NotBlank(message = "id不能为空")
    private String id;

    /** 房源类型 */
    @ApiModelProperty(value = "房源类型", position = 2)
    private String houseType;

    /** 楼栋 */
    @ApiModelProperty(value = "楼栋", position = 3)
    private String building;

    /** 房号 */
    @ApiModelProperty(value = "房号", position = 4)
    private String houseNumber;

    /** 社区 */
    @ApiModelProperty(value = "社区", position = 5)
    private String community;

    /** 区域 */
    @ApiModelProperty(value = "区域", position = 6)
    private String area;

    /** 车位编号 */
    @ApiModelProperty(value = "车位编号", position = 7)
    private String parkingNumber;

    /** 客户姓名 */
    @ApiModelProperty(value = "客户姓名", position = 8)
    private String customerName;

    /** 联系方式 */
    @ApiModelProperty(value = "联系方式", position = 9)
    private String contactInformation;

    /** 截至日期 */
    @ApiModelProperty(value = "截至日期", position = 10)
    private Date deadline;

    /** 台账id */
    @ApiModelProperty(value = "台账id", position = 11)
    private String ledgerId;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息", position = 12)
    private String extJson;

}
