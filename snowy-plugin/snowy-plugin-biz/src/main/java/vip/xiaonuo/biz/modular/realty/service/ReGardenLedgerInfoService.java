/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.service.IService;
import org.noear.solon.core.handle.Context;
import vip.xiaonuo.biz.modular.realty.entity.ReGardenLedgerInfo;
import vip.xiaonuo.biz.modular.realty.param.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 园区房屋台账信息Service接口
 *
 * <AUTHOR>
 * @date  2024/08/28 15:32
 **/
public interface ReGardenLedgerInfoService extends IService<ReGardenLedgerInfo> {

    /**
     * 获取园区房屋台账信息分页
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    Page<ReGardenLedgerInfo> page(ReGardenLedgerInfoPageParam reGardenLedgerInfoPageParam);

    /**
     * 添加园区房屋台账信息
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    void add(ReGardenLedgerInfoAddParam reGardenLedgerInfoAddParam);

    /**
     * 编辑园区房屋台账信息
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    void edit(ReGardenLedgerInfoEditParam reGardenLedgerInfoEditParam);

    /**
     * 删除园区房屋台账信息
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    void delete(List<ReGardenLedgerInfoIdParam> reGardenLedgerInfoIdParamList);

    /**
     * 获取园区房屋台账信息详情
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     */
    ReGardenLedgerInfo detail(ReGardenLedgerInfoIdParam reGardenLedgerInfoIdParam);

    /**
     * 获取园区房屋台账信息详情
     *
     * <AUTHOR>
     * @date  2024/08/28 15:32
     **/
    ReGardenLedgerInfo queryEntity(String id);

    ReGardenLedgerInfoWithParam detailWithSub(ReGardenLedgerInfoIdParam reGardenLedgerInfoIdParam);

    ReGardenLedgerInfoWithParam detailByHouseId(ReGardenHouseIdParam reGardenHouseIdParam);

    void AddDetail(ReGardenLedgerInfoWithParam reGardenLedgerInfoWithParam);

    Map<String,List<ReGardenStatisticsParam>> statistics(ReGardenHouseIdParam reGardenHouseIdParam);

    void restore(ReGardenHouseIdParam reGardenHouseIdParam);

    void UpdateDetail(ReGardenLedgerInfoWithParam reGardenLedgerInfoWithParam);

    void relet(ReGardenLedgerInfoWithParam reGardenLedgerInfoWithParam);

    Map<String,List<ReGardenIndexProject>> indexStatistics();

    void export(ReGardenHouseIdParam reGardenHouseIdParam, Context context,Boolean isHistory) throws IOException;
}
