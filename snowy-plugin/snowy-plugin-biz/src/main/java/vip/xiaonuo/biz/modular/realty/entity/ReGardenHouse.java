/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import vip.xiaonuo.common.pojo.CommonEntity;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 园区房屋管理实体
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
@Getter
@Setter
@Table(value = "re_garden_house",mapperGenerateEnable = false)
public class ReGardenHouse extends CommonEntity {

    /** ID */
    @Id
    @ApiModelProperty(value = "ID", position = 1)
    private String id;

    /** 房屋类型--字典（厂房、办公用房、宿舍） */
    @ApiModelProperty(value = "房屋类型--字典（厂房、办公用房、宿舍）", position = 2)
    private String houseType;

    /** 房源楼号 */
    @ApiModelProperty(value = "房源楼号", position = 3)
    private String buildId;

    /** 房源楼层 */
    @ApiModelProperty(value = "房源楼层", position = 4)
    private Integer floor;

    /** 房源房号 */
    @ApiModelProperty(value = "房源房号", position = 5)
    private String houseNum;

    /** 房源位置 */
    @ApiModelProperty(value = "房源位置", position = 6)
    private String houseLocation;

    /** 建筑面积 */
    @ApiModelProperty(value = "建筑面积", position = 7)
    private BigDecimal buildArea;

    /** 连廊面积 */
    @ApiModelProperty(value = "连廊面积", position = 8)
    private BigDecimal corridorArea;

    /** 房屋状态--字典（待售、售出-未结、售出-已结，租赁） */
    @ApiModelProperty(value = "房屋状态--字典（待售、售出-未结、售出-已结，租赁）", position = 9)
    private String status;

    /**
     * 签约状态
     **/
    @ApiModelProperty(value = "签约状态", position = 10)
    @Column(ignore = true)
    private String contractStatus;

    /** 项目编号 */
    @ApiModelProperty(value = "项目编号", position = 10)
    private String projectId;

    /** 客户姓名 */
    @ApiModelProperty(value = "客户姓名", position = 11)
    private String customerName;

    /** 客户电话 */
    @ApiModelProperty(value = "客户电话", position = 12)
    private String customerPhone;

    /** 销售单价--展示使用 */
    @ApiModelProperty(value = "销售单价--展示使用", position = 13)
    private BigDecimal salesUnitPrice;

    /** 销售总价--展示使用 */
    @ApiModelProperty(value = "销售总价--展示使用", position = 14)
    private BigDecimal salesTotalPrice;

    /** 租赁单价--展示使用 */
    @ApiModelProperty(value = "租赁单价--展示使用", position = 15)
    private BigDecimal leaseUnitPrice;

    /** 租赁时间--展示使用 */
    @ApiModelProperty(value = "租赁时间--展示使用", position = 16)
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date leaseTime;

    /** 扩展信息 */
    @ApiModelProperty(value = "扩展信息", position = 17)
    private String extJson;

}
