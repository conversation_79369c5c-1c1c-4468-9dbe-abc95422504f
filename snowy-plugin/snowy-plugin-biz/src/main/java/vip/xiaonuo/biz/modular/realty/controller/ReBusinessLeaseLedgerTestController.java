package vip.xiaonuo.biz.modular.realty.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import com.mybatisflex.core.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.noear.solon.annotation.Controller;
import org.noear.solon.annotation.Get;
import org.noear.solon.annotation.Inject;
import org.noear.solon.annotation.Mapping;
import vip.xiaonuo.biz.modular.realty.entity.ReLeaseContract;
import vip.xiaonuo.biz.modular.realty.entity.ReLedgerInfo;
import vip.xiaonuo.biz.modular.realty.param.ReLedgerInfoXiaoKongParamVo;
import vip.xiaonuo.biz.modular.realty.service.ReLeaseContractService;
import vip.xiaonuo.biz.modular.realty.service.ReLedgerInfoService;
import vip.xiaonuo.common.pojo.CommonResult;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商业租赁台账测试控制器
 * 用于验证租赁总价显示问题的修复效果
 * 
 * <AUTHOR> 4.0 sonnet
 * @date 2024/12/19
 */
@Api(tags = "商业租赁台账测试控制器")
@Controller
@Slf4j
public class ReBusinessLeaseLedgerTestController {

    @Inject
    private ReLedgerInfoService reLedgerInfoService;

    @Inject
    private ReLeaseContractService reLeaseContractService;

    /**
     * 检查商业租赁台账的租赁总价显示问题
     */
    @ApiOperation("检查商业租赁台账租赁总价显示")
    @Get
    @Mapping("/test/businessLease/checkLeaseTotalPrice")
    public CommonResult<JSONObject> checkLeaseTotalPrice() {
        try {
            log.info("🧪 开始检查商业租赁台账租赁总价显示问题...");
            
            JSONObject result = new JSONObject();
            List<Map<String, Object>> issues = new ArrayList<>();
            List<Map<String, Object>> normalRecords = new ArrayList<>();
            
            // 查询所有商业租赁台账（房屋类型=2，签约类型=2）
            QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ReLedgerInfo::getHouseType, "2")  // 商业
                .eq(ReLedgerInfo::getContractType, "2")  // 租赁
                .eq(ReLedgerInfo::getIsHistory, false);  // 非历史记录
            
            List<ReLedgerInfo> ledgerList = reLedgerInfoService.list(queryWrapper);
            log.info("找到 {} 条商业租赁台账记录", ledgerList.size());
            
            for (ReLedgerInfo ledger : ledgerList) {
                Map<String, Object> recordInfo = checkSingleLedgerRecord(ledger);
                
                if ((Boolean) recordInfo.get("hasIssue")) {
                    issues.add(recordInfo);
                } else {
                    normalRecords.add(recordInfo);
                }
            }
            
            result.set("totalRecords", ledgerList.size());
            result.set("issueCount", issues.size());
            result.set("normalCount", normalRecords.size());
            result.set("issues", issues);
            result.set("normalRecords", normalRecords.size() > 10 ? normalRecords.subList(0, 10) : normalRecords);
            result.set("checkTime", DateUtil.now());
            
            String summary = String.format("检查完成：总记录数 %d，问题记录 %d，正常记录 %d", 
                ledgerList.size(), issues.size(), normalRecords.size());
            result.set("summary", summary);
            
            log.info("✅ {}", summary);
            return CommonResult.data(result);
            
        } catch (Exception e) {
            String errorMessage = "检查商业租赁台账失败：" + e.getMessage();
            log.error(errorMessage, e);
            return CommonResult.error(errorMessage);
        }
    }

    /**
     * 获取指定台账的详细信息（用于验证详情页面显示）
     */
    @ApiOperation("获取台账详细信息")
    @Get
    @Mapping("/test/businessLease/getLedgerDetail")
    public CommonResult<JSONObject> getLedgerDetail(String ledgerId) {
        try {
            log.info("🔍 获取台账详细信息：{}", ledgerId);
            
            // 获取台账详情
            ReLedgerInfoXiaoKongParamVo detail = reLedgerInfoService.queryEntityById(ledgerId);
            
            JSONObject result = new JSONObject();
            result.set("ledgerInfo", detail.getLedgerInfo());
            result.set("leaseSign", detail.getLeaseSign());
            result.set("customerInfo", detail.getCustomerInfo());
            result.set("paymentInfos", detail.getPaymentInfos());
            
            // 验证租赁总价字段
            if (detail.getLeaseSign() != null) {
                ReLeaseContract leaseContract = detail.getLeaseSign();
                JSONObject validation = new JSONObject();
                validation.set("leaseTotalPrice", leaseContract.getLeaseTotalPrice());
                validation.set("totalHousePrice", leaseContract.getTotalHousePrice());
                validation.set("leaseUnitPrice", leaseContract.getLeaseUnitPrice());
                validation.set("isConsistent", 
                    leaseContract.getLeaseTotalPrice() != null && 
                    leaseContract.getLeaseTotalPrice().equals(leaseContract.getTotalHousePrice()));
                
                result.set("validation", validation);
            }
            
            log.info("✅ 台账详情获取成功");
            return CommonResult.data(result);
            
        } catch (Exception e) {
            String errorMessage = "获取台账详情失败：" + e.getMessage();
            log.error(errorMessage, e);
            return CommonResult.error(errorMessage);
        }
    }

    /**
     * 修复指定台账的租赁总价问题
     */
    @ApiOperation("修复台账租赁总价")
    @Get
    @Mapping("/test/businessLease/fixLeaseTotalPrice")
    public CommonResult<String> fixLeaseTotalPrice(String ledgerId) {
        try {
            log.info("🔧 开始修复台账租赁总价：{}", ledgerId);
            
            // 获取台账信息
            ReLedgerInfo ledger = reLedgerInfoService.getById(ledgerId);
            if (ledger == null) {
                return CommonResult.error("台账不存在");
            }
            
            // 获取租赁合同
            QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ReLeaseContract::getLedgerId, ledgerId);
            ReLeaseContract leaseContract = reLeaseContractService.getOne(queryWrapper);
            
            if (leaseContract == null) {
                return CommonResult.error("租赁合同不存在");
            }
            
            // 检查是否需要修复
            boolean needFix = leaseContract.getLeaseTotalPrice() == null || 
                             leaseContract.getLeaseTotalPrice().equals(BigDecimal.ZERO) ||
                             !leaseContract.getLeaseTotalPrice().equals(leaseContract.getTotalHousePrice());
            
            if (!needFix) {
                return CommonResult.data("租赁总价正常，无需修复");
            }
            
            // 尝试修复：如果totalHousePrice有值，则同步到leaseTotalPrice
            if (leaseContract.getTotalHousePrice() != null && 
                leaseContract.getTotalHousePrice().compareTo(BigDecimal.ZERO) > 0) {
                
                leaseContract.setLeaseTotalPrice(leaseContract.getTotalHousePrice());
                reLeaseContractService.updateById(leaseContract);
                
                String message = String.format("修复成功：租赁总价已设置为 %s", leaseContract.getTotalHousePrice());
                log.info("✅ {}", message);
                return CommonResult.data(message);
            }
            
            return CommonResult.error("无法修复：缺少有效的价格数据");
            
        } catch (Exception e) {
            String errorMessage = "修复租赁总价失败：" + e.getMessage();
            log.error(errorMessage, e);
            return CommonResult.error(errorMessage);
        }
    }

    /**
     * 获取修复建议
     */
    @ApiOperation("获取修复建议")
    @Get
    @Mapping("/test/businessLease/getFixSuggestions")
    public CommonResult<JSONObject> getFixSuggestions() {
        JSONObject suggestions = new JSONObject();
        
        suggestions.set("问题描述", "商业租赁台账导入后，租赁总价字段在详情页面和编辑页面无法正常显示");
        
        List<String> causes = new ArrayList<>();
        causes.add("导入过程中租赁总价计算逻辑错误");
        causes.add("leaseTotalPrice和totalHousePrice字段不一致");
        causes.add("列表查询时未处理租赁合同的totalHousePrice");
        causes.add("前端页面数据绑定路径错误");
        suggestions.set("可能原因", causes);
        
        List<String> fixes = new ArrayList<>();
        fixes.add("修复ImportBussnessLeaseLedgerInfoListener中的租赁总价计算逻辑");
        fixes.add("在ReLedgerInfoServiceImpl.page()方法中添加租赁合同处理");
        fixes.add("确保leaseTotalPrice和totalHousePrice数据一致性");
        fixes.add("验证前端页面中leaseSign.leaseTotalPrice字段绑定");
        suggestions.set("修复方案", fixes);
        
        List<String> tests = new ArrayList<>();
        tests.add("使用/test/businessLease/checkLeaseTotalPrice检查问题记录");
        tests.add("使用/test/businessLease/getLedgerDetail查看详情数据");
        tests.add("使用/test/businessLease/fixLeaseTotalPrice修复单条记录");
        tests.add("重新导入Excel文件验证修复效果");
        suggestions.set("测试步骤", tests);
        
        return CommonResult.data(suggestions);
    }

    /**
     * 检查单条台账记录
     */
    private Map<String, Object> checkSingleLedgerRecord(ReLedgerInfo ledger) {
        Map<String, Object> recordInfo = new HashMap<>();
        recordInfo.put("ledgerId", ledger.getId());
        recordInfo.put("customerName", ledger.getName());
        recordInfo.put("houseNumber", ledger.getHouseNumber());
        recordInfo.put("buildCode", ledger.getBuildCode());
        
        List<String> issues = new ArrayList<>();
        boolean hasIssue = false;
        
        try {
            // 检查租赁合同
            QueryWrapper queryWrapper = QueryWrapper.create()
                .eq(ReLeaseContract::getLedgerId, ledger.getId());
            ReLeaseContract leaseContract = reLeaseContractService.getOne(queryWrapper);
            
            if (leaseContract == null) {
                issues.add("缺少租赁合同");
                hasIssue = true;
            } else {
                recordInfo.put("leaseTotalPrice", leaseContract.getLeaseTotalPrice());
                recordInfo.put("totalHousePrice", leaseContract.getTotalHousePrice());
                recordInfo.put("leaseUnitPrice", leaseContract.getLeaseUnitPrice());
                
                // 检查租赁总价
                if (leaseContract.getLeaseTotalPrice() == null) {
                    issues.add("租赁总价为空");
                    hasIssue = true;
                } else if (leaseContract.getLeaseTotalPrice().equals(BigDecimal.ZERO)) {
                    issues.add("租赁总价为0");
                    hasIssue = true;
                }
                
                // 检查数据一致性
                if (leaseContract.getLeaseTotalPrice() != null && 
                    leaseContract.getTotalHousePrice() != null &&
                    !leaseContract.getLeaseTotalPrice().equals(leaseContract.getTotalHousePrice())) {
                    issues.add("租赁总价与房款合计不一致");
                    hasIssue = true;
                }
            }
            
        } catch (Exception e) {
            issues.add("查询异常：" + e.getMessage());
            hasIssue = true;
        }
        
        recordInfo.put("issues", issues);
        recordInfo.put("hasIssue", hasIssue);
        
        return recordInfo;
    }
}
