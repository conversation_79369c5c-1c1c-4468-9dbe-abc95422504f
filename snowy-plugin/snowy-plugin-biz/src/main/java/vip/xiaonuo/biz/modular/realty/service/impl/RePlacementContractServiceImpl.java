/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.ReLedgerInfo;
import vip.xiaonuo.biz.modular.realty.entity.RePlacementContract;
import vip.xiaonuo.biz.modular.realty.mapper.RePlacementContractMapper;
import vip.xiaonuo.biz.modular.realty.param.RePlacementContractAddParam;
import vip.xiaonuo.biz.modular.realty.param.RePlacementContractEditParam;
import vip.xiaonuo.biz.modular.realty.param.RePlacementContractIdParam;
import vip.xiaonuo.biz.modular.realty.param.RePlacementContractPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReLedgerInfoService;
import vip.xiaonuo.biz.modular.realty.service.RePlacementContractService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;

import java.util.List;

/**
 * 安置签约Service接口实现类
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
@Component
public class RePlacementContractServiceImpl extends ServiceImpl<RePlacementContractMapper, RePlacementContract> implements RePlacementContractService {

    @Inject
    private ReLedgerInfoService reLedgerInfoService;

    @Override
    public Page<RePlacementContract> page(RePlacementContractPageParam rePlacementContractPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        if(ObjectUtil.isAllNotEmpty(rePlacementContractPageParam.getSortField(), rePlacementContractPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(rePlacementContractPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(rePlacementContractPageParam.getSortField()),rePlacementContractPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(RePlacementContract::getId);
        }
        return this.page(CommonPageRequest.defaultPage(), queryWrapper);
    }

    @Tran
    @Override
    public String add(RePlacementContractAddParam rePlacementContractAddParam) {
        RePlacementContract rePlacementContract = BeanUtil.toBean(rePlacementContractAddParam, RePlacementContract.class);
        this.save(rePlacementContract);
        return rePlacementContract.getId();
    }

    @Tran
    @Override
    public void edit(RePlacementContractEditParam rePlacementContractEditParam) {
        RePlacementContract rePlacementContract = this.queryEntity(rePlacementContractEditParam.getId());
        BeanUtil.copyProperties(rePlacementContractEditParam, rePlacementContract);
        this.updateById(rePlacementContract);
    }

    @Tran
    @Override
    public void delete(List<RePlacementContractIdParam> rePlacementContractIdParamList) {
        // 执行删除
        this.removeByIds(CollStreamUtil.toList(rePlacementContractIdParamList, RePlacementContractIdParam::getId));
    }

    @Override
    public RePlacementContract detail(RePlacementContractIdParam rePlacementContractIdParam) {
        return this.queryEntity(rePlacementContractIdParam.getId());
    }

    @Override
    public RePlacementContract queryEntity(String id) {
        RePlacementContract rePlacementContract = this.getById(id);
        if(ObjectUtil.isEmpty(rePlacementContract)) {
            throw new CommonException("安置签约不存在，id值为：{}", id);
        }
        return rePlacementContract;
    }
}
