/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.biz.modular.realty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.update.UpdateWrapper;
import com.mybatisflex.core.util.StringUtil;
import com.mybatisflex.solon.service.impl.ServiceImpl;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import org.noear.solon.data.annotation.Tran;
import vip.xiaonuo.biz.modular.realty.entity.ReBuilding;
import vip.xiaonuo.biz.modular.realty.entity.ReHouse;
import vip.xiaonuo.biz.modular.realty.entity.ReProject;
import vip.xiaonuo.biz.modular.realty.entity.ReProjectDetail;
import vip.xiaonuo.biz.modular.realty.mapper.ReBuildingMapper;
import vip.xiaonuo.biz.modular.realty.param.ReBuildingAddParam;
import vip.xiaonuo.biz.modular.realty.param.ReBuildingEditParam;
import vip.xiaonuo.biz.modular.realty.param.ReBuildingIdParam;
import vip.xiaonuo.biz.modular.realty.param.ReBuildingPageParam;
import vip.xiaonuo.biz.modular.realty.service.ReBuildingService;
import vip.xiaonuo.biz.modular.realty.service.ReHouseService;
import vip.xiaonuo.biz.modular.realty.service.ReProjectService;
import vip.xiaonuo.common.enums.CommonSortOrderEnum;
import vip.xiaonuo.common.exception.CommonException;
import vip.xiaonuo.common.page.CommonPageRequest;
import vip.xiaonuo.common.util.CommonServletUtil;
import vip.xiaonuo.dev.api.DevDictApi;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 楼栋管理Service接口实现类
 *
 * <AUTHOR>
 * @date 2024/08/17 14:34
 **/
@Component
public class ReBuildingServiceImpl extends ServiceImpl<ReBuildingMapper, ReBuilding> implements ReBuildingService {

    @Inject
    private ReProjectService reProjectService;

    @Inject
    private DevDictApi devDictApi;

    @Inject
    private ReHouseService rehouseService;

    @Override
    public Page<ReBuilding> page(ReBuildingPageParam reBuildingPageParam) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq(ReBuilding::getType, reBuildingPageParam.getType(), StringUtil.isNotBlank(reBuildingPageParam.getType()));
        queryWrapper.eq(ReBuilding::getProjectId, reBuildingPageParam.getProjectId(), StringUtil.isNotBlank(reBuildingPageParam.getProjectId()));
        if (ObjectUtil.isAllNotEmpty(reBuildingPageParam.getSortField(), reBuildingPageParam.getSortOrder())) {
            CommonSortOrderEnum.validate(reBuildingPageParam.getSortOrder());
            queryWrapper.orderBy(StrUtil.toUnderlineCase(reBuildingPageParam.getSortField()), reBuildingPageParam.getSortOrder().equals(CommonSortOrderEnum.ASC.getValue()));
        } else {
            queryWrapper.orderBy(ReBuilding::getId);
        }
        //此接口不传分页时,查询全部数据
        String size = CommonServletUtil.getParamFromRequest("size");
        Page<ReBuilding> objectPage;
        if (StrUtil.isBlank(size)) {
            objectPage = CommonPageRequest.defaultPage();
            objectPage.setPageSize(CommonPageRequest.getPageSizeMaxValue());
        } else {
            objectPage = CommonPageRequest.defaultPage();
        }
        return this.page(objectPage, queryWrapper);
    }

    @Tran
    @Override
    public void add(ReBuildingAddParam reBuildingAddParam) {
        ReBuilding reBuilding = BeanUtil.toBean(reBuildingAddParam, ReBuilding.class);
        if (null == reBuilding.getProjectId()) {
            throw new CommonException("项目id不能为空");
        }
        /** 通过项目id查询项目详情 **/
        ReProject byId = reProjectService.getById(reBuilding.getProjectId());
        if (null == byId) {
            throw new CommonException("项目不存在，id值为：{}", reBuilding.getProjectId());
        }
        long count = this.count(QueryWrapper.create().eq(ReBuilding::getProjectId, reBuilding.getProjectId()));
        // 同步修改项目表的楼栋数
        byId.setBuildNum(Integer.valueOf(String.valueOf(count)) + 1);
        reProjectService.updateById(byId);
        this.save(reBuilding);
    }

    @Tran
    @Override
    public void edit(ReBuildingEditParam reBuildingEditParam) {
        ReBuilding reBuilding = this.queryEntity(reBuildingEditParam.getId());
        BeanUtil.copyProperties(reBuildingEditParam, reBuilding);
        this.updateById(reBuilding);
    }

    @Tran
    @Override
    public void delete(List<ReBuildingIdParam> reBuildingIdParamList) {
        // 执行删除 删除之前判断是哪个项目的楼栋减少对应的数量
        reBuildingIdParamList.forEach(s -> {
            ReProject byId = reProjectService.getById(s.getId());
            if (null != byId) {
                byId.setBuildNum(byId.getBuildNum() - 1);
                reProjectService.updateById(byId);
            }
        });
        //楼栋删除前将楼下房屋删除

       reHouseService.updateChain().set(ReHouse::getDeleteFlag,"deleted")
               .in(ReHouse::getBuildId,CollStreamUtil.toList(reBuildingIdParamList, ReBuildingIdParam::getId)).update();

        this.removeByIds(CollStreamUtil.toList(reBuildingIdParamList, ReBuildingIdParam::getId));
    }

    @Override
    public ReBuilding detail(ReBuildingIdParam reBuildingIdParam) {
        return this.queryEntity(reBuildingIdParam.getId());
    }

    @Override
    public ReBuilding queryEntity(String id) {
        ReBuilding reBuilding = this.getById(id);
        if (ObjectUtil.isEmpty(reBuilding)) {
            throw new CommonException("楼栋管理不存在，id值为：{}", id);
        }
        return reBuilding;
    }

    @Inject
    private ReHouseService reHouseService;

    @Override
    public List<ReProjectDetail> queryEntityById(String id) {
        List<ReHouse> list = reHouseService.list(QueryWrapper.create().eq(ReHouse::getBuildId, id));
        List<ReProjectDetail> objects = new ArrayList<>();

        devDictApi.getDictListByType("house_type").forEach(e -> {
            ReProjectDetail reProjectDetail = new ReProjectDetail();
            List<ReHouse> collect = list.stream().filter(s -> s.getHouseType().equals(JSONUtil.parseObj(e).getStr("dictValue"))).collect(Collectors.toList());
            long count = collect.stream().filter(s -> s.getStatus().equals("3")).count();
            reProjectDetail.setSoldHouse(Integer.valueOf(String.valueOf(count)));
            reProjectDetail.setSoldArea(collect.stream().filter(s -> s.getStatus().equals("3") && null != s.getActualBuildArea()).map(ReHouse::getActualBuildArea).reduce(BigDecimal.ZERO, BigDecimal::add));
            reProjectDetail.setTotalHouse(collect.size());
            reProjectDetail.setTotalArea(collect.stream().filter(s -> null != s.getActualBuildArea()).map(ReHouse::getActualBuildArea).reduce(BigDecimal.ZERO, BigDecimal::add));
            reProjectDetail.setUnsoldHouse(Integer.valueOf(String.valueOf(collect.size() - count)));
            reProjectDetail.setUnsoldArea(collect.stream().filter(s -> !s.getStatus().equals("3") && null != s.getActualBuildArea()).map(ReHouse::getActualBuildArea).reduce(BigDecimal.ZERO, BigDecimal::add));
            reProjectDetail.setType(JSONUtil.parseObj(e).getStr("dictValue"));
            objects.add(reProjectDetail);
        });
        return objects;
    }
}
