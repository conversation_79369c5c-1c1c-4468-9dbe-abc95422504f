/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.dev.modular.dict.provider;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.mybatisflex.core.query.QueryWrapper;
import org.noear.solon.annotation.Component;
import org.noear.solon.annotation.Inject;
import vip.xiaonuo.dev.api.DevDictApi;
import vip.xiaonuo.dev.modular.dict.entity.DevDict;
import vip.xiaonuo.dev.modular.dict.service.DevDictService;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 字典API接口实现类
 *
 * <AUTHOR>
 * @date 2022/9/2 16:05
 */
@Component
public class DevDictApiProvider implements DevDictApi {

    @Inject
    private DevDictService devDictService;

    /**
     * 通过字典类型获取字典列表
     *
     * <AUTHOR>
     * @date 2024/9/23 16:54
     */
    @Override
    public JSONArray getDictListByType(String dictType) {
        AtomicReference<JSONArray> array = new AtomicReference<>(JSONUtil.createArray());
        devDictService.getOneOpt(QueryWrapper.create().eq(DevDict::getDictValue, dictType)).ifPresent(s -> {
            List<DevDict> list = devDictService.list(QueryWrapper.create().eq(DevDict::getParentId, s.getId()));
            array.set(JSONUtil.parseArray(list));
        });
        return array.get();
    }
}
