/*
 * Copyright [2022] [https://www.xiaonuo.vip]
 *
 * Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *
 * 1.请不要删除和修改根目录下的LICENSE文件。
 * 2.请不要删除和修改Snowy源码头部的版权声明。
 * 3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 * 4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 * 5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 * 6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
package vip.xiaonuo.dev.api;


import org.noear.solon.core.handle.UploadedFile;

/**
 * 文件API接口，可参考vip.xiaonuo.dev.core.util.file包下的工具类扩展更多需要的方法
 *
 * <AUTHOR>
 * @date 2022/6/22 15:21
 **/
public interface DevFileApi {

    /* =========本地文件========= */

    /**
     * 上传文件返回Url
     *
     * @param file 文件
     * <AUTHOR>
     * @date 2022/6/22 17:44
     **/
    String storageFileWithReturnUrlLocal(UploadedFile file);

    /**
     * 上传文件返回Id
     *
     * @param file 文件
     * <AUTHOR>
     * @date 2022/6/22 17:44
     **/
    String storageFileWithReturnIdLocal(UploadedFile file);

    /* =========阿里云文件========= */

    /**
     * 上传文件返回Url
     *
     * @param file 文件
     * <AUTHOR>
     * @date 2022/6/22 17:44
     **/
    String storageFileWithReturnUrlAliyun(UploadedFile file);

    /**
     * 上传文件返回Id
     *
     * @param file 文件
     * <AUTHOR>
     * @date 2022/6/22 17:44
     **/
    String storageFileWithReturnIdAliyun(UploadedFile file);

    /* =========腾讯云件========= */

    /**
     * 上传文件返回Url
     *
     * @param file 文件
     * <AUTHOR>
     * @date 2022/6/22 17:44
     **/
    String storageFileWithReturnUrlTencent(UploadedFile file);

    /**
     * 上传文件返回Id
     *
     * @param file 文件
     * <AUTHOR>
     * @date 2022/6/22 17:44
     **/
    String storageFileWithReturnIdTencent(UploadedFile file);

    /* =========MINIO件========= */

    /**
     * 上传文件返回Url
     *
     * @param file 文件
     * <AUTHOR>
     * @date 2022/6/22 17:44
     **/
    String storageFileWithReturnUrlMinio(UploadedFile file);

    /**
     * 上传文件返回Id
     *
     * @param file 文件
     * <AUTHOR>
     * @date 2022/6/22 17:44
     **/
    String storageFileWithReturnIdMinio(UploadedFile file);
}
