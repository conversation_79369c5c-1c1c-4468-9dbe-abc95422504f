#########################################
# Involves the configuration of the environment
#########################################

#########################################
# redis configuration
#########################################
snowy.redis.db: 4
snowy.redis.server: 192.168.10.185:6379
snowy.redis.password: ruoyi123
snowy.redis.timeout: 10s

#########################################
# sa-token redis configuration
#########################################
# sa-token redis configuration
sa-token.redis.db: ${snowy.redis.db}
sa-token.redis.server: ${snowy.redis.server}
sa-token.redis.password: ${snowy.redis.password}
sa-token.redis.timeout: ${snowy.redis.timeout}

#########################################
# datasource configuration
#########################################
snowy.datasource.dynamic.type: "com.zaxxer.hikari.HikariDataSource"
snowy.datasource.dynamic.strict: true

# mysql
snowy.datasource.dynamic.master.driverClassName: com.mysql.cj.jdbc.Driver
snowy.datasource.dynamic.master.jdbcUrl: ************************************************************************************************************************************************************************************
snowy.datasource.dynamic.master.username: root
snowy.datasource.dynamic.master.password: root

# oracle
#snowy.datasource.dynamic.master.driver-class-name=oracle.jdbc.driver.OracleDriver
#snowy.datasource.dynamic.master.url=***********************************************************
#snowy.datasource.dynamic.master.username=SNOWY
#snowy.datasource.dynamic.master.password=12345678

# dm database
#snowy.datasource.dynamic.master.driver-class-name=dm.jdbc.driver.DmDriver
#snowy.datasource.dynamic.master.url=jdbc:dm://localhost:5236/SYSDBA
#snowy.datasource.dynamic.master.username=SYSDBA
#snowy.datasource.dynamic.master.password=SYSDBA

# kingbase database
#snowy.datasource.dynamic.master.driverClassName=com.kingbase8.Driver
#snowy.datasource.dynamic.master.url=**************************************
#snowy.datasource.dynamic.master.username=SYSTEM
#snowy.datasource.dynamic.master.password=123456
#snowy.datasource.dynamic.strict=true

#########################################
# snowy configuration
#########################################

# common configuration
snowy.config.common.front-url: http://**************:48003
snowy.config.common.backend-url: http://**************:48003/prod-api