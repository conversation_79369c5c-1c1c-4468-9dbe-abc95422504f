#########################################
# server configuration
#########################################
server.port: 83

#########################################
# solon profiles configuration
#########################################
solon.env: local
#solon.env: test
#solon.env: prod

#########################################
# request configuration
#########################################
server.request.maxHeaderSize: 10kb
server.request.maxBodySize: 100MB
server.request.maxFileSize: 100MB

#########################################
# logging configuration
#########################################
solon.logging.appender:
  console:
    level: "INFO"

solon.logging.logger:
  "root": #默认记录器配置
    level: "INFO"
  "com.zaxxer.hikari":
    level: "WARN"

#########################################
# serialization configuration
#########################################

solon.serialization.json:
  dateAsTimeZone: 'GMT+8'
  dateAsFormat: 'yyyy-MM-dd HH:mm:ss'

#########################################
# mybatis-flex configuration
#########################################
mybatis.snowy:
  typeAliases: #支持包名 或 类名（大写开头 或 *）//支持 ** 或 * 占位符
    - "vip.xiaonuo.**.modular.**.entity"
  mappers:
    - "vip.xiaonuo.**.mapper"
    - "classpath:vip/xiaonuo/**/mapping/*.xml"
  typeHandlers:
    - "vip.xiaonuo.common.handler"
  configuration: #扩展配置（要与 FlexConfiguration 类的属性一一对应）
    cacheEnabled: false
    mapperVerifyEnabled: false #如果为 true，则要求所有 mapper 有 @Mapper 主解
    mapUnderscoreToCamelCase: true
  #  globalConfig:
  #    banner: false
  #    metaObjectHandler: "vip.xiaonuo.core.config.CustomMetaObjectHandler"
  #    dbConfig:
  #      idType: ASSIGN_ID
  #      logicDeleteField: DELETE_FLAG
  #      logicDeleteValue: DELETED
  #      logicNotDeleteValue: NOT_DELETE
  globalConfig: #全局配置（要与 FlexGlobalConfig 类的属性一一对应）//只是示例，别照抄
    printBanner: false
    cacheEnabled: false #mybatis 二级缓存
    logEnabled: true #是否打印sql
    keyConfig:
      keyType: "Generator"
      value: "snowFlakeId"

#########################################
# easy-trans configuration
#########################################
easy-trans:
  enable-cloud: false #关闭微服务支持
  enable-global: true #开启全局拦截

#########################################
# knife4j configuration（暂时无用）
#########################################
knife4j.enable: true
knife4j.production: false
knife4j.basic.enable: true
knife4j.basic.username: admin
knife4j.basic.password: 123456
knife4j.setting.enableOpenApi: false
knife4j.setting.enableSwaggerModels: false
knife4j.setting.enableFooter: false
knife4j.setting.enableFooterCustom: true
knife4j.setting.footerCustomContent: Apache License 2.0 | Copyright 2022-[SNOWY](https://www.xiaonuo.vip)

#########################################
# sa-token configuration
#########################################
sa-token.token-name: token
sa-token.timeout: 6400
sa-token.active-timeout: 3200
sa-token.is-concurrent: true
sa-token.is-share: false
sa-token.max-login-count: -1
sa-token.token-style: random-32
sa-token.is-log: false
sa-token.is-print: false

#########################################
# easy poi configuration
#########################################
easy.poi.base.isDev: false

#########################################
# snowy configuration
#########################################

# common configuration
snowy.config.common.front-url: http://***************:81
#snowy.config.common.backend-url: http://***************:83
snowy.config.common.backend-url: http://***************:83
