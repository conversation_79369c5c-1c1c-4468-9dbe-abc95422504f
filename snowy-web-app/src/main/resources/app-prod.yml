#########################################
# Involves the configuration of the environment
#########################################

#########################################
# redis configuration
#########################################
snowy.redis.db: 6
snowy.redis.server: 127.0.0.1:6379
snowy.redis.password: 6zQ8jwDIsC
snowy.redis.timeout: 10s

#########################################
# sa-token redis configuration
#########################################
# sa-token redis configuration
sa-token.redis.db: ${snowy.redis.db}
sa-token.redis.server: ${snowy.redis.server}
sa-token.redis.password: ${snowy.redis.password}
sa-token.redis.timeout: ${snowy.redis.timeout}

#########################################
# datasource configuration
#########################################
snowy.datasource.dynamic.type: "com.zaxxer.hikari.HikariDataSource"
snowy.datasource.dynamic.strict: true

# mysql
snowy.datasource.dynamic.master.driverClassName: com.mysql.cj.jdbc.Driver
snowy.datasource.dynamic.master.jdbcUrl: **************************************************************************************************************************************************************************
snowy.datasource.dynamic.master.username: xcfdc
snowy.datasource.dynamic.master.password: kmHkSdnsknLw8DHa

# oracle
#snowy.datasource.dynamic.master.driver-class-name=oracle.jdbc.driver.OracleDriver
#snowy.datasource.dynamic.master.url=***********************************************************
#snowy.datasource.dynamic.master.username=SNOWY
#snowy.datasource.dynamic.master.password=12345678

# dm database
#snowy.datasource.dynamic.master.driver-class-name=dm.jdbc.driver.DmDriver
#snowy.datasource.dynamic.master.url=jdbc:dm://localhost:5236/SYSDBA
#snowy.datasource.dynamic.master.username=SYSDBA
#snowy.datasource.dynamic.master.password=SYSDBA

# kingbase database
#snowy.datasource.dynamic.master.driverClassName=com.kingbase8.Driver
#snowy.datasource.dynamic.master.url=**************************************
#snowy.datasource.dynamic.master.username=SYSTEM
#snowy.datasource.dynamic.master.password=123456
#snowy.datasource.dynamic.strict=true

#########################################
# snowy configuration
#########################################

# common configuration
snowy.config.common.front-url: http://************
snowy.config.common.backend-url: http://************/prod-api