{"name": "snowy-admin-web", "version": "3.0.0", "private": true, "description": "小诺团队旗下Snowy前端，基于Antdv3.2+Vue3.2+Vite2.8", "repository": {"type": "git", "url": "https://www.gitee.com/xiaonuobase/snowy"}, "license": "Apache-2.0", "author": "<PERSON><PERSON><PERSON><PERSON>", "scripts": {"serve": "vite --host 0.0.0.0", "dev": "vite --mode development --host", "preview": "vite preview", "build": "vite build --mode production", "prod": "vite  --mode production", "fix-memory-limit": "cross-env LIMIT=8048 increase-memory-limit"}, "dependencies": {"@amap/amap-jsapi-loader": "1.0.1", "@ant-design/colors": "7.0.0", "@ant-design/icons-vue": "7.0.1", "@antv/g2plot": "2.4.31", "@chenfengyuan/vue-qrcode": "2.0.0", "@highlightjs/vue-plugin": "2.1.0", "@tinymce/tinymce-vue": "5.1.1", "@vue-office/docx": "1.6.0", "@vue-office/excel": "1.7.1", "@vue-office/pdf": "1.6.4", "ant-design-vue": "4.1.2", "axios": "1.6.2", "cropperjs": "1.6.1", "dayjs": "1.11.10", "echarts": "5.4.3", "echarts-stat": "1.2.0", "enquire.js": "2.1.6", "event-source-polyfill": "1.0.31", "fuse.js": "7.0.0", "highlight.js": "11.9.0", "hotkeys-js": "3.12.2", "js-pinyin": "0.2.5", "lodash-es": "4.17.21", "nprogress": "0.2.0", "pinia": "2.1.7", "print-js": "^1.6.0", "qs": "6.11.2", "screenfull": "6.0.2", "sm-crypto": "0.3.13", "snowflake-id": "1.1.0", "snowy-im": "^1.0.5", "sortablejs": "1.15.1", "tinymce": "6.8.1", "vue": "3.4.21", "vue-cropper": "1.1.1", "vue-i18n": "9.8.0", "vue-router": "4.3.0", "vue3-colorpicker": "2.3.0", "vue3-tree-org": "4.2.2", "vuedraggable-es": "4.1.1"}, "devDependencies": {"@babel/eslint-parser": "7.19.1", "@vitejs/plugin-legacy": "5.2.0", "@vitejs/plugin-vue": "4.5.2", "@vitejs/plugin-vue-jsx": "3.1.0", "@vue/compiler-sfc": "3.3.10", "@vue/eslint-config-standard": "8.0.1", "antd-less-to-css-variable": "1.0.5", "autoprefixer": "10.4.16", "eslint": "8.55.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-prettier": "5.0.1", "eslint-plugin-vue": "9.7.0", "less": "4.1.3", "postcss": "8.4.32", "prettier": "3.1.0", "rollup-plugin-visualizer": "5.10.0", "sass": "^1.77.8", "tailwindcss": "3.3.6", "typescript": "5.3.3", "unplugin-auto-import": "0.17.2", "unplugin-vue-components": "0.26.0", "vite": "5.1.6", "vite-plugin-compression": "0.5.1", "vite-plugin-vue-setup-extend": "0.4.0", "vue-eslint-parser": "9.3.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}