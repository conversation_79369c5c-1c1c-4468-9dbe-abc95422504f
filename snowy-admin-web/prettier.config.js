/**
 *  Copyright [2022] [https://www.xiaonuo.vip]
 *	Snowy采用APACHE LICENSE 2.0开源协议，您在使用过程中，需要注意以下几点：
 *	1.请不要删除和修改根目录下的LICENSE文件。
 *	2.请不要删除和修改Snowy源码头部的版权声明。
 *	3.本项目代码可免费商业使用，商业使用请保留源码和相关描述文件的项目出处，作者声明等。
 *	4.分发源码时候，请注明软件出处 https://www.xiaonuo.vip
 *	5.不可二次分发开源参与同类竞品，如有想法可联系团队******************商议合作。
 *	6.若您的项目无法满足以上几点，需要更多功能代码，获取Snowy商业授权许可，请在官网购买授权，地址为 https://www.xiaonuo.vip
 */
module.exports = {
	// 一行最多 180 字符
	printWidth: 120,
	// 使用 2 个tab缩进
	tabWidth: 2,
	// 使用tab符缩进，false为空格缩进
	useTabs: true,
	// 行尾需要分号
	semi: false,
	// 使用单引号
	singleQuote: true,
	// 对象的 key 仅在必要时用引号
	quoteProps: 'as-needed',
	// jsx 不使用单引号，而使用双引号
	jsxSingleQuote: false,
	// 末尾不需要逗号
	trailingComma: 'none',
	// 大括号内的首尾需要空格
	bracketSpacing: true,
	// jsx 标签的反尖括号不需要换行
	jsxBracketSameLine: true,
	// 箭头函数，只有一个参数的时候，也需要括号
	arrowParens: 'always',
	// 根据显示样式决定 html 要不要折行
	htmlWhitespaceSensitivity: 'css',
	// 换行符使用 lf
	endOfLine: 'lf',
	// 缩进js跟css
	vueIndentScriptAndStyle: true
}
