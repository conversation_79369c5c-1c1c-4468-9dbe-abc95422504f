// vite.config.mjs
import { resolve } from "path";
import { defineConfig, loadEnv } from "file:///E:/works/%E6%96%B0%E5%9F%8E/snowy/snowy-admin-web/node_modules/vite/dist/node/index.js";
import vue from "file:///E:/works/%E6%96%B0%E5%9F%8E/snowy/snowy-admin-web/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import Components from "file:///E:/works/%E6%96%B0%E5%9F%8E/snowy/snowy-admin-web/node_modules/unplugin-vue-components/dist/vite.js";
import VueJSX from "file:///E:/works/%E6%96%B0%E5%9F%8E/snowy/snowy-admin-web/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import AutoImport from "file:///E:/works/%E6%96%B0%E5%9F%8E/snowy/snowy-admin-web/node_modules/unplugin-auto-import/dist/vite.js";
import vueSetupExtend from "file:///E:/works/%E6%96%B0%E5%9F%8E/snowy/snowy-admin-web/node_modules/vite-plugin-vue-setup-extend/dist/index.mjs";
import { visualizer } from "file:///E:/works/%E6%96%B0%E5%9F%8E/snowy/snowy-admin-web/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import Less2CssVariablePlugin from "file:///E:/works/%E6%96%B0%E5%9F%8E/snowy/snowy-admin-web/node_modules/antd-less-to-css-variable/dist/index.js";
import viteCompression from "file:///E:/works/%E6%96%B0%E5%9F%8E/snowy/snowy-admin-web/node_modules/vite-plugin-compression/dist/index.mjs";
import { theme } from "file:///E:/works/%E6%96%B0%E5%9F%8E/snowy/snowy-admin-web/node_modules/ant-design-vue/lib/index.js";
import convertLegacyToken from "file:///E:/works/%E6%96%B0%E5%9F%8E/snowy/snowy-admin-web/node_modules/ant-design-vue/lib/theme/convertLegacyToken.js";
var __vite_injected_original_dirname = "E:\\works\\\u65B0\u57CE\\snowy\\snowy-admin-web";
var { defaultAlgorithm, defaultSeed } = theme;
var mapToken = defaultAlgorithm(defaultSeed);
var v3Token = convertLegacyToken.default(mapToken);
var r = (...args) => resolve(__vite_injected_original_dirname, ".", ...args);
var vite_config_default = defineConfig(({ command, mode }) => {
  const envConfig = loadEnv(mode, "./");
  const alias = {
    "~": `${resolve(__vite_injected_original_dirname, "./")}`,
    "@/": `${resolve(__vite_injected_original_dirname, "src")}/`
  };
  return {
    server: {
      port: envConfig.VITE_PORT,
      proxy: {
        "/api": {
          target: envConfig.VITE_API_BASEURL,
          ws: false,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, "")
        }
      }
    },
    resolve: {
      alias
    },
    // 解决警告You are running the esm-bundler build of vue-i18n.
    define: {
      __VUE_I18N_FULL_INSTALL__: true,
      __VUE_I18N_LEGACY_API__: true,
      __VUE_I18N_PROD_DEVTOOLS__: true,
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: true
    },
    build: {
      // sourcemap: true,
      manifest: true,
      brotliSize: false,
      rollupOptions: {
        output: {
          manualChunks: {
            echarts: ["echarts"],
            "ant-design-vue": ["ant-design-vue"],
            vue: ["vue", "vue-router", "pinia", "vue-i18n"]
          }
        }
      },
      chunkSizeWarningLimit: 1e3
    },
    plugins: [
      vue({
        script: {
          refTransform: true
        }
      }),
      viteCompression(),
      vueSetupExtend(),
      VueJSX(),
      AutoImport({
        imports: ["vue"],
        dirs: ["./src/utils/permission"],
        dts: r("src/auto-imports.d.ts")
      }),
      // 组件按需引入
      Components(
        {
          dirs: [r("src/components")],
          dts: false,
          resolvers: []
        },
        {
          dirs: [r("src/components/HomeCard")],
          dts: false,
          resolvers: []
        }
      ),
      visualizer()
    ],
    css: {
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
          plugins: [
            new Less2CssVariablePlugin({
              // TODO：有必要用的情况下，是否需要传入 variables，可能会造成重复引用
              variables: { ...v3Token }
            })
          ],
          modifyVars: v3Token
        }
      }
    },
    optimizeDeps: {}
  };
});
export {
  vite_config_default as default,
  r
};
//# sourceMappingURL=data:application/json;base64,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
