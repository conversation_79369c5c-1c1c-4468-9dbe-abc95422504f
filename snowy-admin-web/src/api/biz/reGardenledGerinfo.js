import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/regardenledgerinfo/` + url, ...arg)

/**
 * 园区房屋台账管理Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/28 14:34
 **/
export default {
	// 获取园区房屋管理分页
	reGardenHousePage(data) {
		return request('page', data, 'get')
	},
	// 提交园区房屋管理表单 edit为true时为编辑，默认为新增
	reGardenHouseSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除园区房屋管理
	reGardenHouseDelete(data) {
		return request('delete', data)
	},
	// 根据房屋ID获取园区房屋台账信息详情及其附属信息
	reGardenledgerInfoID(data) {
		return request('detailByHouseId', data, 'get')
	},
	// 添加园区房屋台账信息详情及其附属信息
	AddreGardenledger(data) {
		return request('AddDetail', data)
	},

	// 将房屋台账所有状态重置 - 退房
	resetGardenledger(data) {
		return request('restore', data)
	},

	// 获取园区房屋台账信息详情及其附属信息
	getGardenledgerInfo(data) {
		return request('detailWithSub', data, 'get')
	},
	// 更新园区房屋台账信息详情及其附属信息
	uploadGardenledgerInfo(data) {
		return request('UpdateDetail', data, 'post')
	},
	// 续租(新增台账,其他台账变为历史)
	reletGardenledgerInfo(data) {
		return request('relet', data)
	}
}
