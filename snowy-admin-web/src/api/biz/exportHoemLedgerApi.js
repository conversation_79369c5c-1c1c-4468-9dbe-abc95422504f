import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/rehouse/` + url, ...arg)


/**
 * 厂区台账信息Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/09/05 09:00
 **/
export default {
	// 导出住宅台账
	exportHoemLedger(data) {
		return request('export', data, 'get', { responseType: 'blob' })
	},
	// 导出储物间台账
	exportStoreRoomLedger(data) {
		return request('exportStoreRoom', data, 'get', { responseType: 'blob' })
	},
	exportLedegerList(data) {
		return request('exportTax', data, 'get', { responseType: 'blob' })
	},
	
}