import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/repark/` + url, ...arg)

/**
 * 车位管理Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
export default {
	// 获取车位管理分页
	reParkPage(data) {
		return request('page', data, 'get')
	},
	// 提交车位管理表单 edit为true时为编辑，默认为新增
	reParkSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除车位管理
	reParkDelete(data) {
		return request('delete', data)
	},
	// 获取车位管理详情
	reParkDetail(data) {
		return request('detail', data, 'get')
	},
	// 导出模板
	exportTemplate(data) {
		return request('export', data, 'get', { responseType: 'blob' })
	},
	// 通过excel导入车位信息
	importRePark(data) {
		return request('import', data)
	},
	// 退车位
	reParkRefund(data) {
		return request('refund', data)
	},
	// 导入车位台账信息
	importPark(data) {
		return request('importLeder', data)
	},
}
