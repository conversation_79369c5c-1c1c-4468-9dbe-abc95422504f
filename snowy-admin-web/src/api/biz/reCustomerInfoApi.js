import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/recustomerinfo/` + url, ...arg)

/**
 * 客户信息Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
export default {
	// 获取客户信息分页
	reCustomerInfoPage(data) {
		return request('page', data, 'get')
	},
	// 提交客户信息表单 edit为true时为编辑，默认为新增
	reCustomerInfoSubmitForm(data, edit = false,msg) {
		return request(edit ? 'edit' : 'add', data,'post',{
			isMessage:msg
		})
	},
	// 删除客户信息
	reCustomerInfoDelete(data,status,msg) {
		return request('delete', data,'post',{
			isMessage:msg
		})
	},
	// 获取客户信息详情
	reCustomerInfoDetail(data) {
		return request('detail', data, 'get')
	}
}
