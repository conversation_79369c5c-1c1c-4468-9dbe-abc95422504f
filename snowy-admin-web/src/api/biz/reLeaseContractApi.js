import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/releasecontract/` + url, ...arg)

/**
 * 租赁签约Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
export default {
	// 获取租赁签约分页
	reLeaseContractPage(data) {
		return request('page', data, 'get')
	},
	// 提交租赁签约表单 edit为true时为编辑，默认为新增
	reLeaseContractSubmitForm(data, edit = false,msg) {
		return request(edit ? 'edit' : 'add', data,'post',{
			isMessage:msg
		})
	},
	// 删除租赁签约
	reLeaseContractDelete(data) {
		return request('delete', data)
	},
	// 获取租赁签约详情
	reLeaseContractDetail(data) {
		return request('detail', data, 'get')
	},
	// 商业租赁续签
	renewLease(data) {
		return request('renewal', data, 'post')
	},
	// 检查续签重复性
	checkRenewalDuplicate(data) {
		return request('checkRenewalDuplicate', data, 'post')
	}
}
