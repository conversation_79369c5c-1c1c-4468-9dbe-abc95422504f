import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/regardenotherinfo/` + url, ...arg)

/**
 * 园区其他信息Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
export default {
	// 获取园区其他信息分页
	reGardenOtherInfoPage(data) {
		return request('page', data, 'get')
	},
	// 提交园区其他信息表单 edit为true时为编辑，默认为新增
	reGardenOtherInfoSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除园区其他信息
	reGardenOtherInfoDelete(data) {
		return request('delete', data)
	},
	// 获取园区其他信息详情
	reGardenOtherInfoDetail(data) {
		return request('detail', data, 'get')
	}
}
