import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/rehouse/` + url, ...arg)
const request2 = (url, ...arg) => baseRequest(url, ...arg)

/**
 * 房屋管理Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
export default {
	// 获取房屋管理分页
	reHousePage(data) {
		return request('page', data, 'get')
	},
	// 通过楼栋获取房屋管理列表
	reHouseList(data) {
		return request('listByBuild', data, 'get')
	},
	// 提交房屋管理表单 edit为true时为编辑，默认为新增
	reHouseSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除房屋管理
	reHouseDelete(data) {
		return request('delete', data)
	},
	// 获取房屋管理详情
	reHouseDetail(data) {
		return request('detail', data, 'get')
	},
	// 导入房屋管理信息
	importReHouse(data) {
		return request('import', data)
	},
	// 导入房屋台账信息
	importImportLedger(data) {
		return request('importLedger', data)
	},

	// 导出住宅台账导入模板
	exportTenplate(data) {
		return request(`exportTemplate/${data.houseType}`, data, 'get', { responseType: 'blob' })
	},

	// 导出住宅台账导入模板
	exportReHouse(data) {
		return request2(`/dev/file/download?id=1912436805868773376`, data, 'get', { responseType: 'blob' })
	},

	// 导出商业台账导入模板
	exportbusiness(data) {
		return request2(`/dev/file/download?id=1897829790462783488`, data, 'get', { responseType: 'blob' })
	},

	// 导出商业租赁台账导入模板
	exportLeasbusiness(data) {
		return request2(`/dev/file/download?id=1897167283075198976`, data, 'get', { responseType: 'blob' })
	},

	// 导出车位台账导入模板
	exportpark(data) {
		return request2(`/dev/file/download?id=1882006521004515328`, data, 'get', { responseType: 'blob' })
	},

	// 导出储藏间台账导入模板
	exportlockerroom(data) {
		return request2(`/dev/file/download?id=1882006537081282560`, data, 'get', { responseType: 'blob' })
	},

	checkOut(data, edit = false) {
		return request('checkout', data)
	}
}
