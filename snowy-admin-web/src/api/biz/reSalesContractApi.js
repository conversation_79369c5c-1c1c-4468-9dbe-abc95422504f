import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/resalescontract/` + url, ...arg)

/**
 * 销售签约Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
export default {
	// 获取销售签约分页
	reSalesContractPage(data) {
		return request('page', data, 'get')
	},
	// 提交销售签约表单 edit为true时为编辑，默认为新增
	reSalesContractSubmitForm(data, edit = false,msg) {
		return request(edit ? 'edit' : 'add', data,'post',{
			isMessage:msg
		})
	},
	// 删除销售签约
	reSalesContractDelete(data) {
		return request('delete', data)
	},
	// 获取销售签约详情
	reSalesContractDetail(data) {
		return request('detail', data, 'get')
	}
}
