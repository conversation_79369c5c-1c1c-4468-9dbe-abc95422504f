import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/reparkledgerinfo/` + url, ...arg)

/**
 * 车位台账信息Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
export default {
	// 获取车位台账信息分页
	reParkLedgerInfoPage(data) {
		return request('page', data, 'get')
	},
	// 提交车位台账信息表单 edit为true时为编辑，默认为新增
	reParkLedgerInfoSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除车位台账信息
	reParkLedgerInfoDelete(data) {
		return request('delete', data)
	},
	// 获取车位台账信息详情
	reParkLedgerInfoDetail(data) {
		return request('detail', data, 'get')
	},

	// 提交车位台账信息表单 edit为true时为编辑，默认为新增
	addSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'addBySaleControl', data)
	},

	// 通过销控查询车位台账详情
	getDetail(data,msg) {
		return request('detailBySaleControl', data, 'get',{
			isMessage: msg
		})
	}
	
}
