import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/regardenhouse/` + url, ...arg)

/**
 * 园区房屋管理Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
export default {
	// 获取园区房屋管理分页
	reGardenHousePage(data) {
		return request('page', data, 'get')
	},
	// 提交园区房屋管理表单 edit为true时为编辑，默认为新增
	reGardenHouseSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除园区房屋管理
	reGardenHouseDelete(data) {
		return request('delete', data)
	},
	// 获取园区房屋管理详情
	reGardenHouseDetail(data) {
		return request('detail', data, 'get')
	},
	// 获取园区房屋已售未售面积
	reGardenHouseArea(data) {
		return request('soldAndUnsoldArea', data, 'get')
	},
	// 下载园区房源导入模板
	downloadReGardenHouse(data) {
		return request('downloadTemplate', data, 'get', { responseType: 'blob' })
	},
	// 园区房源导入
	importReGardenHouse(data) {
		return request('import', data)
	}
}
