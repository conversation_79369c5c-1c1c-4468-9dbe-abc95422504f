import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/reledgerinfo/` + url, ...arg)
const request2 = (url, ...arg) => baseRequest(url, ...arg)

/**
 * 房屋台账信息Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:34
 **/
export default {
	// 获取房屋台账信息分页
	reLedgerInfoPage(data) {
		return request('page', data, 'get')
	},
	// 提交房屋台账信息表单 edit为true时为编辑，默认为新增
	reLedgerInfoSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'addBySaleControl', data)
	},
	// 删除房屋台账信息
	reLedgerInfoDelete(data) {
		return request('delete', data)
	},
	// 获取房屋台账信息详情
	reLedgerInfoDetail(data) {
		return request('queryEntity', data, 'get')
	},
	// 通过房屋id查询台账信息
	reLedgerInfoByHouseId(data, msg) {
		return request('queryEntityByHouseId', data, 'get', {
			isMessage: msg
		})
	},
	// 导入商业销售台账信息
	importBussiness(data) {
		return request('bussiness/importLeder', data)
	},
	// 导入商业租赁台账信息
	importLeasBussiness(data) {
		return request('bussiness/import', data)
	},
	// 导入储藏间台账信息
	importStoreroom(data) {
		return request('storeroom/importLeder', data)
	}
}
