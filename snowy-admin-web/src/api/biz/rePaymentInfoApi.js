import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/repaymentinfo/` + url, ...arg)

/**
 * 交款信息Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
export default {
	// 获取交款信息分页
	rePaymentInfoPage(data) {
		return request('page', data, 'get')
	},
	// 提交交款信息表单 edit为true时为编辑，默认为新增
	rePaymentInfoSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除交款信息
	rePaymentInfoDelete(data) {
		return request('delete', data)
	},
	// 获取交款信息详情
	rePaymentInfoDetail(data) {
		return request('detail', data, 'get')
	}

}
