import { baseRequest } from '@/utils/request'

const request = (url, ...arg) => baseRequest(`/biz/reparkarea/` + url, ...arg)

/**
 * 车位区域管理Api接口管理器
 *
 * <AUTHOR>
 * @date  2024/08/17 14:35
 **/
export default {
	// 获取车位区域管理分页
	reParkAreaPage(data) {
		return request('page', data, 'get')
	},
	// 提交车位区域管理表单 edit为true时为编辑，默认为新增
	reParkAreaSubmitForm(data, edit = false) {
		return request(edit ? 'edit' : 'add', data)
	},
	// 删除车位区域管理
	reParkAreaDelete(data) {
		return request('delete', data)
	},
	// 获取车位区域管理详情
	reParkAreaDetail(data) {
		return request('detail', data, 'get')
	}
}
