@font-face {
  font-family: "iconfont"; /* Project id 4655834 */
  src: url('iconfont.woff2?t=1725090863117') format('woff2'),
       url('iconfont.woff?t=1725090863117') format('woff'),
       url('iconfont.ttf?t=1725090863117') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-tui:before {
  content: "\e659";
}

.icon-dingwei1:before {
  content: "\e627";
}

.icon-dingwei:before {
  content: "\e636";
}

