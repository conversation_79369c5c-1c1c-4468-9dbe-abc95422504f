<template>
	<xn-form-container
		title="房屋详情"
		:width="1000"
		:visible="visible"
		:destroy-on-close="true"
		:footer-style="{ textAlign: 'right' }"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="horizontal">
			<a-row :gutter="24">
				<a-col :span="5">
					<a-form-item label="楼号：" name="code">
						<span>{{ formData.code }}</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="房屋编号：" name="houseNum">
						<span>{{ formData.houseNum }}</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="楼层：" name="floor">
						<span>{{ formData.floor }}</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="建筑面积：" name="buildArea">
						<span>{{ formData.buildArea }}㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="4">
					<a-form-item label="房屋状态" name="actualBuildArea">
						{{ $TOOL.dictTypeData('park_house_type', formData.status) }}
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<a-tabs v-model:activeKey="activeKey" type="card">
			<a-tab-pane key="1" tab="客户信息">
				<a-form ref="formRef2" layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="24">
							<a-form-item label="入住企业：" name="company">
								<span>{{ detailData.customer.company || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="企业法人：" name="contactPerson">
								<span>{{ detailData.customer.legalPerson || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="身份证号：" name="idCard">
								<span>{{ detailData.customer.idCard || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="联系电话：" name="phone">
								<span>{{ detailData.customer.phone || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="企业联络人：" name="contactPerson">
								<span>{{ detailData.customer.contactPerson || '-' }}</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
			</a-tab-pane>
			<a-tab-pane key="2" tab="签约信息">
				<a-form ref="formRef2" layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="8">
							<a-form-item label="物业类别：" name="propertyType">
								<span>{{ detailData.contract.propertyType || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="物业现状：" name="propertyStatus">
								<span>{{ detailData.contract.propertyStatus || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8"></a-col>
						<a-col :span="8">
							<a-form-item label="租赁合同：" name="leaseContract">
								{{ $TOOL.dictTypeData('lease_agreement', detailData.contract.leaseContract) }}
							</a-form-item>
						</a-col>
						<a-col :span="16">
							<a-form-item label="入区协议：" name="entryAgreement">
								{{ $TOOL.dictTypeData('entry_agreement', detailData.contract.entryAgreement) }}
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="租赁价格：" name="leasePrice">
								<span>{{ detailData.contract.leasePrice || '-' }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="连廊租金：" name="corridorRent">
								<span>{{ detailData.contract.corridorRent || '-' }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="月租金：" name="monthlyRent">
								<span>{{ detailData.contract.monthlyRent || '-' }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="租赁保证金：" name="leaseDeposit">
								<span>{{ detailData.contract.leaseDeposit || '-' }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="租赁起始日期：" name="leaseStartTime">
								<span>{{ detailData.contract.leaseStartTime || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="租赁截止日期：" name="leaseEndTime">
								<span>{{ detailData.contract.leaseEndTime || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="租赁计算日期：" name="leaseCalculationDate">
								<span>{{ detailData.contract.leaseCalculationDate || '-' }}</span>
							</a-form-item>
						</a-col>

						<a-col :span="8">
							<a-form-item label="租期：" name="leaseTerm">
								<span>{{ detailData.contract.leaseTerm || '-' }}月</span>
							</a-form-item>
						</a-col>
						<a-col :span="16">
							<a-form-item label="免租期：" name="rentFreePeriod">
								<span>{{ detailData.contract.rentFreePeriod || '-' }}月</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="应缴租金：" name="rentPayable">
								<span>{{ detailData.contract.rentPayable || '-' }}元</span>
							</a-form-item>
						</a-col>

						<a-col :span="8">
							<a-form-item label="已缴租金：" name="rentPaid">
								<span>{{ detailData.contract.rentPaid || '-' }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="欠缴租金：" name="rentArrears">
								<span>{{ detailData.contract.rentArrears || '-' }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="备注：" name="remark">
								<span>{{ detailData.contract.remark || '-' }}</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
			</a-tab-pane>
			<a-tab-pane key="3" tab="其他信息">
				<a-form ref="formRef2" layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="24">
							<a-form-item label="企业优惠政策：" name="companyPolicy">
								<span>{{ detailData.other.companyPolicy || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="经营产品：" name="businessProduct">
								<span>{{ detailData.other.businessProduct || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="投资额：" name="investmentAmount">
								<span>{{ detailData.other.investmentAmount || '-' }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="员工人数：" name="staffNumber">
								<span>{{ detailData.other.staffNumber || '-' }}人</span>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="年营业额：" name="annualTurnover">
								<span>{{ detailData.other.annualTurnover || '-' }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="配套需求：" name="supportingRequirements">
								<span>{{ detailData.other.supportingRequirements || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="存在问题：" name="existingProblems">
								<span>{{ detailData.other.existingProblems || '-' }}</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
			</a-tab-pane>
			<a-tab-pane key="4" tab="历史台账">
				<a-table :dataSource="detailData.history" :columns="columns">
					<template #bodyCell="{ column, record }">
						<template v-if="column.key === 'contractType'">
							{{ $TOOL.dictTypeData('contract_type', record.contractType) }}
						</template>
						<template v-else-if="column.key === 'action'">
							<a style="cursor: pointer; color: #1677ff" @click="detailRef.onOpen({ ...formData.value, id: record.id })"
								>详情</a
							>
						</template>
					</template>
				</a-table>
			</a-tab-pane>
		</a-tabs>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
		</template>
	</xn-form-container>
	<Detail ref="detailRef"></Detail>
</template>

<script setup name="reHouseForm">
	import Detail from '../projectLedger/factoryLedger/detail.vue'
	import { useMenuStore } from '@/store/menu'
	const menuStore = useMenuStore()
	import { message } from 'ant-design-vue'
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import reHouseApi from '@/api/biz/reHouseApi'
	import reGardenledGerinfo from '@/api/biz/reGardenledGerinfo'
	import tool from '@/utils/tool'
	// 房屋状态
	const houseStatus = tool.dictList('park_house_type')
	// 园区入区协议
	const entryAgreement = tool.dictList('entry_agreement')
	// 园区租赁合同
	const leaseAgreement = tool.dictList('lease_agreement')
	// 抽屉状态
	const visible = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	const detailRef = ref()
	// 表单数据
	const formData = ref({})
	const detailData = ref({
		customer: {},
		contract: {},
		other: {}
	})
	const submitLoading = ref(false)
	const activeKey = ref('1')
	const dataSource = ref([
		{
			key: '1',
			name: '胡彦斌',
			age: 32,
			address: '13303738899',
			file: '1111111'
		},
		{
			key: '2',
			name: '胡彦祖',
			age: 42,
			address: '13303738899',
			file: '1111111'
		}
	])
	const columns = ref([
		{
			title: '入住企业',
			dataIndex: 'enterprise',
			key: 'enterprise'
		},
		{
			title: '客户姓名',
			dataIndex: 'name',
			key: 'name'
		},
		{
			title: '客户电话',
			dataIndex: 'phone',
			key: 'phone'
		},
		{
			title: '签约类型',
			dataIndex: 'contractType',
			key: 'contractType'
		},
		{
			title: '签约日期',
			dataIndex: 'contractTime',
			key: 'contractTime'
		},
		{
			title: '操作',
			key: 'action'
		}
	])

	// 打开抽屉
	const onOpen = (record) => {
		console.log(record, '--------')
		//有台账信息
		visible.value = true
		if (record) {
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)
			formData.value.projectId = menuStore.projectObj.id
			getDetail({ id: record.id })
		}
	}
	const getDetail = (queryData) => {
		reGardenledGerinfo.reGardenledgerInfoID(queryData).then((res) => {
			if (!res.customer) {
				res.customer = {}
			}
			if (!res.contract) {
				res.contract = {}
			}
			if (!res.other) {
				res.other = {}
			}
			detailData.value = Object.assign({}, res)
		})
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		visible.value = false
	}
	// 默认要校验的
	const formRules = {}
	// 验证并提交数据
	const onSubmit = () => {
		formRef.value.validate().then(() => {
			submitLoading.value = true
			const formDataParam = cloneDeep(formData.value)
			reHouseApi
				.reHouseSubmitForm(formDataParam, formDataParam.id)
				.then(() => {
					onClose()
					emit('successful')
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}
	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
