<template>
	<xn-form-container
		:title="formData.id ? '编辑房屋管理' : '增加房屋管理'"
		:width="600"
		:visible="visible"
		:destroy-on-close="true"
		:footer-style="{ textAlign: 'right' }"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="楼号：" name="code">
						<a-input v-model:value="formData.code" placeholder="请输入楼号" allow-clear disabled />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="层数：" name="floor">
						<a-input-number
							v-model:value="formData.floor"
							:min="1"
							placeholder="请输入楼层"
							allow-clear
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="房号：" name="houseNum">
						<a-input-number
							v-model:value="formData.houseNum"
							:min="1"
							placeholder="请输入房号"
							allow-clear
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="房源位置：" name="houseLocation">
						<a-input v-model:value="formData.houseLocation" placeholder="请输入房源位置" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="建筑面积：" name="buildArea">
						<a-input v-model:value="formData.buildArea" placeholder="请输入建筑面积" allow-clear addon-after="㎡" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="连廊面积：" name="corridorArea">
						<a-input v-model:value="formData.corridorArea" placeholder="请输入连廊面积" allow-clear addon-after="㎡" />
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="reHouseForm">
	import { message } from 'ant-design-vue'
	import { useMenuStore } from '@/store/menu'
	const menuStore = useMenuStore()
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import reGardenHouseApi from '@/api/biz/reGardenHouseApi'
	import tool from '@/utils/tool'
	// 房屋朝向
	const houseOrientation = tool.dictList('house_orientation')
	// 房屋户型
	const houseLayout = tool.dictList('house_layout')
	// 房屋类型
	const houseType = tool.dictList('house_type')
	// 房屋状态
	const houseStatus = tool.dictList('house_status')
	// 抽屉状态
	const visible = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	const submitLoading = ref(false)

	// 打开抽屉
	const onOpen = (record) => {
		console.log(record, '--------')

		if (record && record.code) {
			visible.value = true
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)
			formData.value.projectId = menuStore.projectObj.id
			formData.value.buildCode = recordData.code
		} else {
			message.error('请先选择楼栋！')
		}
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		visible.value = false
	}
	// 默认要校验的
	const formRules = {
		floor: [{ required: true, message: '请输入楼层', trigger: 'change' }],
		houseNum: [{ required: true, message: '请输入房号', trigger: 'change' }],
		// houseLocation: [{ required: true, message: '请输入房源位置', trigger: 'change' }],
		buildArea: [{ required: true, message: '请输入建筑面积', trigger: 'change' }],
		// corridorArea: [{ required: true, message: '请输入连廊面积', trigger: 'change' }]
	}
	// 验证并提交数据
	const onSubmit = () => {
		formRef.value.validate().then(() => {
			submitLoading.value = true
			const formDataParam = cloneDeep({ ...formData.value, projectId: menuStore.projectObj.id })
			reGardenHouseApi
				.reGardenHouseSubmitForm(formDataParam, formDataParam.id)
				.then(() => {
					onClose()
					emit('successful', formDataParam)
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}
	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
