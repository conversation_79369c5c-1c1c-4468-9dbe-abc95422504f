<template>
	<a-card :bordered="false">
		<a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
			<a-row :gutter="24">
				<a-col :span="6">
					<a-form-item label="房源类型" name="houseType">
						<a-select v-model:value="searchFormState.houseType" placeholder="请选择类型"
							:options="typeOptions" />
					</a-form-item>
				</a-col>
				<a-col :span="6">
					<a-form-item label="截止日期" name="deadline">
						<a-date-picker value-format="YYYY-MM-DD" style="width: 100%"
							v-model:value="searchFormState.endDate" />
					</a-form-item>
				</a-col>
				<a-col :span="6">
					<a-button type="primary" @click="tableRef.refresh(true)">查询</a-button>
					<a-button style="margin: 0 8px" @click="reset">重置</a-button>
					<!-- v-show="advanced" -->
					<!-- <a @click="toggleAdvanced" style="margin-left: 8px">
                        {{ advanced ? '收起' : '展开' }}
                        <component :is="advanced ? 'up-outlined' : 'down-outlined'"/>
                    </a> -->
				</a-col>
			</a-row>
		</a-form>
		<s-table ref="tableRef" :columns="columns" :data="loadData" :alert="options.alert.show" bordered
			:row-key="(record) => record.id" :tool-config="toolConfig" :row-selection="options.rowSelection">
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'houseType'">
					<span>
						{{ $TOOL.dictTypeData('house_typeAlarm', record.houseType) }}
					</span>
				</template>
				<template v-if="column.dataIndex === 'action'">
					<a-space>
						<a @click="goDetail(record)">详情</a>
					</a-space>
				</template>
			</template>
		</s-table>
	</a-card>
</template>

<script setup name="notice">
import tool from '@/utils/tool'
import { cloneDeep } from 'lodash-es'
import router from '@/router'
import Form from './form.vue'
import Detail from './detail.vue'
import bizNoticeApi from '@/api/biz/bizNoticeApi'
import { useMenuStore } from '@/store/menu'
const menuStore = useMenuStore()
const searchFormState = ref({
})
const searchFormRef = ref()
const tableRef = ref()
const formRef = ref()
const detailRef = ref()
const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
const loading = ref(false)
// 查询区域显示更多控制
const advanced = ref(false)
const typeOptions = tool.dictList('house_typeAlarm')
const toggleAdvanced = () => {
	advanced.value = !advanced.value
}
const columns = [
	{
		title: '房源类型',
		dataIndex: 'houseType',
		align: 'center'
	},
	{
		title: '楼栋',
		dataIndex: 'building',
		align: 'center'
	},
	{
		title: '房号',
		dataIndex: 'houseNumber',
		align: 'center'
	},
	{
		title: '客户姓名',
		dataIndex: 'customerName',
		align: 'center'
	},
	{
		title: '联系方式',
		dataIndex: 'contactInformation',
		align: 'center'
	},
	{
		title: '截止日期',
		dataIndex: 'deadline',
		align: 'center'
	}
]
// 操作栏通过权限判断是否显示
if (hasPerm(['aralm_detail'])) {
	columns.push({
		title: '操作',
		dataIndex: 'action',
		align: 'center',
		width: '200px'
	})
}
const selectedRowKeys = ref([])
// 列表选择配置
const options = {
	// columns数字类型字段加入 needTotal: true 可以勾选自动算账
	alert: {
		show: false,
		clear: () => {
			selectedRowKeys.value = ref([])
		}
	},
	rowSelection: {
		onChange: (selectedRowKey, selectedRows) => {
			selectedRowKeys.value = selectedRowKey
		}
	}
}
const loadData = (parameter) => {
	searchFormState.value.extJson = menuStore.projectObj ? menuStore.projectObj.id : ''
	let searchFormParam = cloneDeep(searchFormState.value)
	return bizNoticeApi.bizNoticePage(Object.assign(parameter, searchFormParam)).then((data) => {
		return data
	})
}
// 重置
const reset = () => {
	searchFormState.value = {}
	searchFormRef.value.resetFields()
	tableRef.value.refresh(true)
}
// 删除
const deleteBizNotice = (record) => {
	let params = [
		{
			id: record.id
		}
	]
	bizNoticeApi.bizNoticeDelete(params).then(() => {
		tableRef.value.refresh(true)
	})
}
// 批量删除
const deleteBatchBizNotice = (params) => {
	bizNoticeApi.bizNoticeDelete(params).then(() => {
		tableRef.value.clearRefreshSelected()
	})
}

const goDetail = (e) => {
	//跳转住宅台账
	if (e.houseType == 1) {
		router.push({
			path: '/biz/reledgerinfo',
			query: { phone: e.contactInformation, name: e.customerName, ledgerId: e.ledgerId }
		})
	}
	//跳转商业台账
	if (e.houseType == 2) {
		router.push({
			path: '/biz/reBusinessLedgerInfo',
			query: { phone: e.contactInformation, name: e.customerName, ledgerId: e.ledgerId }
		})
	}
	//跳转储藏间台账
	if (e.houseType == 3) {
		router.push({
			path: '/biz/reLockerroomLedgerInfo',
			query: { phone: e.contactInformation, name: e.customerName, ledgerId: e.ledgerId }
		})
	}
}
// 修改状态
const editStatus = (record) => {
	loading.value = true
	if (record.status === 'ENABLE') {
		bizNoticeApi
			.bizNoticeDisableStatus(record)
			.then(() => {
				tableRef.value.refresh()
			})
			.finally(() => {
				loading.value = false
			})
	} else {
		bizNoticeApi
			.bizNoticeEnableStatus(record)
			.then(() => {
				tableRef.value.refresh()
			})
			.finally(() => {
				loading.value = false
			})
	}
}
const placeOptions = tool.dictList('BIZ_NOTICE_PLACE')
const statusOptions = tool.dictList('BIZ_NOTICE_STATUS')
</script>
