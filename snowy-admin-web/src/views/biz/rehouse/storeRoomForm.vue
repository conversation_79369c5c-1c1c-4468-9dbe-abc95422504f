<template>
	<xn-form-container
		:title="formData.id ? '编辑储藏间管理' : '增加储藏间管理'"
		:width="600"
		:visible="visible"
		:destroy-on-close="true"
		:footer-style="{ textAlign: 'right' }"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="楼号：" name="code">
						<a-input v-model:value="formData.code" placeholder="请输入楼号" allow-clear disabled />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="楼层：" name="floor">
						<a-input v-model:value="formData.floor" placeholder="请输入楼层" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="室号：" name="houseNumber">
						<a-input v-model:value="formData.houseNumber" placeholder="请输入室号" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="房源位置：" name="houseLocation">
						<a-input v-model:value="formData.houseLocation" placeholder="请输入房源位置" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="建筑面积：" name="actualBuildArea">
						<a-input
							v-model:value="formData.actualBuildArea"
							placeholder="请输入建筑面积"
							allow-clear
							addon-after="㎡"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="套内面积：" name="actualHouseArea">
						<a-input
							v-model:value="formData.actualHouseArea"
							placeholder="请输入套内面积"
							allow-clear
							addon-after="㎡"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="销售总价：" name="totalPrice">
						<a-input v-model:value="formData.totalPrice" placeholder="请输入销售总价" allow-clear addon-after="元" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="销售单价：" name="unitPrice">
						<a-input v-model:value="formData.unitPrice" placeholder="请输入销售单价" allow-clear addon-after="元/㎡" />
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="reHouseForm">
	import { message } from 'ant-design-vue'
	import { useMenuStore } from '@/store/menu'
	const menuStore = useMenuStore()
	import { cloneDeep } from 'lodash-es'
	import { required, rules } from '@/utils/formRules'
	import reHouseApi from '@/api/biz/reHouseApi'
	import tool from '@/utils/tool'
	// 储藏间朝向
	const houseOrientation = tool.dictList('house_orientation')
	// 储藏间户型
	const houseLayout = tool.dictList('house_layout')
	// 储藏间类型
	const houseType = tool.dictList('house_type')
	// 储藏间状态
	const houseStatus = tool.dictList('house_status')
	// 抽屉状态
	const visible = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	const submitLoading = ref(false)

	// 打开抽屉
	const onOpen = (record) => {
		console.log(record, '--------')
		if (record && record.code) {
			visible.value = true
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)
			formData.value.houseType = 3 //  houseType 住宅1、商业2、储藏间3
			formData.value.projectId = menuStore.projectObj.id
			formData.value.buildCode = record.code
		} else {
			message.error('请先选择楼栋！')
		}
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		visible.value = false
	}
	// 默认要校验的
	const formRules = {
		floor: [{ required: true, message: '请输入楼层', trigger: 'change' }],
		houseNumber: [{ required: true, message: '请输入房号', trigger: 'change' }],
		houseLocation: [{ required: true, message: '请输入房源位置', trigger: 'change' }],
		actualBuildArea: [{ required: true, message: '请输入建筑面积', trigger: 'change' }, rules.typeNumberDot],
		actualHouseArea: [{ required: true, message: '请输入套内面积', trigger: 'change' }, rules.typeNumberDot],
		totalPrice: [{ required: true, message: '请输入销售总价', trigger: 'change' }],
		unitPrice: [{ required: true, message: '请输入销售单价', trigger: 'change' }]
	}
	// 验证并提交数据
	const onSubmit = () => {
		formRef.value.validate().then(() => {
			submitLoading.value = true
			const formDataParam = cloneDeep(formData.value)
			reHouseApi
				.reHouseSubmitForm(formDataParam, formDataParam.id)
				.then(() => {
					onClose()
					emit('successful')
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}
	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
