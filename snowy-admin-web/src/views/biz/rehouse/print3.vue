<template>
	<div id="prints">
		<div style="font-size: 12px">{{ currentTime }}</div>
		<div class="title" style="font-weight: bold; text-align: center; line-height: 30px">
			{{ menuStore.projectObj.name }}储藏间签约明细表
		</div>
		<div style="border: 1px solid #000; border-top: 0; margin-top: 20px; height: 110vh; box-sizing: content-box">
			<div style="display: flex; justify-content: space-between; width: 100%; margin-bottom: 10px">
				<div style="width: 43%; height: 1px; background-color: #000"></div>
				<div style="width: 10%; text-align: center; font-size: 18px; margin-top: -10px">客户信息</div>
				<div style="width: 43%; height: 1px; background-color: #000"></div>
			</div>
			<div class="pl10" style="width: 100%; display: flex; justify-content: space-between; padding-bottom: 6px">
				<div class="item-w">
					<span>买受人：</span>
					<span>{{ houseInfo.customerInfo.personInfo.name || '-' }}</span>
				</div>
				<div class="item-w">
					<span>身份证号：</span>
					<span>{{ houseInfo.customerInfo.personInfo.idCard || '-' }}</span>
				</div>
				<div class="item-w">
					<span>电话：</span>
					<span>{{ houseInfo.customerInfo.personInfo.phone || '-' }}</span>
				</div>
			</div>
			<!-- <div
			style="width: 100%; display: flex"
			v-for="(item, index) in carInfo.customerInfo.shareholderList"
			:key="index"
		>
			<div class="item-w" style="width: 6%">共有人：</div>
			<div class="item-w">
				<span>姓名：</span>
				<span>{{ item.name || '-' }}</span>
			</div>
			<div class="item-w">
				<span>身份证号：</span>
				<span>{{ item.idCard || '-' }}</span>
			</div>
			<div class="item-w">
				<span>电话：</span>
				<span>{{ item.phone || '-' }}</span>
			</div>
		</div>
		<div style="width: 100%; display: flex">
			<div class="item-w" style="width: 18.2%">
				<span>地址：</span>
				<span>{{ carInfo.customerInfo.address || '-' }}</span>
			</div>
			<div class="item-w">
				<span>认购时间：</span>
				<span>{{ carInfo.customerInfo.subscribeTime || '-' }}</span>
			</div>
		</div> -->

			<div style="display: flex; justify-content: space-between; align-items: center; width: 100%; margin: 10px 0">
				<div style="width: 43%; height: 1px; background-color: #000"></div>
				<div style="width: 12%; text-align: center; font-size: 18px">储藏间信息</div>
				<div style="width: 43%; height: 1px; background-color: #000"></div>
			</div>

			<div class="pl10" style="width: 100%; display: flex; justify-content: space-between">
				<div class="item-w">
					<span>楼号：</span>
					<span>{{ formData.code }}号楼</span>
				</div>
				<div class="item-w">
					<span>楼层：</span>
					<span>{{ formData.floor }}层</span>
				</div>
				<div class="item-w">
					<span>房号：</span>
					<span>{{ formData.houseNumber }}号</span>
				</div>
			</div>

			<div style="display: flex; justify-content: space-between; align-items: center; width: 100%; margin: 10px 0">
				<div style="width: 43%; height: 1px; background-color: #000"></div>
				<div style="width: 10%; text-align: center; font-size: 18px">签约信息</div>
				<div style="width: 43%; height: 1px; background-color: #000"></div>
			</div>

			<div class="pl10" style="display: flex; width: 100%; flex-wrap: wrap">
				<div class="item-w">
					<span>签约形式：</span>
					<span>销售</span>
				</div>
			</div>
			<div class="pl10" style="display: flex; width: 100%; flex-wrap: wrap">
				<div class="item-w">
					<span>总价：</span>
					<span>{{ formData.totalPrice }}元</span>
				</div>
			</div>
			<div class="pl10" style="width: 100%; display: flex; justify-content: space-between">
				<div class="item-w">
					<span>执行优惠：</span>
					<span>{{ houseInfo.saleSign.discount || '-' }}</span>
					<span class="discountType" v-if="houseInfo.saleSign.discount">{{
						houseInfo.saleSign.discountType === '1' ? '折扣' : '直减'
					}}</span>
				</div>
				<div class="item-w">
					<span>优惠备注：</span>
					<span>{{ houseInfo.saleSign.discountRemark || '-' }}</span>
				</div>
			</div>
			<div class="pl10" style="width: 100%; display: flex; justify-content: space-between">
				<div class="item-w">
					<span>签约总价：</span>
					<span>{{ houseInfo.saleSign.contractPrice || 0 }}元</span>
				</div>
			</div>

			<div style="display: flex; justify-content: space-between; align-items: center; width: 100%; margin: 10px 0">
				<div style="width: 43%; height: 1px; background-color: #000"></div>
				<div style="width: 10%; text-align: center; font-size: 18px">交款信息</div>
				<div style="width: 43%; height: 1px; background-color: #000"></div>
			</div>

			<div class="pl10" style="width: 100%; display: flex; justify-content: space-between">
				<div class="item-w">
					<span>合计交款：</span>
					<span>{{ houseInfo.ledgerInfo.totalPayment || 0 }}元</span>
				</div>
				<div class="item-w">
					<span>欠款金额：</span>
					<span
						>{{
							((houseInfo.saleSign.contractPrice || 0) - (houseInfo.ledgerInfo.totalPayment || 0)).toFixed(2) || 0
						}}元</span
					>
				</div>
				<div class="item-w">
					<span>结清状态：</span>
					<span>{{
						houseInfo.saleSign.contractPrice - houseInfo.ledgerInfo.totalPayment > 0 ? '未结清' : '已结清'
					}}</span>
				</div>
			</div>
			<div
				style="display: flex; justify-content: space-between; align-items: center; width: 100%; margin: 10px 0"
				v-if="houseInfo.paymentInfos.length > 0"
			>
				<div style="width: 43%; height: 1px; background-color: #000"></div>
				<div style="width: 10%; text-align: center; font-size: 18px">交款记录</div>
				<div style="width: 43%; height: 1px; background-color: #000"></div>
			</div>

			<div
				style="width: 100%; display: flex; justify-content: space-between"
				v-for="(item, index) in houseInfo.paymentInfos"
				:key="index"
				class="pl10"
			>
				<div class="item-w">
					<span>交款类型：</span>
					<span>{{ $TOOL.dictTypeData('payment', item.paymentType) }}</span>
				</div>
				<div class="item-w">
					<span>交款金额：</span>
					<span>{{ item.paymentAmount }}元</span>
				</div>
				<div class="item-w">
					<span> 交款时间：</span>
					<span>{{ item.paymentTime }}</span>
				</div>
				<div class="item-w" style="display: flex">
					<span> 财务确认：</span>
					<div class="lines"></div>
				</div>
			</div>

			<div class="pl10" style="width: 100%; display: flex; justify-content: space-between; margin-top: 60px">
				<div class="item-w" style="display: flex">
					<span>客户签字：</span>
					<div class="lines"></div>
				</div>
				<div class="item-w" style="display: flex">
					<span>置业顾问：</span>
					<div class="lines"></div>
				</div>
			</div>
			<div class="pl10" style="width: 100%; display: flex; justify-content: space-between; margin-top: 20px">
				<div class="item-w" style="display: flex">
					<span>台账审核：</span>
					<div class="lines"></div>
				</div>
				<div class="item-w" style="display: flex">
					<span>公司财务：</span>
					<div class="lines"></div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup name="reHouseForm">
	import { message } from 'ant-design-vue'
	import { useMenuStore } from '@/store/menu'
	const menuStore = useMenuStore()
	import { cloneDeep } from 'lodash-es'
	import reLedgerInfoApi from '@/api/biz/reLedgerInfoApi'
	import printJS from 'print-js'
	import dayjs from 'dayjs'
	const currentTime = ref(dayjs().format('YYYY年MM月DD日 HH时mm分'))
	const emit = defineEmits({ successful: null })
	// 表单数据
	const formData = ref({})
	const houseInfo = ref({
		ledgerInfo: {},
		customerInfo: {
			personInfo: {
				name: '',
				idCard: '',
				phone: ''
			}
		},
		saleSign: {
			paymentInfoList: []
		},
		placeSign: {},
		leaseSign: {},
		paymentInfos: [],
		contractInfo: {}
	})
	const activeKey = ref('1')
	// 打开抽屉
	const onOpen = (record) => {
		activeKey.value = '1'
		if (record) {
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)
			formData.value.projectId = menuStore.projectObj.id
			getDetail(record.id)
		}
	}
	const getDetail = (id) => {
		reLedgerInfoApi
			.reLedgerInfoByHouseId({ id: id }, 2)
			.then((res) => {
				if (!res.paymentInfos) {
					res.paymentInfos = []
				}
				if (!res.paymentInfoList) {
					res.paymentInfoList = []
				}
				houseInfo.value = res
				const style = '@page {size: auto;margin:5mm 10mm;};' //打印时去掉眉页眉尾
				setTimeout(() => {
					printJS({
						printable: 'prints', // 标签元素id
						type: 'html',
						header: '', // 标题，可自行添加
						targetStyles: ['*'],
						style,
						font_size: 'llllll'
					})
				}, 500)
			})
			.catch((err) => {
				message.error(err.msg)

			})
	}
	const onPrint = (record) => {
		formData.value = record
		onOpen(record)
		currentTime.value = dayjs().format('YYYY年MM月DD日 HH时mm分')
	}
	// 抛出函数
	defineExpose({
		onOpen,
		onPrint
	})
</script>
<style scoped>
	.discountType {
		font-size: 12px;
		/* color: #409eff; */
		margin-left: 5px;
	}
	.item-w {
		width: 25%;
		margin-top: 16px;
	}
	.title {
		font-size: 26px;
	}

	.lines {
		width: 90px;
		height: 1px;
		background-color: #000000;
		margin-top: 16px;
	}

	* {
		-webkit-print-color-adjust: exact !important;
		-moz-print-color-adjust: exact !important;
		-ms-print-color-adjust: exact !important;
		print-color-adjust: exact !important;
	}

	.pl10 {
		padding-left: 10px;
	}
</style>
