<template>
	<xn-form-container
		title="房屋详情"
		:width="1000"
		:visible="visible"
		:destroy-on-close="true"
		:footer-style="{ textAlign: 'right' }"
		@close="onClose"
	>
		<a-button style="position: fixed; right: 20px; top: 10px" @click="onPrint">打印</a-button>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="horizontal">
			<a-row :gutter="24">
				<a-col :span="5">
					<a-form-item label="楼号：" name="code">
						<span>{{ formData.code }}号楼</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="单元：" name="unit">
						<span>{{ formData.unit }}单元</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="楼层：" name="floor">
						<span>{{ formData.floor }}层</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="房号：" name="houseNumber">
						<span>{{ formData.houseNumber }}号</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="建筑面积：" name="actualBuildArea">
						<span>{{ formData.actualBuildArea || '-' }}㎡</span>
					</a-form-item>
				</a-col>
				<!-- <a-col :span="5">
					<a-form-item label="市场价：" name="marketPrice">
						<span>{{ formData.marketPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="安置价：" name="placementPrice">
						<span>{{ formData.placementPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col> -->
				<a-col :span="4">
					<a-form-item label="房屋状态" name="actualBuildArea">
						{{ $TOOL.dictTypeData('house_status', formData.status) }}
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="销售单价" name="salesUnitPrice">
						<span>{{ formData.salesUnitPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="销售总价" name="salesTotalPrice">
						<span>{{ formData.salesTotalPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="租赁单价" name="leaseUnitPrice">
						<span>{{ formData.leaseUnitPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="租赁总价" name="leaseTotalPrice">
						<span>{{ formData.leaseTotalPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="4">
					<a-form-item label="安置单价" name="placementPrice">
						<span>{{ formData.placementPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="市场单价" name="marketPrice">
						<span>{{ formData.marketPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<a-tabs v-model:activeKey="activeKey" type="card" @change="activeKeyChange">
			<a-tab-pane key="1" tab="客户信息">
				<a-form layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="6">
							<a-form-item label="村落编号：" name="villageId">
								<span>{{ houseInfo.customerInfo.villageId || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="选房序号：" name="code">
								<span>{{ houseInfo.customerInfo.code || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="认购时间：" name="subscribeTime">
								<span>{{ houseInfo.customerInfo.subscribeTime || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="姓名：" name="name">
								<span>{{ houseInfo.customerInfo.personInfo.name || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="身份证号：" name="idCard">
								<span>{{ houseInfo.customerInfo.personInfo.idCard || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="电话：" name="phone">
								<span>{{ houseInfo.customerInfo.personInfo.phone || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="地址：" name="address">
								<span>{{ houseInfo.customerInfo.address || '-' }}</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
				<h4>共有人信息</h4>
				<a-table :dataSource="houseInfo.customerInfo.shareholderList" :columns="columns" />
				<h4>名额信息</h4>
				<a-table :dataSource="houseInfo.customerInfo.quotaList" :columns="columns" />
				<a-form layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="6">
							<a-form-item label="人数：" name="code">
								<span>{{ houseInfo.customerInfo.quotaList ? houseInfo.customerInfo.quotaList.length : 0 }}人</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="启用面积：" name="code">
								<span>{{ houseInfo.customerInfo.enableArea || 0 }}㎡</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
			</a-tab-pane>
			<a-tab-pane key="2" tab="签约信息">
				<a-form layout="horizontal" v-if="houseInfo.ledgerInfo.contractType === '1'">
					<a-row :gutter="24">
						<a-col :span="8">
							<a-form-item label="销售单价：" name="code">
								<span>{{ houseInfo.saleSign.unitPrice || 0 }}元/㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="销售总价：" name="houseNumber">
								<span>{{ houseInfo.saleSign.totalPrice || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="执行优惠：" name="floor">
								<span>{{ houseInfo.saleSign.discount || '-' }}</span>
								<span class="discountType">{{ houseInfo.saleSign.discountType === '1' ? '折扣' : '直减' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="优惠备注：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.discountRemark || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="签约总价：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.contractPrice || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="签约单价：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.contractUnitPrice || 0 }}元/㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="维修基金单价：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.maintenanceFundUnitPrice || 0 }}元/㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="维修基金：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.maintenanceFund || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="付款形式：" name="actualBuildArea">
								{{ $TOOL.dictTypeData('pay_type', houseInfo.saleSign.paymentMethod) }}
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="定金：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.earnestMoney || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="房款合计：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.totalHousePrice || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8" v-if="houseInfo.saleSign && houseInfo.saleSign.paymentMethod == 2">
							<a-form-item label="分期金额：" name="installmentAmount">
								<span>{{ houseInfo.saleSign.installmentAmount || 0 }}元</span>
							</a-form-item>
						</a-col>
					</a-row>
					<h4 v-if="houseInfo.saleSign.paymentMethod == 2">分期付款</h4>

					<a-table :dataSource="stagesList" :columns="columns4" v-if="houseInfo.saleSign.paymentMethod == 2">
						<template #bodyCell="{ column, record, index }">
							<template v-if="column.dataIndex === 'serialNumber'">
								<span>{{ index + 1 }}</span>
							</template>
							<template v-if="column.key === 'paymentAttachment'">
								<!-- <span> {{ record.paymentAttachment || '-' }}</span> -->
								<div style="width: 100%; text-align: center; margin-left: 6px" v-if="record.paymentAttachment">
									<a-image style="width: 60px; height: 60px" :src="record.paymentAttachment" />
								</div>
							</template>
						</template>
					</a-table>
				</a-form>
				<a-form layout="horizontal" v-if="houseInfo.ledgerInfo.contractType === '2'">
					<a-row :gutter="24">
						<a-col :span="8">
							<a-form-item label="租赁单价：" name="code">
								<span>{{ houseInfo.leaseSign.leaseUnitPrice || 0 }}元/㎡/月</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="租赁总价：" name="actualBuildArea">
								<span>{{ houseInfo.leaseSign.leaseTotalPrice || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="租赁保证金：" name="houseNumber">
								<span>{{ houseInfo.leaseSign.leaseDeposit || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="租赁起始时间：" name="floor">
								<span>{{ houseInfo.leaseSign.leaseStartTime || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="租赁结束时间：" name="actualBuildArea">
								<span>{{ houseInfo.leaseSign.leaseEndTime || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="到期提醒：" name="actualBuildArea">
								<span>{{ houseInfo.leaseSign.expireRemind || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="租期：" name="actualBuildArea">
								<span>{{ houseInfo.leaseSign.leaseTerm || 0 }}个月</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="免租期：" name="actualBuildArea">
								<span>{{ houseInfo.leaseSign.rentFreePeriod || 0 }}个月</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="执行优惠：" name="actualBuildArea">
								<span>{{ houseInfo.leaseSign.discount || 0 }}</span>
								<span class="discountType">{{ houseInfo.leaseSign.discountType === '1' ? '折扣' : '直减' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="优惠备注：" name="actualBuildArea">
								<span>{{ houseInfo.leaseSign.discountRemark || '-' }}</span>
							</a-form-item>
						</a-col>

						<a-col :span="24">
							<a-form-item label="付款形式：" name="actualBuildArea">
								{{ $TOOL.dictTypeData('pay_type', houseInfo.leaseSign.paymentMethod) }}
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="定金" name="earnestMoney">
								<span>{{ houseInfo.leaseSign.earnestMoney || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="房款合计：" name="actualBuildArea">
								<span>{{ houseInfo.leaseSign.totalHousePrice || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8" v-if="houseInfo.saleSign && houseInfo.saleSign.paymentMethod == 2">
							<a-form-item label="分期金额：" name="installmentAmount">
								<span>{{ houseInfo.leaseSign.installmentAmount || 0 }}元</span>
							</a-form-item>
						</a-col>
					</a-row>
					<h4 v-if="houseInfo.leaseSign.paymentMethod == 2">分期付款</h4>

					<a-table :dataSource="stagesList" :columns="columns4" v-if="houseInfo.leaseSign.paymentMethod == 2">
						<template #bodyCell="{ column, record, index }">
							<template v-if="column.dataIndex === 'serialNumber'">
								<span>{{ index + 1 }}</span>
							</template>
							<template v-if="column.key === 'paymentAttachment'">
								<!-- <span> {{ record.paymentAttachment || '-' }}</span> -->
								<div style="width: 100%; text-align: center; margin-left: 6px" v-if="record.paymentAttachment">
									<a-image style="width: 60px; height: 60px" :src="record.paymentAttachment" />
								</div>
							</template>
						</template>
					</a-table>
				</a-form>
				<a-form layout="horizontal" v-if="houseInfo.ledgerInfo.contractType === '3'">
					<a-row :gutter="24">
						<a-col :span="8">
							<a-form-item label="基准价：" name="code">
								<span>{{ houseInfo.placeSign.benchmarkPrice || 0 }}元/㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="面积：" name="houseNumber">
								<span>{{ houseInfo.placeSign.area || 0 }}㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="小计：" name="floor">
								<span>{{ houseInfo.placeSign.subtotal || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="补贴价：" name="actualBuildArea">
								<span>{{ houseInfo.placeSign.subsidyPrice || 0 }}元/㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="面积：" name="actualBuildArea">
								<span>{{ houseInfo.placeSign.subsidyArea || 0 }}㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="小计：" name="actualBuildArea">
								<span>{{ houseInfo.placeSign.subsidySubtotal || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="市场价：" name="actualBuildArea">
								<span>{{ houseInfo.placeSign.marketPrice || 0 }}元/㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="面积：" name="actualBuildArea">
								<span>{{ houseInfo.placeSign.marketArea || 0 }}㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="小计：" name="actualBuildArea">
								<span>{{ houseInfo.placeSign.marketSubtotal || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="执行优惠：" name="actualBuildArea">
								<span>{{ houseInfo.placeSign.discount || 0 }}</span>
								<span class="discountType">{{ houseInfo.placeSign.discountType === '1' ? '折扣' : '直减' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="优惠备注：" name="actualBuildArea">
								<span>{{ houseInfo.placeSign.discountRemark || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="签约总价：" name="actualBuildArea">
								<span>{{ houseInfo.placeSign.contractPrice || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="维修基金：" name="actualBuildArea">
								<span>{{ houseInfo.placeSign.maintenanceFund || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="合计：" name="actualBuildArea">
								<span>{{ houseInfo.placeSign.total || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="签约时间：" name="actualBuildArea">
								<span>{{ houseInfo.placeSign.contractTime || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="付款形式：" name="actualBuildArea">
								{{ $TOOL.dictTypeData('pay_type', houseInfo.placeSign.paymentMethod) }}
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="房款合计：" name="actualBuildArea">
								<span>{{ houseInfo.placeSign.totalHousePrice || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8" v-if="houseInfo.saleSign && houseInfo.saleSign.paymentMethod == 2">
							<a-form-item label="分期金额：" name="installmentAmount">
								<span>{{ houseInfo.placeSign.installmentAmount || 0 }}元</span>
							</a-form-item>
						</a-col>
					</a-row>
					<h4 v-if="houseInfo.placeSign.paymentMethod == 2">分期付款</h4>

					<a-table :dataSource="stagesList" :columns="columns4" v-if="houseInfo.placeSign.paymentMethod == 2">
						<template #bodyCell="{ column, record, index }">
							<template v-if="column.dataIndex === 'serialNumber'">
								<span>{{ index + 1 }}</span>
							</template>
							<template v-if="column.key === 'paymentAttachment'">
								<!-- <span> {{ record.paymentAttachment || '-' }}</span> -->
								<div style="width: 100%; text-align: center; margin-left: 6px" v-if="record.paymentAttachment">
									<a-image style="width: 60px; height: 60px" :src="record.paymentAttachment" />
								</div>
							</template>
						</template>
					</a-table>
				</a-form>
			</a-tab-pane>
			<a-tab-pane key="3" tab="交款信息">
				<a-form layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="8">
							<a-form-item label="合计交款：" name="code">
								<span>{{ houseInfo.ledgerInfo.totalPayment || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="欠款金额：" name="houseNumber">
								<span v-if="houseInfo.ledgerInfo.contractType === '1'"
									>{{
										((houseInfo.saleSign.totalHousePrice || 0) - (houseInfo.ledgerInfo.totalPayment || 0)).toFixed(2) ||
										0
									}}元</span
								>
								<span v-if="houseInfo.ledgerInfo.contractType === '2'"
									>{{
										((houseInfo.leaseSign.totalHousePrice || 0) - (houseInfo.ledgerInfo.totalPayment || 0)).toFixed(
											2
										) || 0
									}}元</span
								>
								<span v-if="houseInfo.ledgerInfo.contractType === '3'"
									>{{
										((houseInfo.placeSign.totalHousePrice || 0) - (houseInfo.ledgerInfo.totalPayment || 0)).toFixed(
											2
										) || 0
									}}元</span
								>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="结清状态：" name="floor">
								<span v-if="houseInfo.ledgerInfo.contractType === '1'">{{
									houseInfo.saleSign.totalHousePrice - houseInfo.ledgerInfo.totalPayment > 0 ? '未结清' : '已结清'
								}}</span>
								<span v-if="houseInfo.ledgerInfo.contractType === '2'">{{
									houseInfo.leaseSign.totalHousePrice - houseInfo.ledgerInfo.totalPayment > 0 ? '未结清' : '已结清'
								}}</span>
								<span v-if="houseInfo.ledgerInfo.contractType === '3'">{{
									houseInfo.placeSign.totalHousePrice - houseInfo.ledgerInfo.totalPayment > 0 ? '未结清' : '已结清'
								}}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="承诺结清日期：" name="floor">
								<span>{{ houseInfo.contractInfo.promiseSettlementDate || '-' }}</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
				<h4>付款记录</h4>
				<a-table :dataSource="houseInfo.paymentInfos" :columns="columns2">
					<template #bodyCell="{ column, record }">
						<template v-if="column.key === 'paymentType'">
							<!-- <span>{{ record.paymentType === 1 ? '交款' : '付款' }}</span> -->
							<span>{{ $TOOL.dictTypeData('payment', record.paymentType) }}</span>
						</template>
						<template v-else-if="column.key === 'paymentAttachment'">
							<div style="width: 100%; text-align: center; margin-left: 6px" v-if="record.paymentAttachment">
								<a-image style="width: 60px; height: 60px" :src="record.paymentAttachment" />
							</div>
							<!-- <span> {{ record.paymentAttachment || '-' }}</span> -->
						</template>
					</template>
				</a-table>
			</a-tab-pane>
			<a-tab-pane key="4" tab="合同信息">
				<a-form layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="8">
							<a-form-item label="合同备案签约：" name="code">
								<span>{{ houseInfo.contractInfo.contractRecordTime || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="合同编号：" name="houseNumber">
								<span>{{ houseInfo.contractInfo.contractNumber || '-' }}</span>
							</a-form-item>
						</a-col>

						<a-col :span="8">
							<a-form-item label="车位状态：" name="floor">
								<span v-if="houseInfo.contractInfo.parkingStatus">{{
									$TOOL.dictTypeData('car_status', houseInfo.contractInfo.parkingStatus)
								}}</span>
								<span v-else>-</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="发票状态：" name="floor">
								<span v-if="houseInfo.contractInfo.invoiceStatus">{{
									$TOOL.dictTypeData('invoice_status', houseInfo.contractInfo.invoiceStatus)
								}}</span>
								<span v-else>-</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="办证申请表/发票：" name="floor">
								<span>{{ houseInfo.contractInfo.certificateApplication || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="备注信息：" name="floor">
								<span>{{ houseInfo.contractInfo.remark || '-' }}</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
			</a-tab-pane>
			<a-tab-pane key="5" tab="历史台账">
				<a-table :dataSource="houseInfo.historyLedgerInfo" :columns="columns3">
					<template #bodyCell="{ column, record }">
						<template v-if="column.dataIndex === 'contractType'">
							<div>
								{{ $TOOL.dictTypeData('contract_type', record.contractType) }}
							</div>
						</template>
						<template v-if="column.dataIndex === 'status'">
							<div>
								{{ $TOOL.dictTypeData('house_status', record.status) }}
							</div>
						</template>
						<template v-if="column.dataIndex === 'caozuo'">
							<div>
								<a-button @click="detailRef.onOpen({ ...record }, 2)" type="link" size="small">查看</a-button>
							</div>
						</template>
					</template>
				</a-table>
			</a-tab-pane>
		</a-tabs>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
		</template>
	</xn-form-container>
	<Detail ref="detailRef" @successful="table.refresh(true)"></Detail>
	<print ref="printRef" v-if="printShow"></print>   
</template>

<script setup name="reHouseForm">
	import { useMenuStore } from '@/store/menu'
	const menuStore = useMenuStore()
	import { message } from 'ant-design-vue'
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import reHouseApi from '@/api/biz/reHouseApi'
	import reLedgerInfoApi from '@/api/biz/reLedgerInfoApi'
	import tool from '@/utils/tool'
	import Detail from '../reledgerinfo/detail.vue'
	import print from './print.vue'
	const detailRef = ref()
	const printRef = ref()

	// 房屋朝向
	const houseOrientation = tool.dictList('house_orientation')
	// 房屋户型
	const houseLayout = tool.dictList('house_layout')
	// 房屋类型
	const houseType = tool.dictList('house_type')
	// 房屋状态
	const houseStatus = tool.dictList('house_status')
	// 抽屉状态
	const visible = ref(false)
	const printShow = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()

	// 表单数据
	const formData = ref({})
	const stagesList = ref([])
	const houseInfo = ref({
		ledgerInfo: {},
		customerInfo: {
			personInfo: {
				name: '',
				idCard: '',
				phone: ''
			}
		},
		saleSign: {
			paymentInfoList: []
		},
		placeSign: {},
		leaseSign: {},
		paymentInfos: [],
		contractInfo: {}
	})
	const houseInfo2 = ref({
		ledgerInfo: {},
		customerInfo: {
			personInfo: {
				name: '',
				idCard: '',
				phone: ''
			}
		},
		saleSign: {
			paymentInfoList: []
		},
		placeSign: {},
		leaseSign: {},
		paymentInfos: [],
		contractInfo: {}
	})
	const submitLoading = ref(false)
	const activeKey = ref('1')
	const columns = ref([
		{
			title: '姓名',
			dataIndex: 'name',
			align: 'center'
		},
		{
			title: '身份证号',
			dataIndex: 'idCard',
			align: 'center'
		},
		{
			title: '电话',
			dataIndex: 'phone',
			align: 'center'
		}
	])
	const columns2 = ref([
		{
			title: '交款类型',
			dataIndex: 'paymentType',
			key: 'paymentType',
			align: 'center'
		},
		{
			title: '交款金额(元)',
			dataIndex: 'paymentAmount',
			key: 'paymentAmount',
			align: 'center'
		},
		{
			title: '交款时间',
			dataIndex: 'paymentTime',
			key: 'paymentTime',
			align: 'center'
		},
		{
			title: '附件',
			dataIndex: 'paymentAttachment',
			key: 'paymentAttachment',
			align: 'center'
		}
	])
	const columns3 = ref([
		{
			title: '客户姓名',
			dataIndex: 'name',
			align: 'center'
		},
		{
			title: '客户电话',
			dataIndex: 'phone',
			align: 'center'
		},
		{
			title: '签约类型',
			dataIndex: 'contractType',
			align: 'center'
		},
		{
			title: '结清状态',
			dataIndex: 'status',
			align: 'center'
		},
		{
			title: '认购日期',
			dataIndex: 'subscribeTime',
			align: 'center'
		},
		{
			title: '操作',
			dataIndex: 'caozuo',
			align: 'center'
		}
	])

	const columns4 = ref([
		{
			title: '序号',
			dataIndex: 'serialNumber',
			scopedSlots: { customRender: 'bodyCell' },
			align: 'center'
		},

		{
			title: '交款金额(元)',
			dataIndex: 'paymentAmount',
			key: 'paymentAmount',
			align: 'center'
		},
		{
			title: '交款时间',
			dataIndex: 'paymentTime',
			key: 'paymentTime',
			align: 'center'
		},
		{
			title: '附件',
			dataIndex: 'paymentAttachment',
			key: 'paymentAttachment',
			align: 'center'
		}
	])

	// 打开抽屉
	const onOpen = (record) => {
		// if (record.contractStatus) {
		//有台账信息
		houseInfo.value.historyLedgerInfo = []
		activeKey.value = '1'
		houseInfo.value = houseInfo2.value
		visible.value = true
		if (record) {
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)
			formData.value.projectId = menuStore.projectObj.id
			getDetail(record.id)
		}
		// } else {
		// 	message.error('暂无详情数据！')
		// }
	}
	const getDetail = (id) => {
		reLedgerInfoApi
			.reLedgerInfoByHouseId({ id: id }, 2)
			.then((res) => {
				if (!res.paymentInfos) {
					res.paymentInfos = []
				}
				if (!res.paymentInfoList) {
					res.paymentInfoList = []
				}
				if (res.ledgerInfo.contractType == 1) {
					stagesList.value = res.saleSign ? res.saleSign.paymentInfoList : []
				}
				if (res.ledgerInfo.contractType == 2) {
					stagesList.value = res.leaseSign ? res.leaseSign.paymentInfoList : []
				}
				if (res.ledgerInfo.contractType == 3) {
					stagesList.value = res.placeSign ? res.placeSign.paymentInfoList : []
				}
				houseInfo.value = res
			})
			.catch((err) => {
				if (err.code !== 200) {
					houseInfo.value.ledgerInfo.contractType = '1'
					stagesList.value = []
				}
			})
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		printShow.value = false
		visible.value = false
	}
	// 默认要校验的
	const formRules = {}
	// 验证并提交数据
	const onSubmit = () => {
		formRef.value.validate().then(() => {
			submitLoading.value = true
			const formDataParam = cloneDeep(formData.value)
			reHouseApi
				.reHouseSubmitForm(formDataParam, formDataParam.id)
				.then(() => {
					onClose()
					emit('successful')
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}
	const activeKeyChange = (e) => {
		if (e == 5) {
			reLedgerInfoApi
				.reLedgerInfoPage({
					isHistory: true,
					houseId: formData.value.id
				})
				.then((res) => {
					houseInfo.value.historyLedgerInfo = res.records
				})
		}
	}

	const onPrint = () => {
		printShow.value = true
		formData.value.abs = 1
		nextTick(()=> {
			printRef.value.onPrint(formData.value)
		})
	}

	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
<style scoped>
	.discountType {
		font-size: 12px;
		color: #409eff;
		margin-left: 5px;
	}
</style>
