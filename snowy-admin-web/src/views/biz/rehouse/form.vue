<template>
	<xn-form-container
		:title="formData.id ? '编辑房屋管理' : '增加房屋管理'"
		:width="600"
		:visible="visible"
		:destroy-on-close="true"
		:footer-style="{ textAlign: 'right' }"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="楼号：" name="code">
						<a-input v-model:value="formData.code" placeholder="请输入楼号" allow-clear disabled />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="单元：" name="unit">
						<a-input-number
							v-model:value="formData.unit"
							:min="1"
							placeholder="请输入单元"
							allow-clear
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="楼层：" name="floor">
						<a-input
							v-model:value="formData.floor"
							:min="1"
							placeholder="请输入楼层"
							allow-clear
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="房号：" name="houseNumber">
						<a-input
							v-model:value="formData.houseNumber"
							placeholder="请输入房号"
							allow-clear
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="预测建筑面积：" name="forecastBuildArea">
						<a-input
							v-model:value="formData.forecastBuildArea"
							placeholder="请输入预测建筑面积"
							allow-clear
							addon-after="㎡"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="预测套内面积：" name="forecastHouseArea">
						<a-input
							v-model:value="formData.forecastHouseArea"
							placeholder="请输入预测套内面积"
							allow-clear
							addon-after="㎡"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="实测建筑面积：" name="actualBuildArea">
						<a-input
							v-model:value="formData.actualBuildArea"
							placeholder="请输入实测建筑面积"
							allow-clear
							addon-after="㎡"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="实测套内面积：" name="actualHouseArea">
						<a-input
							v-model:value="formData.actualHouseArea"
							placeholder="请输入实测套内面积"
							allow-clear
							addon-after="㎡"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="朝向：" name="houseOrientation">
						<a-select
							v-model:value="formData.houseOrientation"
							placeholder="请选择房屋朝向"
							:options="houseOrientation"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="户型：" name="houseLayout">
						<a-select v-model:value="formData.houseLayout" placeholder="请选择房屋户型" :options="houseLayout" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="销售单价：" name="salesUnitPrice">
						<a-input
							v-model:value="formData.salesUnitPrice"
							placeholder="请输入销售单价"
							allow-clear
							addon-after="元/㎡"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="销售总价：" name="salesTotalPrice">
						<a-input
							v-model:value="formData.salesTotalPrice"
							placeholder="请输入销售总价"
							allow-clear
							addon-after="元"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="租赁单价：" name="leaseUnitPrice">
						<a-input
							v-model:value="formData.leaseUnitPrice"
							placeholder="请输入租赁单价"
							allow-clear
							addon-after="元/㎡"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="租赁总价：" name="leaseTotalPrice">
						<a-input
							v-model:value="formData.leaseTotalPrice"
							placeholder="请输入租赁总价"
							allow-clear
							addon-after="元"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="安置单价：" name="placementPrice">
						<a-input
							v-model:value="formData.placementPrice"
							placeholder="请输入安置单价"
							allow-clear
							addon-after="元/㎡"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="市场单价：" name="marketPrice">
						<a-input v-model:value="formData.marketPrice" placeholder="请输入市场单价" allow-clear addon-after="元/㎡" />
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="reHouseForm">
	import { message } from 'ant-design-vue'
	import { useMenuStore } from '@/store/menu'
	const menuStore = useMenuStore()
	import { cloneDeep } from 'lodash-es'
	import { required, rules } from '@/utils/formRules'
	import reHouseApi from '@/api/biz/reHouseApi'
	import tool from '@/utils/tool'
	// 房屋朝向
	const houseOrientation = tool.dictList('house_orientation')
	// 房屋户型
	const houseLayout = tool.dictList('house_layout')
	// 房屋类型
	const houseType = tool.dictList('house_type')
	// 房屋状态
	const houseStatus = tool.dictList('house_status')
	// 抽屉状态
	const visible = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	const submitLoading = ref(false)

	// 打开抽屉
	const onOpen = (record) => {
		console.log(record, '--------')

		if (record && record.code) {
			visible.value = true
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)
			formData.value.houseType = 1 //  houseType 住宅1、商业2、储藏间3
			formData.value.projectId = menuStore.projectObj.id
			formData.value.buildCode = record.code
			// formData.value.code = record.code
		} else {
			message.error('请先选择楼栋！')
		}
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		visible.value = false
	}
	// 默认要校验的
	const formRules = {
		unit: [{ required: true, message: '请输入单元', trigger: 'change' }],
		floor: [{ required: true, message: '请输入楼层', trigger: 'change' }],
		houseNumber: [{ required: true, message: '请输入房号', trigger: 'change' }],
		actualBuildArea: [{ required: true, message: '请输入实测建筑面积', trigger: 'change' }, rules.typeNumberDot],
		// placementPrice: [{ required: true, message: '请输入安置价', trigger: 'change' }],
		// marketPrice: [{ required: true, message: '请输入市场价', trigger: 'change' }],
		// salesUnitPrice: [{ required: true, message: '请输入销售单价', trigger: 'change' }],
		// salesTotalPrice: [{ required: true, message: '请输入销售总价', trigger: 'change' }],
		// leaseUnitPrice: [{ required: true, message: '请输入租赁单价', trigger: 'change' }],
		// leaseTotalPrice: [{ required: true, message: '请输入租赁总价', trigger: 'change' }]
	}
	// 验证并提交数据
	const onSubmit = () => {
		formRef.value.validate().then(() => {
			submitLoading.value = true
			const formDataParam = cloneDeep(formData.value)
			reHouseApi
				.reHouseSubmitForm(formDataParam, formDataParam.id)
				.then(() => {
					onClose()
					emit('successful')
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}
	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
