<template>
	<xn-form-container
		:title="formData.id ? '编辑区域管理' : '增加区域管理'"
		:width="600"
		:visible="visible"
		:destroy-on-close="true"
		:footer-style="{ textAlign: 'right' }"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="horizontal">
			<a-row :gutter="16">
				<a-col :span="24">
					<a-form-item label="区域名称：" name="code">
						<a-input v-model:value="formData.code" placeholder="请输入区域名称" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="区域图片：" name="areaImage">
						<xn-upload v-model:value="formData.areaImage" uploadMode="image" />
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button class="xn-mr8" type="primary" danger @click="deleteArea">删除该区域</a-button>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="reHouseForm">
	import { useMenuStore } from '@/store/menu'
	const menuStore = useMenuStore()
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import reParkAreaApi from '@/api/biz/reParkAreaApi'
	import tool from '@/utils/tool'
	// 区域朝向
	const houseOrientation = tool.dictList('house_orientation')
	// 区域户型
	const houseLayout = tool.dictList('house_layout')
	// 区域类型
	const houseType = tool.dictList('house_type')
	// 区域状态
	const houseStatus = tool.dictList('house_status')
	// 抽屉状态
	const visible = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	const submitLoading = ref(false)

	// 打开抽屉
	const onOpen = (record) => {
		console.log(record, '--------')

		visible.value = true
		if (record) {
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)
			formData.value.projectId = menuStore.projectObj.id
		}
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		visible.value = false
	}
	// 默认要校验的
	const formRules = {
		code: [{ required: true, message: '请输入区域名称', trigger: 'change' }]
	}
	// 验证并提交数据
	const onSubmit = () => {
		formRef.value.validate().then(() => {
			submitLoading.value = true
			const formDataParam = cloneDeep(formData.value)
			reParkAreaApi
				.reParkAreaSubmitForm(formDataParam, formDataParam.id)
				.then(() => {
					onClose()
					emit('successful')
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}

	const deleteArea = () => {
		formRef.value.validate().then(() => {
			const param = [{ id: formData.value.id }]
			reParkAreaApi
				.reParkAreaDelete(param)
				.then(() => {
					onClose()
					emit('successful')
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}
	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
