<template>
	<xn-form-container
		:title="formData.id ? '编辑楼栋' : '增加楼栋'"
		:width="600"
		:visible="visible"
		:destroy-on-close="true"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="24">
					<a-form-item label="楼号" name="code">
						<a-input  v-model:value="formData.code" placeholder="请输入楼号" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="预售证许可证编号" name="extJson">
						<a-input placeholder="请输入预售证许可证编号" v-model:value="formData.extJson" />
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="是否为商业楼栋" name="type">
						<a-radio-group
							:disabled="props.modelType=='园区'"
							v-model:value="formData.type"
							button-style="solid"
							:options="categoryOptions"
							option-type="button"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="楼栋定位" name="type">
						<a-button type="primary" @click="selectPoint">
							<template #icon><plus-outlined /></template>
							选择定位
						</a-button>
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="">
						<div style="display: flex; align-items: center">
							<a-input placeholder="请选择" v-model:value="formData.positionX" /> <span>&nbsp; &nbsp;</span>
							<a-input placeholder="请选择" v-model:value="formData.positionY" />
						</div>
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button class="xn-mr8" @click="onClose">关闭</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
		</template>
		<positionModel
			@zoom="zoom"
			:pointPosition="mapTable"
			:isTrue="isTrue"
			:overlook="overlook"
			:num="num"
			:drag="true"
			ref="clickMap"
			@iconClick="iconClick"
		/>
	</xn-form-container>
</template>

<script setup>
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import positionModel from './buildPostion.vue'
	import reBuildingApi from '@/api/biz/reBuildingApi'
	import GaodeMap from '@/components/Map/gaodeMap/index.vue'
	import tool from '@/utils/tool'
	import reProjectApi from '@/api/biz/reProjectApi'

	const emit = defineEmits({ successful: null })
	const visible = ref(false)
	const formRef = ref()
	const treeData = ref([])
	const submitLoading = ref(false)
	const map = ref(null)
	const overlook = ref('')
	const clickMap = ref()
	const mapTable = ref([]) //打点的坐标数组
	const isTrue = ref(true) //是否允许拖动
	const url = ref('')
	const num = ref(1) //放大缩小倍数
	// 表单数据
	const formData = ref({
		type: '1'
	})
	const props = defineProps({
		id: {
			type: String,
			default: ''
		},
		modelType: {
			type: String,
			default: ''
		}
	})
	// 打开抽屉
	const onOpen = (record) => {
		visible.value = true
		num.value = 1
		formData.value = {
			type: '1'
		}
		if (record) {
			formData.value = Object.assign({}, record)
		} else {
			formData.value = {
				type: '1'
			}
		}
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		visible.value = false
	}

	// 默认要校验的
	const formRules = {
		code: [required('请输入楼号')],
		// extJson: [required('请输入预售证号')]
	}
	const categoryOptions = tool.dictList('SYS_YESNO')

	// 验证并提交数据
	const onSubmit = () => {
		formRef.value.validate().then(() => {
			submitLoading.value = true
			formData.value.projectId = formData.id ? formData.id : props.id
			const formDataParam = cloneDeep(formData.value)
			if(props.modelType == '园区') {
				formDataParam.type = 2
			}
			reBuildingApi
				.reBuildingSubmitForm(formDataParam, formDataParam.id)
				.then(() => {
					onClose()
					emit('successful')
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}

	const selectPoint = () => {
		mapTable.value = []
		if (!formData.value.positionX) {
			formData.value.positionX = 20
			formData.value.positionY = 20
		}
		mapTable.value.push(formData.value)
		reProjectApi.reProjectDetail({ id: props.id }).then((res) => {
			clickMap.value.onOpen()
			overlook.value = res.overlook
		})
	}

	const iconClick = (data) => {
		console.log(data, 'data')
	}
	// 放大缩小
	const zoom = (value) => {
		if (value == '放大') {
			num.value += 0.1
		} else {
			num.value -= 0.1
		}
	}

	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>

<style>
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none;
	}
	input[type='number'] {
		-moz-appearance: textfield;
	}
</style>
