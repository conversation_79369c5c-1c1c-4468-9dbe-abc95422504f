<template>
	<xn-form-container title="楼栋定位" width="100%" :visible="visible" :destroy-on-close="true" @close="onClose">
		<div class="clickMap" v-if="visible">
			<div
				@mousedown="moveMouse"
				ref="myDiv"
				:style="{
					transform: 'scale(' + num + ')',
					position: 'relative'
				}"
			>
				<div
					@mousewheel="mousewheelImg"
					ref="clickMap"
					:style="{
						position: 'relative',
						width: '100%',
						height: '100%'
					}"
					@click="getOffect"
				>
					<div v-for="(item, index) in props.pointPosition" :key="index">
						<div
							@mousedown.stop="moveMouse2($event, index)"
							@click="iconClick(item)"
							class="point"
							:style="{
								position: 'absolute',
								left: item.positionX + 'px',
								top: item.positionY + 'px',
								fontSize: '18px',
								color: '#ffffff'
							}"
						>
							<span class="iconfont icon-dingwei1" style="color: #409eff; font-size: 18px"></span>
							{{ item.code }}
						</div>
					</div>
					<img style="width: 100%; height: 100%; max-height: 100%" :src="props.overlook" alt="" />
				</div>
			</div>
		</div>

		<template #footer>
			<a-button class="xn-mr8" @click="onClose">关闭</a-button>
			<a-button type="primary" @click="onSubmit" v-if="props.isTrue">保存</a-button>
		</template>
	</xn-form-container>
	<a-modal v-model:open="open" title="楼栋统计">
		<div style="font-size: 16px; font-weight: bold; text-align: center; margin-bottom: 10px" v-if="buildCode">
			{{ buildCode }}
		</div>
		<a-card>
			<div style="width: 100%" v-if="projectDetail.projectType == 1">
				<div>
					<div class="tabBgc" style="display: flex; justify-content: space-around">
						<div class="th" style="width: 120px">类型</div>
						<div class="th">住宅</div>
						<div class="th">商业</div>
						<div class="th">储藏间</div>
					</div>
					<div>
						<div
							style="display: flex; justify-content: space-around"
							class="table-c"
							:class="index % 2 !== 0 ? 'tabBgc' : ''"
							v-for="(items, index) in listData2"
						>
							<div class="th" style="width: 120px">{{ items.names }}</div>
							<div class="th">{{ items.totalHouse }}</div>
							<div class="th">{{ items.totalHouse2 }}</div>
							<div class="th">{{ items.totalHouse3 }}</div>
						</div>
					</div>
				</div>
			</div>
			<div style="width: 100%" v-else>
				<div class="tabBgc" style="display: flex; justify-content: space-around">
					<div class="th" style="width: 120px">类型</div>
					<div class="th">厂房</div>
					<div class="th">办公用房</div>
					<div class="th">宿舍</div>
				</div>
				<div>
					<div
						style="display: flex; justify-content: space-around"
						class="table-c"
						:class="index % 2 !== 0 ? 'tabBgc' : ''"
						v-for="(items, index) in listData2"
					>
						<div class="th" style="width: 120px">{{ items.names }}</div>
						<div class="th">{{ items.totalHouse }}</div>
						<div class="th">{{ items.totalHouse2 }}</div>
						<div class="th">{{ items.totalHouse3 }}</div>
					</div>
				</div>
			</div>
		</a-card>
		<template #footer>
			<a-button key="back" @click="handleCancel">关闭</a-button>
		</template>
	</a-modal>
</template>

<script setup>
	import moduleApi from '@/api/sys/resource/moduleApi'
	import reBuildingApi from '@/api/biz/reBuildingApi'

	import { nextTick } from 'vue'
	import { watch } from 'vue'
	import projectDetailApi from '@/api/biz/projectDetailApi'
import { log } from '@antv/g2plot/lib/utils'

	// 默认是关闭状态
	const emits = defineEmits(['zoom', 'iconClick', 'close'])
	const visible = ref(false)
	const listData2 = ref([])
	const map = ref(null)
	const projectDetail = ref(null)
	// 表单数据
	const imgTranLeft = ref(0)
	const imgTranTop = ref(0)
	const buildCode = ref('')
	const positionX = ref(0)
	const positionY = ref(0)
	const open = ref(false)
	const myDiv = ref(null)
	const topDistance = ref(0)
	const leftDistance = ref(0)
	const topDistance2 = ref(0)
	const leftDistance2 = ref(0)
	const props = defineProps({
		detailTable: Object, //包含图片的对象
		isTrue: Boolean, //是否可以拖动
		pointPosition: Array, //点位坐标数组
		drag: Boolean,
		num: {
			type: Number,
			default: 1
		},
		popover: {
			//是否显示弹出层
			type: Boolean,
			default: false
		},
		overlook: {
			type: String,
			default: ''
		},
		detail: {
			type: Boolean,
			default: false
		}
	})

	onMounted(() => {})
	watch(
		() => props.pointPosition,
		(newValue, oldValue) => {
			if (newValue.id) {
				positionX.value = newValue[0].positionX
				positionY.value = newValue[0].positionY
			}
		},
		{
			deep: true
		}
	)
	// 放大缩小
	const mousewheelImg = (e) => {
		if (e.deltaY > 0) {
			if (props.num < 0.3) return
			emits('zoom', '缩小')
		} else {
			emits('zoom', '放大')
		}
		// setTimeout(() => {
		// 	if (myDiv.value) {
		// 		topDistance.value = myDiv.value.getBoundingClientRect().top + window.scrollY
		// 		leftDistance.value = myDiv.value.getBoundingClientRect().left + window.scrollX
		// 	}
		// }, 200)
	}
	const getOffect = (e) => {
		let rect = e.currentTarget.getBoundingClientRect()
		let disX = e.clientX - rect.left
		let disY = e.clientY - rect.top

		document.onmouseup = null
	}

	const positionClick = (e) => {}
	// 拖动
	const moveMouse2 = (e, index) => {
		let odiv = e.target //获取目标元素

		//算出鼠标相对元素的位置
		let rect = e.currentTarget.getBoundingClientRect()

		let disX = e.clientX - rect.left
		let disY = e.clientY - rect.top
		const x = e.clientX
		const y = e.clientY
		if (myDiv.value) {
			topDistance.value = myDiv.value.getBoundingClientRect().top + window.scrollY
			leftDistance.value = myDiv.value.getBoundingClientRect().left + window.scrollX
		}
		if (props.isTrue) {
			setTimeout(() => {
				// 拖动
				document.onmousemove = (es) => {
					//用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
					let left = Math.floor((es.clientX - disX - leftDistance.value) / props.num)
					let top = Math.floor((es.clientY - disY - topDistance.value) / props.num)

					//绑定元素位置
					positionX.value = left
					positionY.value = top
					odiv.style.left = left + 'px'
					odiv.style.top = top + 'px'
					props.pointPosition[index].positionX = positionX.value
					props.pointPosition[index].positionY = positionY.value
				}
				document.onmouseup = (e) => {
					document.onmousemove = null
					document.onmouseup = null
				}
			}, 50)
		}
	}
	const moveMouse = (e, index) => {
		let odiv = e.target

		//算出鼠标相对元素的位置
		let rect = e.currentTarget.getBoundingClientRect()
		let disX = e.clientX - rect.left
		let disY = e.clientY - rect.top
		if (myDiv.value) {
			topDistance2.value = myDiv.value.getBoundingClientRect().top + window.scrollY
			leftDistance2.value = myDiv.value.getBoundingClientRect().left + window.scrollX
		}

		setTimeout(() => {
			let _x = 0
			let _b = 0
			// 拖动
			document.onmousemove = (es) => {
				//鼠标按下并移动的事件
				let left = Math.floor((es.clientX - disX - leftDistance2.value) / props.num) + imgTranLeft.value
				let top = Math.floor((es.clientY - disY - topDistance2.value) / props.num) + imgTranTop.value

				_x = left
				_b = top

				//绑定元素位置
				myDiv.value.style.left = left + 'px'
				myDiv.value.style.top = top + 'px'
			}
			document.onmouseup = (e) => {
				document.onmousemove = null
				document.onmouseup = null
				imgTranLeft.value = _x
				imgTranTop.value = _b
			}
		}, 20)
	}

	const detailEvent = (item) => {
		this.$refs['popoverSH' + item.id][0].doClose() //点击之后隐藏面板
		this.$emit('detailEvent', item)
	}
	// 打点事件
	const ondrop = (e) => {
		let odiv = e.target //获取目标元素
		//算出鼠标相对元素的位置
		let disX = e.clientX - odiv.offsetLeft
		let disY = e.clientY - odiv.offsetTop
		if (props.isTrue) {
			// 拖动
			document.onmousemove = (e) => {
				//鼠标按下并移动的事件
				//用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
				let left = e.clientX - disX
				let top = e.clientY - disY
				//绑定元素位置到positionX和positionY上面
				this.positionX = top
				this.positionY = left
				//移动当前元素
				odiv.style.left = left + 'px'
				odiv.style.top = top + 'px'
			}
			document.onmouseup = (e) => {
				document.onmousemove = null
				document.onmouseup = null
			}
		} else {
			let left = disX - odiv.getBoundingClientRect().x + Number(this.$refs.clickMap.style.left.replace(/px/, '') - 5)
			let top = disY - odiv.getBoundingClientRect().y + Number(this.$refs.clickMap.style.top.replace(/px/, '') - 5)

			let data = {
				positionX: left / this.num,
				positionY: top / this.num
				//   iconName: "sensor", //没有传图标的话默认传感器图标
			}
			this.$emit('addBitmap', data)

			this.biaozhuWidth = 0
			this.biaozhuHeight = 0
			this.biaozhuLeft = 0
			this.biaozhuTop = 0
			document.onmousemove = null
			document.onmouseup = null
		}
	}

	const handleCancel = () => {
		open.value = false
		listData2.value = []
	}

	//点击图标事件
	const iconClick = (data) => {
		console.log(data,'dadata');
		
		if (!props.detail) return
		buildCode.value = data.code + '号楼'
		// emits('iconClick', data)
		reBuildingApi.reBuildingData({ id: data.id }).then((res) => {
			let recordList = res
			let temp = {
				type1: {
					totalHouse: 0,
					totalArea: 0,
					soldHouse: 0,
					soldArea: 0,
					unsoldHouse: 0,
					unsoldArea: 0
				},
				type2: {
					totalHouse2: 0,
					totalArea2: 0,
					soldHouse2: 0,
					soldArea2: 0,
					unsoldHouse2: 0,
					unsoldArea2: 0
				},

				type3: {
					totalHouse3: 0,
					totalArea3: 0,
					soldHouse3: 0,
					soldArea3: 0,
					unsoldHouse3: 0,
					unsoldArea3: 0
				}
			}
			recordList.forEach((el, index) => {
				if (el.type == 1) {
					temp.type1.totalHouse = el.totalHouse ? el.totalHouse : 0
					temp.type1.totalArea = el.totalArea ? el.totalArea : 0
					temp.type1.soldHouse = el.soldHouse ? el.soldHouse : 0
					temp.type1.soldArea = el.soldArea ? el.soldArea : 0
					temp.type1.unsoldHouse = el.unsoldHouse ? el.unsoldHouse : 0
					temp.type1.unsoldArea = el.unsoldArea ? el.unsoldArea : 0
				}
				if (el.type == 2) {
					temp.type2.totalHouse2 = el.totalHouse ? el.totalHouse : 0
					temp.type2.totalArea2 = el.totalArea ? el.totalArea : 0
					temp.type2.soldHouse = el.soldHouse ? el.soldHouse : 0
					temp.type2.soldArea2 = el.soldArea ? el.soldArea : 0
					temp.type2.unsoldHouse2 = el.unsoldHouse ? el.unsoldHouse : 0
					temp.type2.unsoldArea2 = el.unsoldArea ? el.unsoldArea : 0
				}
				if (el.type == 3) {
					temp.type3.totalHouse3 = el.totalHouse ? el.totalHouse : 0
					temp.type3.totalArea3 = el.totalArea ? el.totalArea : 0
					temp.type3.soldHouse = el.soldHouse ? el.soldHouse : 0
					temp.type3.soldArea3 = el.soldArea ? el.soldArea : 0
					temp.type3.unsoldHouse3 = el.unsoldHouse ? el.unsoldHouse : 0
					temp.type3.unsoldArea3 = el.unsoldArea ? el.unsoldArea : 0
				}
			})

			let i = [
				{
					names: '总量(套)',
					totalHouse: temp.type1.totalHouse,
					totalHouse2: temp.type2.totalHouse2,
					totalHouse3: temp.type3.totalHouse3
				},
				{
					names: '总面积(㎡)',
					totalHouse: temp.type1.totalArea,
					totalHouse2: temp.type2.totalArea2,
					totalHouse3: temp.type3.totalArea3
				},
				{
					names: '已售量(套)',
					totalHouse: temp.type1.soldHouse,
					totalHouse2: temp.type2.soldHouse2,
					totalHouse3: temp.type3.soldHouse3
				},
				{
					names: '已售面积(㎡)',
					totalHouse: temp.type1.soldArea,
					totalHouse2: temp.type2.soldArea2,
					totalHouse3: temp.type3.soldArea3
				},
				{
					names: '未售量(套)',
					totalHouse: temp.type1.unsoldHouse,
					totalHouse2: temp.type2.unsoldHouse2,
					totalHouse3: temp.type3.unsoldHouse3
				},
				{
					names: '未售面积(㎡)',
					totalHouse: temp.type1.unsoldArea,
					totalHouse2: temp.type2.unsoldArea2,
					totalHouse3: temp.type3.unsoldArea3
				}
			]
			listData2.value = i
			open.value = true
		})
	}
	const destroyed = () => {
		document.mousewheelImg = null
	}

	// 打开抽屉
	const onOpen = (record) => {
		console.log(record, 'record')

		projectDetail.value = record
		visible.value = true
		setTimeout(() => {
			if (myDiv.value) {
				topDistance.value = myDiv.value.getBoundingClientRect().top + window.scrollY
				leftDistance.value = myDiv.value.getBoundingClientRect().left + window.scrollX
			}
		}, 1000)
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
		emits('close')
	}

	// 验证并提交数据
	const onSubmit = () => {
		if (props.isTrue) {
			props.pointPosition[0].positionX = positionX.value
			props.pointPosition[0].positionY = positionY.value
		}

		visible.value = false
	}

	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>

<style lang="scss">
	.popover {
		background: #678295;
		border: 2px solid #0095ff;
		color: #ffffff;
		font-size: 16px;
		.xiaoshou {
			cursor: pointer; /*鼠标悬停变小手*/
		}
		.el-col {
			line-height: 32px;
			span {
				font-weight: bold;
			}
		}
	}
	.clickMap {
		display: flex;
		justify-content: center;
		width: 100%;
		height: 100%;
		max-height: 100%;
		overflow: hidden;
		.r_b {
			position: absolute;
			right: 0;
			bottom: 0;
			width: 20px;
			height: 20px;
			// background: red;
		}
		.r_b:hover {
			cursor: se-resize;
		}
	}
	.popover-top {
		height: 60px;
		img {
			width: 50px;
			height: 50px;
		}
		span {
			float: right;
		}
	}
	.popover-content,
	.popover-footer {
		border-top: 1px solid #f1f1f1;
		span {
			margin-right: 5px;
			font-weight: bold;
		}
		.el-col {
			line-height: 22px;
		}
	}
	img {
		-webkit-user-drag: none;
	}

	.point {
		cursor: pointer;
		-webkit-user-select: none;
		-moz-user-select: none;
		-ms-user-select: none;
		user-select: none;
		-webkit-user-drag: none;
	}

	.xn-data {
		width: 350px;
		height: 280px;
		background-color: #fff;
		position: absolute;
	}

	.outsideContainerOne {
		width: 100%;
		height: 209px;
		position: relative;
		display: flex;
		justify-content: space-between;
		flex-wrap: nowrap;

		.outsideContainerTwo {
			width: 100%;
			height: 100%;
			object-fit: cover;
			position: absolute;
			top: 0;
			left: 0;
			z-index: 1;
		}

		.outsideContainerThree {
			background-color: rgba(0, 0, 0, 0.5);
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			padding: 10px;
			box-sizing: border-box;
			z-index: 2;
			display: flex;
			justify-content: center;
			color: aliceblue;
		}

		.outsideContainerFour {
			background-color: rgba(0, 0, 0, 0.5);
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			padding: 10px;
			box-sizing: border-box;
			z-index: 2;
		}
	}
	.th {
		width: 80px;
		line-height: 32px;
		text-align: center;
	}

	.tabBgc {
		background-color: #eff6ff !important;
	}
</style>
