<template>
	<a-drawer
		title="车位详情"
		:width="1000"
		:visible="visible"
		:destroy-on-close="true"
		:footer-style="{ textAlign: 'right' }"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="horizontal">
			<a-row :gutter="24">
				<a-col :span="8">
					<a-form-item label="车位编号：" name="areaId">
						<span>{{ formData.areaId }}</span>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="车位区域：" name="code">
						<span>{{ formData.code }}</span>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="车位分区：" name="parkPartition">
						<span>{{ formData.parkPartition }}区</span>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="车位类别：" name="type">
						{{ $TOOL.dictTypeData('car_type', formData.type) }}
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="车位总价：" name="totalPrice">
						<span>{{ formData.totalPrice }}元</span>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="车位状态：" name="status">
						{{ $TOOL.dictTypeData('car_status2', formData.status) }}
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<a-tabs v-model:activeKey="activeKey" type="card">
			<a-tab-pane key="1" tab="客户信息">
				<a-form layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="8">
							<a-form-item label="村落编号：" name="actualBuildArea">
								<span>{{ carInfo.customerInfo.villageId || '-' }}㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="顺序：" name="actualBuildArea">
								<span>{{ carInfo.customerInfo.code || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="认购时间：" name="actualBuildArea">
								<span>{{ carInfo.customerInfo.subscribeTime || '-' }}㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="姓名：" name="code">
								<span>{{ carInfo.customerInfo.personInfo.name || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="身份证号：" name="houseNumber">
								<span>{{ carInfo.customerInfo.personInfo.idCard || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="电话：" name="floor">
								<span>{{ carInfo.customerInfo.personInfo.phone || '-' }}</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
			</a-tab-pane>
			<a-tab-pane key="2" tab="签约信息">
				<a-form layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="24">
							<a-form-item label="执行优惠：" name="code">
								<span>{{ carInfo.parkSign.discount || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="优惠备注：" name="actualBuildArea">
								<span>{{ carInfo.parkSign.remark || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="签约价：" name="actualBuildArea">
								<span>{{ carInfo.parkSign.contractPrice || 0 }}元</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
			</a-tab-pane>
			<a-tab-pane key="3" tab="交款信息">
				<a-form layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="8">
							<a-form-item label="合计交款：" name="code">
								<span>{{ carInfo.ledgerInfo.totalPayment || 0 }} 元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="欠款金额：" name="houseNumber">
								<span>{{ carInfo.ledgerInfo.contractPrice - carInfo.ledgerInfo.totalPayment }} 元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="结清状态：" name="floor">
								<span>{{
									carInfo.ledgerInfo.contractPrice - carInfo.ledgerInfo.totalPayment === 0 ? '已结清' : '未结清'
								}}</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
				<h4>付款记录</h4>
				<a-table :dataSource="carInfo.paymentList" :columns="columns">
					<template #bodyCell="{ column, record }">
						<template v-if="column.key === 'paymentType'">
							{{ $TOOL.dictTypeData('payment', record.paymentType) }}
						</template>
						<template v-else-if="column.key === 'paymentAttachment'">
							<span>
								{{ record.paymentAttachment || '无' }}
							</span>
						</template>
					</template>
				</a-table>
			</a-tab-pane>
		</a-tabs>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
		</template>
	</a-drawer>
</template>

<script setup name="reHouseForm">
	import { message } from 'ant-design-vue'
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import reParkApi from '@/api/biz/reParkApi'
	import reParkLedgerInfoApi from '@/api/biz/reParkLedgerInfoApi'
	import tool from '@/utils/tool'
	// 车位状态
	const houseStatus = tool.dictList('house_status2')
	// 抽屉状态
	const visible = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	const carInfo = ref({})
	const submitLoading = ref(false)
	const activeKey = ref('1')
	const columns = ref([
		{
			title: '交款类型',
			dataIndex: 'paymentType',
			key: 'paymentType'
		},
		{
			title: '交款金额(元)',
			dataIndex: 'paymentAmount',
			key: 'paymentAmount'
		},
		{
			title: '交款时间',
			dataIndex: 'paymentTime',
			key: 'paymentTime'
		},
		{
			title: '附件',
			dataIndex: 'paymentAttachment',
			key: 'paymentAttachment'
		}
	])

	// 打开抽屉
	const onOpen = (record) => {
		console.log(record, '--------')
		if (record.status !== '1') {
			//有台账信息
			visible.value = true
			if (record) {
				let recordData = cloneDeep(record)
				formData.value = Object.assign({}, recordData)
				getDetail(record.id)
			}
		} else {
			message.error('暂无详情数据！')
		}
	}
	const getDetail = (id) => {
		reParkLedgerInfoApi.getDetail({ parkId: id }).then((res) => {
			console.log(res, 'car-----')
			carInfo.value = res
		})
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		visible.value = false
	}
	// 默认要校验的
	const formRules = {}
	// 验证并提交数据
	const onSubmit = () => {
		formRef.value.validate().then(() => {
			submitLoading.value = true
			const formDataParam = cloneDeep(formData.value)
			reParkApi
				.reHouseSubmitForm(formDataParam, formDataParam.id)
				.then(() => {
					onClose()
					emit('successful')
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}
	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
