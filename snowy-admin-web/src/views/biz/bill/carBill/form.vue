<template>
	<xn-form-container
		:title="formData.id ? '编辑' : '车位认购'"
		width="50%"
		:visible="visible"
		:destroy-on-close="false"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-row :gutter="16">
				<a-divider>车位信息</a-divider>
				<a-col :span="colSpan">
					<a-form-item label="车位区域" name="areaName">
						<a-input disabled v-model:value="formData.areaName" placeholder="请输入车位区域" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="colSpan">
					<a-form-item label="车位分区" name="parkPartition">
						<a-input disabled v-model:value="formData.parkPartition" placeholder="请输入车位分区" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="colSpan">
					<a-form-item label="车位编号" name="code">
						<a-input disabled v-model:value="formData.code" placeholder="请输入车位编号" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="colSpan">
					<a-form-item label="车位类型" name="type">
						<a-input disabled v-model:value="formData.typeName" placeholder="请输入车位类型" allow-clear />
						<!-- {{ $TOOL.dictTypeData('car_type', item.type) }} -->
					</a-form-item>
				</a-col>
				<a-col :span="colSpan">
					<a-form-item label="车位总价" name="totalPrice">
						<a-input disabled v-model:value="formData.totalPrice" placeholder="请输入车位总价" allow-clear />
					</a-form-item>
				</a-col>
				<a-divider>客户信息</a-divider>
				<a-col :span="colSpan">
					<a-form-item label="村落" name="villageId">
						<a-input v-model:value="formData.villageId" placeholder="请输入村落" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="colSpan">
					<a-form-item label="顺序" name="carCode">
						<a-input v-model:value="formData.carCode" placeholder="请输入顺序" allow-clear />
					</a-form-item>
				</a-col>

				<a-col :span="colSpan">
					<a-form-item label="姓名" name="name">
						<a-input v-model:value="formData.name" placeholder="请输入姓名" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="colSpan">
					<a-form-item label="身份证号" name="idCard">
						<a-input v-model:value="formData.idCard" placeholder="请输入身份证号" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="colSpan">
					<a-form-item label="电话" name="phone">
						<a-input v-model:value="formData.phone" placeholder="请输入电话" allow-clear />
					</a-form-item>
				</a-col>
				<!-- <a-col :span="colSpan">
					<a-form-item label="地址" name="address">
						<a-input v-model:value="formData.address" placeholder="请输入地址" allow-clear />
					</a-form-item>
				</a-col> -->
				<a-col :span="colSpan">
					<a-form-item label="认购时间" name="subscribeTime">
						<a-date-picker
							v-model:value="formData.subscribeTime"
							value-format="YYYY-MM-DD"
							placeholder="请选择认购时间"
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="colSpan">
					<a-form-item label="签约日期" name="contractTime">
						<a-date-picker
							v-model:value="formData.contractTime"
							value-format="YYYY-MM-DD"
							placeholder="请选择签约日期"
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-divider>签约信息</a-divider>
				<!-- 车位 -->
				<div style="width: 100%; display: flex; flex-wrap: wrap">
					<a-col :span="18">
						<a-form-item label="执行优惠" name="discount">
							<a-input
								type="number"
								:min="0"
								v-model:value="formData.discount"
								placeholder="请输入执行优惠"
								allow-clear
								@blur="saleSignDisBlur"
								:suffix="formData.discountType == 1 ? '%' : '元'"
							>
								<template #addonAfter>
									<a-select v-model:value="formData.discountType" style="width: 80px" @change="saleSignDisBlur">
										<a-select-option value="1">折扣</a-select-option>
										<a-select-option value="2">直减</a-select-option>
									</a-select>
								</template>
							</a-input>
						</a-form-item>
					</a-col>
					<a-col :span="24">
						<a-form-item label="优惠备注" name="remark">
							<a-input v-model:value="formData.remark" placeholder="请输入优惠备注" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="colSpan">
						<a-form-item label="签约总价" name="contractPrice">
							<a-input
								type="number"
								:min="0"
								disabled
								placeholder="请输入签约总价"
								v-model:value="formData.contractPrice"
								suffix="元"
							/>
						</a-form-item>
					</a-col>
				</div>
				<a-divider>交款信息</a-divider>
				<a-col :span="24">
					<a-form-item label="付款记录" name="positionJson">
						<a-row :gutter="10" class="form-row">
							<a-col :span="2" class="form-row-con"> 序号 </a-col>
							<a-col :span="3" class="form-row-con"> 交款类型 </a-col>
							<a-col :span="4" class="form-row-con"> 交款金额 </a-col>
							<a-col :span="4" class="form-row-con"> 交款时间 </a-col>
							<a-col :span="4" class="form-row-con"> 附件 </a-col>
							<a-col :span="3" class="form-row-con"> 操作 </a-col>
							<a-col :span="3" class="form-row-con">
								<a-button type="primary" @click="addPay" size="small">
									<PlusOutlined />
									增加
								</a-button>
							</a-col>
						</a-row>
						<div :key="positionInfo" v-for="(item, index) in formData.paymentInfoList">
							<a-row :gutter="10">
								<a-col :span="2">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ index + 1 }}</div>
									</a-form-item>
								</a-col>
								<a-col :span="3">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">
											{{ $TOOL.dictTypeData('payment', item.paymentType) }}
										</div>
									</a-form-item>
								</a-col>
								<a-col :span="4">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.paymentAmount }}</div>
									</a-form-item>
								</a-col>

								<a-col :span="4">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.paymentTime }}</div>
									</a-form-item>
								</a-col>
								<a-col :span="4">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px" v-if="item.paymentAttachment">
											<img :src="item.paymentAttachment" style="width: 60px; height: 60px" alt="" />
										</div>
									</a-form-item>
								</a-col>
								<a-col :span="3" class="xn-mt4">
									<div style="width: 100%; display: flex; justify-content: center">
										<a-button size="small" type="text" danger ghost @click="editPay(1, index, item)">编辑</a-button>
										<a-popconfirm title="确定要删除此记录吗？" @confirm="delPay(1, index, item.id)">
											<a-button size="small" type="text" danger>移除</a-button>
										</a-popconfirm>
									</div>
								</a-col>
							</a-row>
						</div>
					</a-form-item>
				</a-col>
				<a-col :span="colSpan">
					<a-form-item label="合计交款:" name="PaymentTotal">
						<a-input
							type="number"
							:min="0"
							disabled
							v-model:value="formData.PaymentTotal"
							placeholder="请输入合计交款:"
							allow-clear
							suffix="元"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="colSpan">
					<a-form-item label="欠款金额" name="arrears">
						<a-input
							type="number"
							:min="0"
							disabled
							v-model:value="formData.arrears"
							placeholder="请输入欠款金额"
							allow-clear
							suffix="元"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="colSpan">
					<a-form-item label="结清状态" name="settleStatus">
						<a-input disabled v-model:value="formData.settleStatus" placeholder="请输入结清状态" />
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
		</template>
	</xn-form-container>
	<a-modal :keyboard="false" :maskClosable="false" v-model:visible="payVisible" :title="userTitle" @ok="payHandleOk">
		<a-form
			ref="payForm"
			:model="payData"
			name="basic"
			style="margin-top: 30px"
			:rules="payRules"
			:label-col="{ span: 5 }"
			:wrapper-col="{ span: 16 }"
			autocomplete="off"
		>
			<a-form-item label="交款类型:" name="paymentType">
				<a-select v-model:value="payData.paymentType" style="width: 100%" placeholder="请选择交款类型">
					<a-select-option :value="item.value" v-for="item in paymentType">{{ item.label }}</a-select-option>
				</a-select>
			</a-form-item>

			<a-form-item label="交款金额:" name="paymentAmount">
				<a-input
					type="number"
					:min="0"
					v-model:value="payData.paymentAmount"
					suffix="元"
					placeholder="请输入交款金额"
				/>
			</a-form-item>
			<a-form-item label="付款时间:" name="paymentTime">
				<a-date-picker
					v-model:value="payData.paymentTime"
					value-format="YYYY-MM-DD"
					placeholder="请选择付款时间"
					style="width: 100%"
				/>
			</a-form-item>
			<a-form-item label="附件:" name="paymentAttachment">
				<xn-upload v-if="payVisible" v-model:value="payData.paymentAttachment" uploadMode="image" />
			</a-form-item>
		</a-form>
	</a-modal>
</template>

<script setup name="reLeaseContractForm">
	import { cloneDeep } from 'lodash-es'
	import { required, rules } from '@/utils/formRules'
	import reParkLedgerInfoApi from '@/api/biz/reParkLedgerInfoApi'
	import reCustomerApi from '@/api/biz/reCustomerApi'
	import reCustomerInfoApi from '@/api/biz/reCustomerInfoApi' //客户信息
	import reParkContractApi from '@/api/biz/reParkContractApi'
	import rePaymentInfoApi from '@/api/biz/rePaymentInfoApi'
	import reLedgerInfoApi from '@/api/biz/reLedgerInfoApi'
	import reSalesContractApi from '@/api/biz/reSalesContractApi' //销售签约
	import reLeaseContractApi from '@/api/biz/reLeaseContractApi' //租赁签约
	import rePlacementContractApi from '@/api/biz/rePlacementContractApi' //安置签约
	import reContractInfoApi from '@/api/biz/reContractInfoApi' //合同信息
	import reParkAreaApi from '@/api/biz/reParkAreaApi'
	import tool from '@/utils/tool'
	import { useMenuStore } from '@/store/menu'
	const menuStore = useMenuStore()

	import { message } from 'ant-design-vue'

	const paymentType = tool.dictList('payment')
	const carType = tool.dictList('car_type')

	// 抽屉状态
	const visible = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	const userForm = ref()
	const payForm = ref()
	const colSpan = ref(8)
	// 表单数据
	const formData = ref({
		paymentRecordIds: []
	})
	const userData = ref({})
	const payData = ref({})
	const submitLoading = ref(false)
	const userVisible = ref(false)
	const payVisible = ref(false)
	const userTitle = ref('')
	const userType = ref(0)
	const userIndex = ref(0)
	const payTitle = ref('')
	const payIndex = ref(0)

	// 打开抽屉
	const onOpen = (record, type) => {
		// 新增
		if (type == 1) {
			record.parkId = record.id
			delete record.id
			let recordData = cloneDeep(record)
			formData.value = Object.assign({}, recordData)
			formData.value.paymentInfoList = []
			formData.value.contractPrice = record.totalPrice
			formData.value.discountType = '1'
			visible.value = true
			getCarType(record.type)
		}
		// 修改
		if (type == 2) {
			record.parkId = record.id
			delete record.id
			let recordData = cloneDeep(record)
			reParkLedgerInfoApi.getDetail({ parkId: record.parkId }).then((res) => {
				getParkArea(res.ledgerInfo.areaId)
				// 序列化数据
				let infoData = {
					billId: res.ledgerInfo.id,
					villageId: res.customerInfo.villageId,
					carCode: res.customerInfo.code,
					subscribeTime: res.customerInfo.subscribeTime,
					name: res.ledgerInfo.name,
					idCard: res.ledgerInfo.idCard,
					phone: res.ledgerInfo.phone,
					contractTime: res.customerInfo.contractTime,
					address: res.customerInfo.address,
					discount: res.parkSign.discount,
					discountType: res.parkSign.discountType,
					remark: res.parkSign.remark,
					contractPrice: res.parkSign.contractPrice,

					paymentInfoList: res.paymentList || [],
					parkSignId: res.parkSign.id,
					id: res.customerInfo.id,
					customerId: res.customerInfo.customerId
				}
				formData.value = Object.assign({}, { ...recordData, ...infoData })
				visible.value = true
				computedTotal()
				getCarType(record.type)
			})
		}
	}

	const getParkArea = (e) => {
		reParkAreaApi
			.reParkAreaPage({
				projectId: menuStore.projectObj.id
			})
			.then((res) => {
				res.records.forEach(el=>{
					if(el.id==e) {
						formData.value.areaName = el.code
					}
				})
			})
	}

	//车位类型
	const getCarType = (e) => {
		carType.forEach((el) => {
			if (el.value == e) {
				formData.value.typeName = el.label
			}
		})
	}

	// 关闭抽屉
	const onClose = () => {
		visible.value = false
		formRef.value.resetFields()
		// formData.value = {}
	}
	// 默认要校验的
	const formRules = {
		villageId: [{ required: true, message: '请输入村落编号', trigger: 'change' }],
		carCode: [{ required: true, message: '请输入序号', trigger: 'change' }],
		subscribeTime: [{ required: true, message: '请选择认购时间', trigger: 'change' }],
		name: [{ required: true, message: '请输入姓名', trigger: 'change' }],
		idCard: [{ required: true, message: '请输入身份证号', trigger: 'change' }, rules.idCard],
		phone: [{ required: true, message: '请输入电话', trigger: 'change' }, rules.phone]
	}

	// 支付要校验的
	const payRules = {
		paymentAmount: [{ required: true, message: '请输入交款金额', trigger: 'change' }],
		paymentType: [{ required: true, message: '请选择交款类型', trigger: 'change' }],
		paymentTime: [{ required: true, message: '请选择交款时间', trigger: 'change' }]
	}
	// 验证并提交数据
	const onSubmit = () => {
		formRef.value.validate().then(async () => {
			submitLoading.value = true
			const formDataParam = cloneDeep(formData.value)
			//处理数据
			// 付款信息  交款信息
			let paymentRecordIds = formDataParam.paymentInfoList.map((obj) => obj.id)
			let parasmData = {
				ledgerId: formDataParam.billId,
				id: formDataParam.id,
				parkId: formDataParam.parkId,
				customerInfo: {
					villageId: formDataParam.villageId,
					code: formDataParam.carCode,
					address: formDataParam.address,
					subscribeTime: formDataParam.subscribeTime,
					contractTime: formDataParam.contractTime,
					name: formDataParam.name,
					idCard: formDataParam.idCard,
					phone: formDataParam.phone,
					parkId: formDataParam.parkId,
					parkingLedgerId: formDataParam.billId,
					id: formDataParam.customerId,
					customerId: formDataParam.customerId,
					isLedger: true
				},
				parkSign: {
					discount: formDataParam.discount,
					discountType: formDataParam.discountType,
					remark: formDataParam.remark,
					contractPrice: formDataParam.contractPrice,
					totalHousePrice: formDataParam.contractPrice,
					parkId: formDataParam.parkId,
					ledgerId: formDataParam.billId,
					id: formDataParam.parkSignId
				},
				paymentRecordIds: paymentRecordIds.toString() //分期付款Ids
			}
			parasmData.customerInfo.code = formDataParam.carCode
			// submitLoading.value = false
			//修改 分模块单个修改
			if (parasmData.ledgerId) {
				try {
					//修改客户信息
					await reCustomerInfoApi.reCustomerInfoSubmitForm(
						parasmData.customerInfo,
						parasmData.customerInfo.customerId,
						2
					)
					//修改客户管理信息
					let pars = cloneDeep(parasmData.customerInfo)
					pars.id = parasmData.id
					await reCustomerApi.reCustomerSubmitForm(pars, pars.id, 2)

					//修改签约信息
					await reParkContractApi.reParkContractSubmitForm(parasmData.parkSign, parasmData.parkSign.id, 2)
					onClose()
					setTimeout(() => {
						emit('successful')
						message.success('修改成功')
					}, 500)
				} catch (error) {
					message.error('修改失败')
				} finally {
					submitLoading.value = false
				}
			} else {
				reParkLedgerInfoApi
					.addSubmitForm(parasmData, parasmData.id)
					.then(() => {
						onClose()
						emit('successful')
					})
					.finally(() => {
						submitLoading.value = false
					})
			}
		})
	}

	//添加支付
	const addPay = (e) => {
		payTitle.value = '付款记录添加'
		payData.value = {}
		payVisible.value = true
	}
	//修改支付
	const editPay = (e, index, item) => {
		payIndex.value = index
		payData.value = cloneDeep(item)
		payVisible.value = true
	}

	// 删减支付行
	const delPay = (e, index, id) => {
		payIndex.value = index
		computedTotal()
		deletePay({ id })
	}

	// 删除支付
	const deletePay = (record) => {
		let params = [
			{
				id: record.id,
				extJson: 'park'
			}
		]
		rePaymentInfoApi.rePaymentInfoDelete(params).then(() => {
			// table.value.refresh(true)
			formData.value.paymentInfoList.splice(payIndex.value, 1)
			computedTotal()
		})
	}

	//付款添加确定
	const payHandleOk = () => {
		payForm.value.validate().then(() => {
			const formDataParam = cloneDeep(payData.value)
			formDataParam.extJson = 'park'
			formDataParam.type = '2'
			formDataParam.customerId = formData.value.customerId || null
			formDataParam.contractId = formData.value.parkSignId || null
			formDataParam.ledgerId = formData.value.billId || null
			formData.value.paymentInfoList
			if (formData.value.paymentInfoList.length > 0) {
				let total = 0
				formData.value.paymentInfoList.forEach((el) => {
					total += Number(el.paymentAmount)
				})

				if (Number(formDataParam.paymentAmount) + total > Number(formData.value.contractPrice)) {
					message.error('付款金额不能大于总价')
					return
				}
			} else {
				if (Number(formDataParam.paymentAmount) > Number(formData.value.contractPrice)) {
					message.error('付款金额不能大于总价')
					return
				}
			}

			rePaymentInfoApi
				.rePaymentInfoSubmitForm(formDataParam, formDataParam.id)
				.then((res) => {
					//修改
					if (formDataParam.id) {
						formData.value.paymentInfoList[payIndex.value] = formDataParam
						computedTotal()
					} else {
						formDataParam.id = res
						// 付款记录
						formData.value.paymentInfoList.push(formDataParam)
						computedTotal()
					}
				})
				.finally(() => {
					payVisible.value = false
					payForm.value.resetFields()
				})
		})
	}
	// 以下是计算---------------------------------------------------------------------

	const saleSignDisBlur = (e) => {
		if (formData.value.discountType == '1') {
			// 签约总价
			let price = formData.value.totalPrice * ((formData.value.discount ? formData.value.discount : 100) / 100)
			formData.value.contractPrice = Number(price.toFixed(2))
		} else {
			// 签约总价
			let price = formData.value.totalPrice - (formData.value.discount ? formData.value.discount : 0)
			formData.value.contractPrice = Number(price.toFixed(2))
		}
		// 计算欠款金额 和状态
		computedStauts()
	}

	// 算交款信息的合计
	const computedTotal = () => {
		//算房款记录合计

		if (formData.value.paymentInfoList.length > 0) {
			let total = 0
			formData.value.paymentInfoList.forEach((el) => {
				total += Number(el.paymentAmount)
			})
			formData.value.PaymentTotal = total
		} else {
			formData.value.PaymentTotal = 0
		}
		// 计算欠款金额 和状态
		computedStauts()
	}

	const computedStauts = () => {
		// 🔧 修复：优先使用后端返回的欠款，没有则前端计算（包含定金）
		const contractPrice = Number(formData.value.contractPrice) || 0;
		const paymentTotal = Number(formData.value.PaymentTotal || 0);

		// 如果后端已经提供了欠款金额，直接使用
		if (formData.value.debt !== undefined && formData.value.debt !== null) {
			formData.value.arrears = Number(formData.value.debt).toFixed(2);
			console.log('🔧 使用后端返回的欠款金额：', formData.value.arrears);
		} else {
			// 后端没有提供欠款时，前端计算（包含定金）
			// 计算定金总额（paymentType=1为定金，paymentType=2为房款）
			let depositAmount = 0;
			if (formData.value.paymentInfoList && formData.value.paymentInfoList.length > 0) {
				depositAmount = formData.value.paymentInfoList
					.filter(payment => payment.paymentType === '1') // 支付类型：定金
					.reduce((sum, payment) => sum + (Number(payment.paymentAmount) || 0), 0);
			}

			// 计算欠款：签约价 - 已交款 - 定金
			const calculatedArrears = contractPrice - paymentTotal - depositAmount;
			formData.value.arrears = calculatedArrears.toFixed(2);

			console.log('🔧 前端计算车位账单欠款：', {
				contractPrice,
				paymentTotal,
				depositAmount,
				calculatedArrears: formData.value.arrears
			});
		}

		// 设置结清状态
		if (formData.value.arrears > 0) {
			formData.value.settleStatus = '未结清'
		}
		if (formData.value.arrears <= 0) {
			formData.value.settleStatus = '已结清'
		}
	}

	// 抛出函数
	defineExpose({
		onOpen
	})
</script>

<style scoped lang="less">
	.form-row {
		background-color: var(--item-hover-bg);
		margin-left: 0 !important;
		margin-bottom: 10px;
	}
	.form-row-con {
		padding-bottom: 5px;
		padding-top: 5px;
		padding-left: 15px;
		text-align: center;
	}
</style>
<style>
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none;
	}
	input[type='number'] {
		-moz-appearance: textfield;
	}
</style>
