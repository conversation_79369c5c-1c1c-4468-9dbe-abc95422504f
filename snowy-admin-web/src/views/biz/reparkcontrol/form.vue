<template>
	<xn-form-container
		:title="title"
		:width="1000"
		:visible="visible"
		:destroy-on-close="true"
		:footer-style="{ textAlign: 'right' }"
		@close="onClose"
	>
		<a-form ref="formRef" layout="horizontal">
			<h3>房源信息</h3>
			<a-row :gutter="16">
				<a-col :span="8">
					<a-form-item label="楼号：">
						<a-input v-model:value="houseData.code" placeholder="请输入楼号" allow-clear disabled />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="楼层：">
						<a-input v-model:value="houseData.floor" placeholder="请输入楼层" allow-clear disabled />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="房号：">
						<a-input-number
							v-model:value="houseData.houseNum"
							:min="1"
							placeholder="请输入房号"
							allow-clear
							style="width: 100%"
							disabled
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="建筑面积：">
						<a-input
							v-model:value="houseData.buildArea"
							placeholder="请输入建筑面积"
							allow-clear
							addon-after="㎡"
							disabled
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="连廊面积：">
						<a-input
							v-model:value="houseData.corridorArea"
							placeholder="请输入连廊面积"
							allow-clear
							addon-after="㎡"
							disabled
						/>
					</a-form-item>
				</a-col>
			</a-row>
			<h3>客户信息</h3>
			<a-row :gutter="16">
				<a-col :span="24">
					<a-form-item label="入住企业：" v-bind="validateInfos['customer.company']">
						<a-input v-model:value="formData.customer.company" placeholder="请输入入住企业" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="企业法人：" >
						<a-input v-model:value="formData.customer.legalPerson" placeholder="请输入企业法人" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="身份证号：" v-bind="validateInfos['customer.idCard']">
						<a-input v-model:value="formData.customer.idCard" placeholder="请输入身份证号" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="8"></a-col>
				<a-col :span="8">
					<a-form-item label="企业联络人：" v-bind="validateInfos['customer.contactPerson']">
						<a-input v-model:value="formData.customer.contactPerson" placeholder="请输入企业联络人" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="联系电话：" v-bind="validateInfos['customer.phone']">
						<a-input v-model:value="formData.customer.phone" placeholder="请输入联系电话" allow-clear />
					</a-form-item>
				</a-col>
			</a-row>
			<h3>签约信息</h3>
			<a-row :gutter="16">
				<a-col :span="8">
					<a-form-item label="物业类别：">
						<a-input v-model:value="formData.contract.propertyType" placeholder="请输入物业类别" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="物业现状：">
						<a-input v-model:value="formData.contract.propertyStatus" placeholder="请输入物业现状" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="签约日期：">
						<a-date-picker
							v-model:value="formData.contract.contractTime"
							style="width: 100%"
							value-format="YYYY-MM-DD"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="租赁合同：">
						<a-radio-group v-model:value="formData.contract.leaseContract" :options="leaseAgreement" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="入区协议：">
						<a-radio-group v-model:value="formData.contract.entryAgreement" :options="entryAgreement" />
					</a-form-item>
				</a-col>
				<a-col :span="8"></a-col>
				<a-col :span="8">
					<a-form-item label="租赁价格：">
						<a-input
							v-model:value="formData.contract.leasePrice"
							placeholder="请输入租赁价格"
							allow-clear
							addon-after="元/月/㎡"
							@change="changePrice"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="连廊租金：">
						<a-input
							v-model:value="formData.contract.corridorRent"
							placeholder="请输入连廊租金"
							allow-clear
							addon-after="元"
							@change="changePrice"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="月租金：">
						<a-input
							v-model:value="formData.contract.monthlyRent"
							placeholder="请输入月租金"
							allow-clear
							addon-after="元"
							disabled
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="租赁保证金：">
						<a-input
							v-model:value="formData.contract.leaseDeposit"
							placeholder="请输入租赁保证金"
							allow-clear
							addon-after="元"
							@change="changePrice2"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8"></a-col>
				<a-col :span="8"></a-col>
				<a-col :span="8">
					<a-form-item label="租赁起始日期：">
						<a-date-picker
							v-model:value="formData.contract.leaseStartTime"
							style="width: 100%"
							value-format="YYYY-MM-DD"
							@change="leaseStartTimeChange"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="租赁截止日期：">
						<a-date-picker
							v-model:value="formData.contract.leaseEndTime"
							style="width: 100%"
							value-format="YYYY-MM-DD"
							@change="leaseEndTimeChange"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="租赁计算日期：">
						<a-date-picker
							v-model:value="formData.contract.leaseCalculationDate"
							style="width: 100%"
							value-format="YYYY-MM-DD"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="租期：">
						<a-input-number
							disabled
							v-model:value="formData.contract.leaseTerm"
							addon-after="月"
							:min="1"
							@change="changePrice2"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="免租期：">
						<a-input-number
							v-model:value="formData.contract.rentFreePeriod"
							addon-after="月"
							:min="0"
							@change="changePrice2"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8"> </a-col>
				<a-col :span="8">
					<a-form-item label="应缴租金：">
						<a-input
							v-model:value="formData.contract.rentPayable"
							placeholder="请输入应缴租金"
							allow-clear
							addon-after="元"
							@change="changePayPrice"
							disabled
						/>
					</a-form-item>
				</a-col>

				<a-col :span="8">
					<a-form-item label="已缴租金：">
						<a-input
							v-model:value="formData.contract.rentPaid"
							placeholder="请输入已缴租金"
							allow-clear
							addon-after="元"
							@change="changePayPrice"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="欠缴租金：">
						<a-input
							v-model:value="formData.contract.rentArrears"
							placeholder="请输入欠缴租金"
							allow-clear
							disabled
							addon-after="元"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="备注：">
						<a-textarea v-model:value="formData.contract.remark" show-count :maxlength="500" placeholder="请输入备注" />
					</a-form-item>
				</a-col>
			</a-row>
			<h3>其他信息</h3>
			<a-row :gutter="16">
				<a-col :span="24">
					<a-form-item label="企业优惠政策：">
						<a-textarea
							v-model:value="formData.other.companyPolicy"
							show-count
							:maxlength="500"
							placeholder="请输入企业优惠政策"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="经营产品：">
						<a-input v-model:value="formData.other.businessProduct" placeholder="请输入经营产品" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="投资额：">
						<a-input
							v-model:value="formData.other.investmentAmount"
							placeholder="请输入投资额"
							allow-clear
							addon-after="元"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="员工人数：">
						<a-input
							v-model:value="formData.other.staffNumber"
							placeholder="请输入员工人数"
							allow-clear
							addon-after="个"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="年营业额：">
						<a-input
							v-model:value="formData.other.annualTurnover"
							placeholder="请输入年营业额"
							allow-clear
							addon-after="元"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="配套需求：">
						<a-textarea
							v-model:value="formData.other.supportingRequirements"
							show-count
							:maxlength="500"
							placeholder="请输入配套需求"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="存在问题：">
						<a-textarea
							v-model:value="formData.other.existingProblems"
							show-count
							:maxlength="500"
							placeholder="请输入存在问题"
						/>
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
			<a-button type="primary" @click.prevent="onSubmit" :loading="submitLoading">保存</a-button>
		</template>
	</xn-form-container>
</template>

<script setup name="reHouseForm">
	import { useMenuStore } from '@/store/menu'
	const menuStore = useMenuStore()
	import { message } from 'ant-design-vue'
	import { reactive, toRaw } from 'vue'
	import { Form } from 'ant-design-vue'
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import reGardenledGerinfo from '@/api/biz/reGardenledGerinfo'
	import tool from '@/utils/tool'
	// 园区入区协议
	const entryAgreement = tool.dictList('entry_agreement')
	// 园区租赁合同
	const leaseAgreement = tool.dictList('lease_agreement')
	// 抽屉状态
	const visible = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()

	const title = ref(null)
	const useForm = Form.useForm
	const houseData = ref({})
	// 表单数据
	const formData = reactive({
		customer: {
			company: '',
			legalPerson: '',
			idCard: '',
			phone: '',
			contactPerson: ''
		},
		contract: {
			monthlyRent: 0,
			rentArrears: 0,
			leaseTerm: 0,
			rentFreePeriod: 0,
			leaseContract: '0',
			entryAgreement: '0'
		},
		other: {},
		projectId: null
	})

	const { resetFields, validate, validateInfos } = useForm(
		formData,
		reactive({
			'customer.company': [{ required: true, message: '请输入入住企业' }],
			'customer.legalPerson': [{ required: true, message: '请输入企业法人' }],
			'customer.idCard': [
				{ required: true, message: '请输入身份证号' },
				{
					pattern: /^[1-9]\d{5}(?:18|19|20)\d{2}(?:0[1-9]|10|11|12)(?:0[1-9]|[1-2]\d|30|31)\d{3}[\dXx]$/,
					message: '请输入正确的身份证号'
				}
			],
			'customer.phone': [
				{ required: true, message: '请输入联系电话' },
				{
					pattern:
						/^(?:(?:\+|00)86)?1(?:(?:3[\d])|(?:4[5-79])|(?:5[0-35-9])|(?:6[5-7])|(?:7[0-8])|(?:8[\d])|(?:9[01256789]))\d{8}$/,
					message: '请输入正确的联系电话'
				}
			],
			'customer.contactPerson': [{ required: true, message: '请输入企业联络人' }]
		})
	)
	const submitLoading = ref(false)

	// 打开抽屉
	const onOpen = async (record) => {
		console.log(record, 'record2 --------')
		if (record) {
			let recordData = cloneDeep(record)
			houseData.value = Object.assign({}, recordData)
			formData.houseId = recordData.id
			formData.projectId = menuStore.projectObj.id
			formData.buildCode = recordData.code
			visible.value = true
		}
		switch (record.type) {
			case '新增':
				title.value = '新增台账信息'
				delete formData.id
				break
			case '续租':
				title.value = '台账信息续租'
				getInfo(record.id)
				break
			case '补充':
				title.value = '台账信息补充'
				getInfo(record.id)
				break
		}
	}

	const getInfo = (id) => {
		reGardenledGerinfo.reGardenledgerInfoID({ id: id }).then((res) => {
			//console.log(formData, res, 'res --------')
			if (res.customer) {
				formData.customer = res.customer
			} else {
				formData.customer = {}
			}
			if (res.contract) {
				formData.contract = res.contract
			} else {
				formData.contract = {}
			}
			if (res.other) {
				formData.other = res.other
			} else {
				formData.other = {}
			}
			if (res.history && res.history.length > 0) {
				formData.history = res.history
			} else {
				formData.history = {}
			}

			formData.id = res.id
		})
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		visible.value = false
		resetFields()
	}
	// 验证并提交数据
	const onSubmit = () => {
		validate()
			.then((res) => {
				submitLoading.value = true
				const formDataParam = cloneDeep(formData)
				delete formDataParam.history
				switch (title.value) {
					case '新增台账信息':
						addForm(formDataParam)
						break
					case '台账信息续租':
						reletForm(formDataParam)
						break
					case '台账信息补充':
						uploadForm(formDataParam)
						break
				}
			})
			.catch((err) => {
				console.log('error', err)
			})
	}

	const addForm = (formDataParam) => {
		console.log(houseData.value, 'houseData.valuehouseData.value')

		reGardenledGerinfo
			.AddreGardenledger({
				...formDataParam,
				projectId: menuStore.projectObj.id,
				buildCode: houseData.value.code,
				corridorArea: houseData.value.corridorArea
			})
			.then(() => {
				onClose()
				emit('successful')
				message.success('添加成功')
			})
			.finally(() => {
				submitLoading.value = false
			})
	}

	const reletForm = (formDataParam) => {
		reGardenledGerinfo
			.reletGardenledgerInfo({ ...formDataParam, projectId: menuStore.projectObj.id })
			.then(() => {
				onClose()
				emit('successful')
				message.success('续租成功')
			})
			.finally(() => {
				submitLoading.value = false
			})
	}
	const uploadForm = (formDataParam) => {
		reGardenledGerinfo
			.uploadGardenledgerInfo({ ...formDataParam, projectId: menuStore.projectObj.id })
			.then(() => {
				onClose()
				emit('successful')
				message.success('更新成功')
			})
			.finally(() => {
				submitLoading.value = false
			})
	}

	const changePrice = () => {
		if (formData.contract.leasePrice && formData.contract.corridorRent) {
			formData.contract.monthlyRent =
				Number(formData.contract.leasePrice) * Number(houseData.value.buildArea) +
				Number(formData.contract.corridorRent)
		} else if (formData.contract.leasePrice) {
			formData.contract.monthlyRent = Number(formData.contract.leasePrice) * Number(houseData.value.buildArea)
		} else if (formData.contract.corridorRent) {
			formData.contract.monthlyRent = Number(formData.contract.corridorRent)
		} else {
			formData.contract.monthlyRent = 0
		}
		changePrice2()
	}

	const changePrice2 = () => {
		if (formData.contract.leaseDeposit && formData.contract.monthlyRent) {
			formData.contract.rentPayable =
				formData.contract.monthlyRent * (formData.contract.leaseTerm - formData.contract.rentFreePeriod) +
				Number(formData.contract.leaseDeposit||0)
		}
	}

	const changePayPrice = () => {
		if (formData.contract.rentPaid) {
			formData.contract.rentArrears = Number(formData.contract.rentPayable) - Number(formData.contract.rentPaid)
		} else {
			formData.contract.rentArrears = formData.contract.rentPayable
		}
	}

	const leaseStartTimeChange = (e) => {
		if (formData.contract.leaseEndTime) {
			// 计算两个日期之间的月数
			let month = getMonth(e, formData.contract.leaseEndTime)
			formData.contract.leaseTerm = month
		}
		changePrice2()
	}

	const leaseEndTimeChange = (e) => {
		if (formData.contract.leaseStartTime) {
			let month = getMonth(formData.contract.leaseStartTime, e)
			formData.contract.leaseTerm = month
		}
		changePrice2()
	}

	const getMonth = (date1, date2) => {
		const dateOne = new Date(date1)
		const dateTwo = new Date(date2)
		// 第一个日期的年和月
		const yearOne = dateOne.getFullYear()
		const monthOne = dateOne.getMonth() + 1
		// 第二个日期的年和月
		const yearTwo = dateTwo.getFullYear()
		const monthTwo = dateTwo.getMonth() + 1
		// 两个日期的月份数
		const oneMonthNum = yearOne * 12 + monthOne
		const twoMonthNum = yearTwo * 12 + monthTwo
		return Math.abs(oneMonthNum - twoMonthNum)
	}
	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
