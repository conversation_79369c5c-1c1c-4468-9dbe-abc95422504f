<template>
    <xn-form-container title="详情" width="65%" :open="visible" :destroy-on-close="false" @close="onClose">
        <!-- <a-drawer v-model:open="open" class="custom-class" root-class-name="root-class-name" title="详情"
            placement="right" @after-open-change="afterOpenChange" width="60%"> -->
        <a-from :model="detailForm">
            <h3 class="drawTitle">客户信息</h3>
            <a-row class="drawRow">
                <a-col :span="5">
                    <a-form-item label="村落编号" name="villageId">
                        <span>{{ detailForm.customerInfo.villageId }}</span>
                    </a-form-item>
                </a-col>
                <a-col :span="5">
                    <a-form-item label="选房序号">{{ detailForm.customerInfo.code }}</a-form-item>
                </a-col>
                <a-col :span="5">
                    <a-form-item label="姓名">{{ detailForm.customerInfo.personInfo.name }}</a-form-item>
                </a-col>
                <a-col :span="5">
                    <a-form-item label="身份证号">{{ detailForm.customerInfo.personInfo.idCard }}</a-form-item>
                </a-col>
                <a-col :span="5">
                    <a-form-item label="电话">{{ detailForm.customerInfo.personInfo.phone }}</a-form-item>
                </a-col>
                <a-col :span="5">
					<a-form-item label="销售单价" name="salesUnitPrice">
						<span>{{ rentAndSaleInfo.salesUnitPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="销售总价" name="salesTotalPrice">
						<span>{{ rentAndSaleInfo.salesTotalPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="租赁单价" name="leaseUnitPrice">
						<span>{{ rentAndSaleInfo.leaseUnitPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="租赁总价" name="leaseTotalPrice">
						<span>{{ rentAndSaleInfo.leaseTotalPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="4">
					<a-form-item label="安置单价" name="placementPrice">
						<span>{{ rentAndSaleInfo.placementPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="市场单价" name="marketPrice">
						<span>{{ rentAndSaleInfo.marketPrice || '-' }}元/㎡</span>
					</a-form-item>
				</a-col>
                <a-col :span="24">
                    <a-form-item label="地址">{{ detailForm.customerInfo.address }}</a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item>
                        <span>共有人信息：</span>
                        <a-table :dataSource="detailForm.customerInfo.shareholderList" :columns="columns1"
                            :pagination="false">
                            <!-- 自定义序号列 -->
                            <template #bodyCell="{ column, text, record, index }">
                                <!-- 当前列是序号列时，显示序号 -->
                                <span v-if="column.dataIndex === 'serialNumber'">{{ index + 1 }}</span>
                                <!-- 其他列正常显示 -->
                                <!-- <span v-else>{{ text }}</span> -->
                            </template>
                        </a-table>
                    </a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item>
                        <span>名额信息：</span>

                        <a-table :dataSource="detailForm.customerInfo.quotaList" :columns="columns1"
                            :pagination="false">
                            <!-- 自定义序号列 -->
                            <template #bodyCell="{ column, text, record, index }">
                                <!-- 当前列是序号列时，显示序号 -->
                                <span v-if="column.dataIndex === 'serialNumber'">{{ index + 1 }}</span>
                                <!-- 其他列正常显示 -->
                                <!-- <span v-else>{{ text }}</span> -->
                            </template>
                        </a-table>
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="人数">{{ detailForm.customerInfo.quotaList.length ?
                        detailForm.customerInfo.quotaList.length : 0 }}</a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="启用面积">{{ detailForm.customerInfo.enableArea ? detailForm.customerInfo.enableArea + ' ㎡'
                        : '暂无面积信息' }}
                    </a-form-item>
                </a-col>
            </a-row>
            <h3 class="drawTitle">租售信息</h3>
            <a-row class="drawRow">
                <a-col :span="8">
                    <a-form-item label="楼号">{{ detailForm.ledgerInfo.buildCode }}</a-form-item>
                </a-col :span="8">
                <a-col :span="8">
                    <a-form-item label="单元">{{ rentAndSaleInfo.unit }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="楼层">{{ rentAndSaleInfo.floor }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="房号">{{ rentAndSaleInfo.houseNumber }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="套内面积">{{ rentAndSaleInfo.forecastHouseArea }}</a-form-item>
                </a-col>
                <a-col :span="8">
                </a-col>
                <a-col :span="8">
                    <a-form-item label="市场价">{{ rentAndSaleInfo.marketPrice }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="安置价">{{ rentAndSaleInfo.placementPrice }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="认购时间">{{ rentAndSaleInfo.createTime }}</a-form-item>
                </a-col>
            </a-row>


            <h3 class="drawTitle">签约信息</h3>
            <!-- 销售签约 -->
            <a-row class="drawRow" v-if="detailForm.saleSign">
                <a-col :span="12">
                    <a-form-item label="销售单价">
                        {{ detailForm.saleSign.unitPrice }}
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="销售总价">{{ detailForm.saleSign.totalPrice }}</a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="执行优惠"> <span
                            style="color: blue;font-size: 10px;">{{
                                detailForm.saleSign.discountType == "1" ? '折扣' : '无'
                            }}</span></a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="优惠备注">{{ detailForm.saleSign.discountRemark }}</a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="签约总价">{{ detailForm.saleSign.contractPrice }}</a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="签约单价">{{ detailForm.saleSign.contractUnitPrice }}</a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="维修基金">{{ detailForm.saleSign.maintenanceFundUnitPrice }}</a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="维修基金">{{ detailForm.saleSign.maintenanceFund }}</a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="付款方式">
                        {{ $TOOL.dictTypeData('pay_type', detailForm.saleSign.paymentMethod) }}
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="定金">{{ detailForm.saleSign.earnestMoney }}</a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="房款合计">{{ detailForm.saleSign.totalHousePrice }}</a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item>
                        <span>分期付款：</span>
                        <a-table :dataSource="detailForm.saleSign.paymentInfoList" :columns="columns3">
                            <template #bodyCell="{ column, text, record, index }">
                                <!-- 当前列是序号列时，显示序号 -->
                                <span v-if="column.dataIndex === 'serialNumber'">{{ index + 1 }}</span>
                                <!-- 其他列正常显示 -->
                            </template>
                        </a-table>
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- 安置签约 -->
            <a-row class="drawRow" v-if="detailForm.placeSign">
                <a-col :span="12">
                    <a-form-item label="基准价">
                        {{ detailForm.placeSign.benchmarkPrice }}
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="小计">{{ detailForm.placeSign.subtotal }}</a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="执行优惠"> <span
                            style="color: blue;font-size: 10px;">{{
                                detailForm.placeSign.discountType == "1" ? '折扣' : '无'
                            }}</span></a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="优惠备注">{{ detailForm.placeSign.discountRemark }}</a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="签约总价">{{ detailForm.placeSign.contractPrice }}</a-form-item>
                </a-col>
                <!-- <a-col :span="12">
                    <a-form-item label="签约单价">{{ detailForm.placeSign.contractUnitPrice }}</a-form-item>
                </a-col> -->
                <!-- <a-col :span="12">
                    <a-form-item label="维修基金">{{ detailForm.placeSign.maintenanceFundUnitPrice }}</a-form-item>
                </a-col> -->
                <a-col :span="12">
                    <a-form-item label="维修基金">{{ detailForm.placeSign.maintenanceFund }}</a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="付款方式">
                        {{ $TOOL.dictTypeData('pay_type', detailForm.placeSign.paymentMethod) }}
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="定金">{{ detailForm.placeSign.earnestMoney }}</a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="房款合计">{{ detailForm.placeSign.totalHousePrice }}</a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item>
                        <span>分期付款：</span>
                        <a-table :dataSource="detailForm.placeSign.paymentInfoList" :columns="columns3">
                            <template #bodyCell="{ column, text, record, index }">
                                <!-- 当前列是序号列时，显示序号 -->
                                <span v-if="column.dataIndex === 'serialNumber'">{{ index + 1 }}</span>
                                <!-- 其他列正常显示 -->
                            </template>
                        </a-table>
                    </a-form-item>
                </a-col>
            </a-row>

            <!-- 租赁签约 -->
            <a-row class="drawRow" v-if="detailForm.leaseSign">
                <a-col :span="12">
                    <a-form-item label="租赁单价">
                        {{ detailForm.leaseSign.leaseUnitPrice }}
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="租赁总价">{{ detailForm.leaseSign.leaseTotalPrice }}</a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="执行优惠"> <span
                            style="color: blue;font-size: 10px;">{{
                                detailForm.leaseSign.discountType == "1" ? '折扣' : '无'
                            }}</span></a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="优惠备注">{{ detailForm.leaseSign.discountRemark }}</a-form-item>
                </a-col>
                <!-- <a-col :span="12">
                    <a-form-item label="签约总价">{{ detailForm.leaseSign.contractPrice }}</a-form-item>
                </a-col> -->
                <!-- <a-col :span="12">
                    <a-form-item label="签约单价">{{ detailForm.leaseSign.contractUnitPrice }}</a-form-item>
                </a-col> -->
                <!-- <a-col :span="12">
                    <a-form-item label="维修基金">{{ detailForm.leaseSign.maintenanceFundUnitPrice }}</a-form-item>
                </a-col> -->
                <!-- <a-col :span="12">
                    <a-form-item label="维修基金">{{ detailForm.leaseSign.maintenanceFund }}</a-form-item>
                </a-col> -->
                <a-col :span="24">
                    <a-form-item label="付款方式">
                        {{ $TOOL.dictTypeData('pay_type', detailForm.leaseSign.paymentMethod) }}
                    </a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="定金">{{ detailForm.leaseSign.earnestMoney }}</a-form-item>
                </a-col>
                <a-col :span="12">
                    <a-form-item label="房款合计">{{ detailForm.leaseSign.totalHousePrice }}</a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item>
                        <span>分期付款：</span>
                        <a-table :dataSource="detailForm.leaseSign.paymentInfoList" :columns="columns3">
                            <template #bodyCell="{ column, text, record, index }">
                                <!-- 当前列是序号列时，显示序号 -->
                                <span v-if="column.dataIndex === 'serialNumber'">{{ index + 1 }}</span>
                                <!-- 其他列正常显示 -->
                            </template>
                        </a-table>
                    </a-form-item>
                </a-col>
            </a-row>


            <h3 class="drawTitle">交款信息</h3>
            <a-row class="drawRow">
                <a-col :span="8">
                    <a-form-item label="合计交款">{{allAmount}}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="欠款金额">{{ debtAmount }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="结清状态">{{ debtAmount > 0 ? '未结清' : '已结清' }}</a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item>
                        <span>付款记录：</span>
                        <a-table :dataSource="detailForm.paymentInfos" :columns="columns2" :pagination="false">
                            <!-- 自定义序号列 -->
                            <template #bodyCell="{ column, text, record, index }">
                                <!-- 当前列是序号列时，显示序号 -->
                                <span v-if="column.dataIndex === 'serialNumber'">{{ index + 1 }}</span>
                                <!-- 其他列正常显示 -->
                                <span v-if="column.dataIndex === 'paymentType'">{{ $TOOL.dictTypeData('payment',
                                     record.paymentType) == "无此字典项" ? "":$TOOL.dictTypeData('payment',
                                    record.paymentType+'') }}</span>
                                     <span  v-if="column.dataIndex === 'paymentAttachment'">
                                    <a-image :width="100" :src="record.paymentAttachment" />
                                </span>
                            </template>
                        </a-table>
                    </a-form-item>
                </a-col>
            </a-row>
            <h3 class="drawTitle">合同信息</h3>
            <a-row class="drawRow">
                <a-col :span="8">
                    <a-form-item label="合同备案签约">{{ detailForm.contractInfo.contractRecordTime }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="合同编号">{{ detailForm.contractInfo.contractNumber }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="承诺结清日期">{{ detailForm.contractInfo.promiseSettlementDate }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="车位状态">
                        {{ $TOOL.dictTypeData('car_status', detailForm.contractInfo.parkingStatus) == "无此字典项" ? "" : $TOOL.dictTypeData('car_status', detailForm.contractInfo.parkingStatus) }}
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="发票状态">{{
                        $TOOL.dictTypeData('invoice_status', detailForm.contractInfo.invoiceStatus) == "无此字典项"? "" : $TOOL.dictTypeData('invoice_status', detailForm.contractInfo.invoiceStatus)
                    }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="办证申请表/发票">{{ detailForm.contractInfo.certificateApplication }}</a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="备注信息">{{ detailForm.contractInfo.remark }}</a-form-item>
                </a-col>
            </a-row>
        </a-from>
        <!-- </a-drawer> -->
    </xn-form-container>
</template>

<script name="Detail" setup>
import reLedgerInfoApi from '@/api/biz/reLedgerInfoApi'
import reHouseApi from '@/api/biz/reHouseApi';
const detailForm = ref({
    ledgerInfo: {},
    customerInfo: {
        personInfo: {},
        quotaList: []
    },
    contractInfo: {},
})
const rentAndSaleInfo = ref({})
//欠款金额
const debtAmount = ref('')
// 抽屉状态
const visible = ref(false)
const columns1 = [
    {
        title: '序号',
        dataIndex: 'serialNumber', // 这个dataIndex仅作为标识，不对应实际数据字段
        width: '80px', // 可以自定义宽度
        scopedSlots: { customRender: 'bodyCell' }, // 对应模板中的具名插槽
        ellipsis: true,
        align: 'center'

    },
    {
        title: '姓名',
        dataIndex: 'name',
        key: 'name',
        ellipsis: true,
        align: 'center'
    },
    {
        title: '身份证号',
        dataIndex: 'idCard',
        key: 'idCard',
        ellipsis: true,
        align: 'center'
    },
    {
        title: '电话',
        dataIndex: 'phone',
        key: 'phone',
        ellipsis: true,
        align: 'center'
    },
]
const columns2 = [
    {
        title: '序号',
        dataIndex: 'serialNumber', // 这个dataIndex仅作为标识，不对应实际数据字段
        width: '80px', // 可以自定义宽度
        scopedSlots: { customRender: 'bodyCell' }, // 对应模板中的具名插槽
        ellipsis: true,
        align: 'center'
    },
    {
        title: '付款类型',
        dataIndex: 'paymentType',
        key: 'paymentType',
        ellipsis: true,
        align: 'center'
    },
    {
        title: '交款金额（元）',
        dataIndex: 'paymentAmount',
        key: 'paymentAmount',
        ellipsis: true,
        align: 'center'
    },
    {
        title: '交款时间',
        dataIndex: 'paymentTime',
        key: 'paymentTime',
        ellipsis: true,
        align: 'center'
    },
    {
        title: '附件',
        dataIndex: 'paymentAttachment',
        key: 'paymentAttachment',
        ellipsis: true,
        align: 'center'
    }
]
const columns3 = [
    {
        title: '序号',
        dataIndex: 'serialNumber', // 这个dataIndex仅作为标识，不对应实际数据字段
        width: '80px', // 可以自定义宽度
        scopedSlots: { customRender: 'bodyCell' }, // 对应模板中的具名插槽
        ellipsis: true,
        align: 'center'
    },
    {
        title: '交款金额（元）',
        dataIndex: 'paymentAmount',
        key: 'paymentAmount',
        ellipsis: true,
        align: 'center'
    },
    {
        title: '交款时间',
        dataIndex: 'paymentTime',
        key: 'paymentTime',
        ellipsis: true,
        align: 'center'
    }
]
const allAmount = ref(0)
// 添加精确计算的工具函数
const preciseCalculate = (num1, num2, operation = 'subtract') => {
    const num1Str = String(num1 || 0)
    const num2Str = String(num2 || 0)
    const decimalLength1 = (num1Str.split('.')[1] || '').length
    const decimalLength2 = (num2Str.split('.')[1] || '').length
    const multiple = Math.pow(10, Math.max(decimalLength1, decimalLength2))
    
    if (operation === 'subtract') {
        return ((num1 * multiple - num2 * multiple) / multiple).toFixed(2)
    }
    return ((num1 * multiple + num2 * multiple) / multiple).toFixed(2)
}
// 打开抽屉
const onOpen = (record, type) => {

    reLedgerInfoApi.reLedgerInfoDetail({ id: record.id, houseType: "1" }).then(res => {
        detailForm.value = { ...detailForm.value, ...res }
        let upPay = 0
        // 计算欠款金额
        if (detailForm.value.paymentInfos) {
            for (let i = 0; i < detailForm.value.paymentInfos.length; i++) {
                upPay += detailForm.value.paymentInfos[i].paymentAmount * 1
            }
        }else{
            debtAmount.value = detailForm.value.ledgerInfo.contractType == '1'?detailForm.value.saleSign.totalHousePrice:detailForm.value.ledgerInfo.contractType == "2"?detailForm.value.leaseSign.leaseTotalPrice:detailForm.value.placeSign.totalHousePrice
        }
        allAmount.value = upPay
        if (detailForm.value.ledgerInfo.contractType == "1") {
            const diff = preciseCalculate(detailForm.value.saleSign.totalHousePrice, upPay)
            debtAmount.value = Number(diff) > 0 ? diff : '0'
        }
        if (detailForm.value.ledgerInfo.contractType == "2") {
            const diff = preciseCalculate(detailForm.value.leaseSign.leaseTotalPrice, upPay)
            debtAmount.value = Number(diff) > 0 ? diff : '0'
        }
        if (detailForm.value.ledgerInfo.contractType == "3") {
            const diff = preciseCalculate(detailForm.value.placeSign.totalHousePrice, upPay)
            debtAmount.value = Number(diff) > 0 ? diff : '0'
        }
    })
    reHouseApi.reHouseDetail({ id: record.houseId, houseType: "1" }).then(res => {
        rentAndSaleInfo.value = res
    })
    visible.value = true
}
// 关闭抽屉
const onClose = () => {
    visible.value = false
    // formRef.value.resetFields()
    detailForm.value = {}
    rentAndSaleInfo.value = {}
}

// 抛出函数
defineExpose({
    onOpen
})
</script>

<style lang="scss" scoped>
.drawTitle {
    font-weight: 900;
    font-size: 18px;
}

.drawRow {
    padding-left: 50px;
}
</style>