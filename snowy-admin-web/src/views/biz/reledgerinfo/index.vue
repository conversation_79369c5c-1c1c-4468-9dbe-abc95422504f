<template>
	<a-card :bordered="false">
		<s-table
			ref="table"
			:columns="columns"
			:data="loadData"
			:alert="options.alert.show"
			bordered
			:row-key="(record) => record.id"
			:row-selection="options.rowSelection"
			class="custom-table"
		>
			<template #operator class="table-operator">
				<a-space>
					<a-row>
						<a-col>
							<a-form labelAlign="right" :model="searchFormState" ref="searchFormRef">
								<a-row :gutter="12">
									<a-col>
										<a-form-item label="客户姓名" name="name">
											<a-input
												autocomplete="off"
												v-model:value="searchFormState.name"
												placeholder="请输入客户姓名"
												allow-clear
											/>
										</a-form-item>
									</a-col>
									<a-col>
										<a-form-item label="客户电话" name="phone">
											<a-input
												autocomplete="off"
												v-model:value="searchFormState.phone"
												placeholder="请输入客户电话"
												allow-clear
											/>
										</a-form-item>
									</a-col>
									<a-col>
										<a-form-item label="身份证号" name="idCard">
											<a-input
												autocomplete="off"
												v-model:value="searchFormState.idCard"
												placeholder="请输入身份证号"
												allow-clear
											/>
										</a-form-item>
									</a-col>
									<a-col>
										<a-form-item label="结清状态" name="status">
											<a-select
												v-model:value="searchFormState.status"
												placeholder="请选择"
												:options="houseStatsu"
												style="width: 205px"
											></a-select>
										</a-form-item>
									</a-col>

									<a-col style="margin-left: 10px">
										<a-form-item label="签约类型" name="contractType">
											<a-select
												v-model:value="searchFormState.contractType"
												placeholder="请选择"
												:options="contractDic"
												style="width: 285px"
											></a-select>
										</a-form-item>
									</a-col>
									<a-row v-show="isOpen">
										<a-col>
											<a-form-item label="房屋楼号" name="buildCode">
												<!-- <a-input autocomplete="off" v-model:value="searchFormState.buildCode"
													placeholder="请输入楼号" allow-clear style="width: 205px" /> -->
												<a-select
													v-model:value="buildCodes"
													mode="multiple"
													placeholder="请选择房屋楼号"
													style="width: 205px"
													showArrow
													@change="changeSect"
												>
													<a-select-option v-for="item in budingList" :value="item.code">{{
														item.code
													}}</a-select-option>
												</a-select>
											</a-form-item>
										</a-col>
										<a-col style="margin-left: 10px">
											<a-form-item label="房屋单元" name="unit">
												<!-- <a-cascader v-model:value="value" :options="options" placeholder="请选择 select" /> -->
												<a-input
													autocomplete="off"
													v-model:value="searchFormState.unit"
													style="width: 200px"
													placeholder="请输入房屋单元"
													allow-clear
												/>
											</a-form-item>
										</a-col>
										<a-col style="margin-left: 10px">
											<a-form-item label="房屋楼层" name="floor">
												<!-- <a-cascader v-model:value="value" :options="options" placeholder="请选择 select" /> -->
												<a-input
													autocomplete="off"
													v-model:value="searchFormState.floor"
													placeholder="请输入房屋楼层"
													allow-clear
													style="width: 205px"
												/>
											</a-form-item>
										</a-col>
										<a-col style="margin-left: 10px">
											<a-form-item label="房屋房号" name="houseNumber">
												<!-- <a-cascader v-model:value="value" :options="options" placeholder="请选择 select" /> -->
												<a-input
													autocomplete="off"
													v-model:value="searchFormState.houseNumber"
													placeholder="请输入房屋号"
													allow-clear
													style="width: 205px"
												/>
											</a-form-item>
										</a-col>
										<a-col style="margin-left: 20px">
											<a-form-item label="认购时间">
												<!-- <a-date-picker v-model:value="value1" @change="sureTime"
													style="width: 205px" /> -->
												<a-range-picker v-model:value="value1" @change="sureTime" />
											</a-form-item>
										</a-col>
										<a-col>
											<a-form-item label="名额姓名" name="quotaName">
												<a-input
													autocomplete="off"
													v-model:value="searchFormState.quotaName"
													placeholder="请输入名额姓名"
													allow-clear
													style="width: 205px"
												/>
											</a-form-item>
										</a-col>
										<a-col style="margin-left: 10px">
											<a-form-item label="村落编号" name="villageId">
												<a-input
													autocomplete="off"
													v-model:value="searchFormState.villageId"
													placeholder="请输入村落编号"
													allow-clear
													style="width: 203px"
												/>
											</a-form-item>
										</a-col>

										<a-col style="margin-left: 10px">
											<a-form-item label="选房序号" name="code">
												<a-input
													autocomplete="off"
													v-model:value="searchFormState.code"
													placeholder="请输入选房序号"
													allow-clear
													style="width: 205px"
												/>
											</a-form-item>
										</a-col>
									</a-row>
								</a-row>
								<a-row>
									<a-col>
										<a-row :gutter="12">
											<a-col>
												<a-button type="primary" @click="table.refresh(true)">
													<icon-font style="margin-right: 1px">
														<SearchOutlined />
													</icon-font>
													搜索</a-button
												>
											</a-col>
											<a-col>
												<a-button style="background-color: #ffffff; color: black" @click="onReset">
													<icon-font style="margin-right: 1px">
														<redo-outlined />
													</icon-font>
													重置</a-button
												>
											</a-col>
											<a-col>
												<a-button style="float: right" class="xn-mg08" @click="ImpExpRef.onOpen()">批量导入</a-button>
											</a-col>
											<a-col>
												<a-button
													style="background-color: #ffffff; color: black"
													v-if="hasPerm('reLedgerInfoExport')"
													@click="exportLedger"
												>
													<icon-font style="margin-right: 1px">
														<CloudUploadOutlined />
													</icon-font>
													导出</a-button
												>
											</a-col>
											<a-col>
												<a-button
													style="background-color: #ffffff; color: black"
													v-if="hasPerm('reLedgerInfoExport')"
													@click="exportLedgerList"
												>
													<icon-font style="margin-right: 1px">
														<CloudUploadOutlined />
													</icon-font>
													税务表目导出</a-button
												>
											</a-col>
											<a-col>
												<a-button danger @click="deleteBatchReLedgerInfo()" v-if="hasPerm('reLedgerInfoDelete')">
													<icon-font style="margin-right: 1px">
														<DeleteOutlined />
													</icon-font>
													删除
												</a-button>
												<span style="color: rgb(22, 168, 255); margin-left: 15px" @click="openOrClose">{{
													isOpen ? '收起' : '更多'
												}}</span>
											</a-col>
											<a-col> </a-col>
										</a-row>
									</a-col>
								</a-row>
							</a-form>
						</a-col>
					</a-row>
				</a-space>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'action'">
					<a-space>
						<a-button
							v-if="hasPerm('reLedgerInfoEdit')"
							@click="formRef.onOpen({ ...record, id: record.houseId, actualBuildArea: record.area }, 2)"
							type="link"
							size="small"
							>编辑</a-button
						>
						<!-- <a-button @click="handleDetail(record)" type="link" size="small" >详情</a-button> -->
						<a-button
							@click="detailRef.onOpen({ ...record }, 2)"
							type="link"
							size="small"
							v-if="hasPerm('reLedgerInfoDetail')"
							>详情</a-button
						>
						<!-- <a-divider type="vertical" v-if="hasPerm(['reLedgerInfoEdit', 'reLedgerInfoDelete'], 'and')" /> -->
						<a-popconfirm title="确定要删除吗？" @confirm="deleteReLedgerInfo(record)">
							<a-button type="link" danger size="small" v-if="hasPerm('reLedgerInfoDelete')">删除</a-button>
						</a-popconfirm>
					</a-space>
				</template>
				<template v-if="column.dataIndex === 'status'">
					<div>
						{{ $TOOL.dictTypeData('house_status', record.status) }}
					</div>
				</template>
				<template v-if="column.dataIndex === 'contractType'">
					<div>
						{{ $TOOL.dictTypeData('contract_type', record.contractType) }}
					</div>
				</template>
				<template v-if="column.dataIndex === 'houseType'">
					<div>
						{{ $TOOL.dictTypeData('house_type', record.houseType) }}
					</div>
				</template>
				<template v-if="column.dataIndex === 'debt'">
					<div>
						{{ calculateDebt(record) }}
					</div>
				</template>
			</template>
		</s-table>
	</a-card>
	<Form ref="formRef" @successful="table.refresh(true)" />
	<Detail ref="detailRef" @successful="table.refresh(true)"></Detail>
	<ImpExp ref="ImpExpRef" @successful="refreshList" />
</template>

<script setup name="reledgerinfo">
	import reBuildingApi from '@/api/biz/reBuildingApi'
	import { useMenuStore } from '@/store/menu'
	import { message } from 'ant-design-vue'
	import Form from '../bill/houseBill/form.vue'
	import Detail from './detail.vue'
	import ImpExp from './ImpExp.vue'
	import reLedgerInfoApi from '@/api/biz/reLedgerInfoApi'
	import exportHoemLedgerApi from '@/api/biz/exportHoemLedgerApi'
	import downloadUtil from '@/utils/downloadUtil'
	import { cloneDeep } from 'lodash-es'
	const searchFormRef = ref()
	const searchFormState = ref({})
	const table = ref()
	const formRef = ref()
	const detailRef = ref()
	import tool from '@/utils/tool'
	const contractDic = tool.dictList('contract_type')
	const houseStatsu = tool.dictList('pay-type')
	// const contractType = tool.dictList('contract_type')
	import { useRoute, useRouter } from 'vue-router'
	const route = useRoute()
	const router = useRouter()
	const isOpen = ref(false)
	const menuStore = useMenuStore()
	const projectId = ref('')
	projectId.value = menuStore.projectObj.id
	const value1 = ref('')
	const budingList = ref([])
	const selectValue = ref([])
	const buildCodes = ref([])
	const ImpExpRef = ref()

	//搜表参数
	const formData = reactive({
		name: '',
		idCard: '',
		houseType: '',
		contractType: '',
		status: '',
		villageId: '',
		phone: '',
		subscribeTime: ''
	})
	const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
	const columns = [
		{
			title: '村落编号',
			dataIndex: 'villageId',
			ellipsis: true,
			align: 'center'
		},
		{
			title: '选房序号',
			dataIndex: 'code',
			ellipsis: true,
			align: 'center'
		},
		{
			title: '客户姓名',
			dataIndex: 'name',
			ellipsis: true,
			align: 'center'
		},
		{
			title: '身份证号',
			align: 'center',
			ellipsis: true,
			dataIndex: 'idCard',
			width: 160
		},
		{
			title: '客户电话',
			align: 'center',
			ellipsis: true,
			dataIndex: 'phone',
			width: 110
		},
		{
			title: '楼号',
			align: 'center',
			ellipsis: true,
			dataIndex: 'buildCode',
			width: 60
		},
		{
			title: '单元',
			align: 'center',
			ellipsis: true,
			dataIndex: 'unit',
			width: 60
		},
		{
			title: '楼层',
			align: 'center',
			ellipsis: true,
			dataIndex: 'floor',
			width: 60
		},
		{
			title: '房号',
			align: 'center',
			ellipsis: true,
			dataIndex: 'houseNumber',
			width: 60
		},
		{
			title: '面积(㎡)',
			align: 'center',
			ellipsis: true,
			dataIndex: 'area'
		},
		{
			title: '签约类型',
			align: 'center',
			ellipsis: true,
			dataIndex: 'contractType'
		},
		// {
		// 	title: '成交单价(元)',
		// 	align: 'center',
		// 	ellipsis: true,
		// 	dataIndex: 'dealUnitPrice'
		// },
		{
			title: '签约总价',
			align: 'center',
			ellipsis: true,
			dataIndex: 'contractPrice'
		},
		{
			title: '认购时间',
			align: 'center',
			ellipsis: true,
			dataIndex: 'subscribeTime'
		},
		// {
		// 	title: '签约价',
		// 	align: 'center',
		// 	ellipsis: true,
		// 	dataIndex: 'contractPrice'
		// },
		{
			title: '合计交款',
			align: 'center',
			ellipsis: true,
			dataIndex: 'totalPayment'
		},
		{
			title: '欠款金额',
			align: 'center',
			ellipsis: true,
			dataIndex: 'debt'
		},
		// {
		// 	title: '房屋id',
		// 	align: 'center',
		// 	ellipsis: true,
		// 	dataIndex: 'houseId'
		// },
		// {
		// 	title: '是否历史',
		// 	align: 'center',
		// 	ellipsis: true,
		// 	dataIndex: 'isHistory',
		// 	width:60
		// },
		// {
		// 	title: '项目id',
		// 	align: 'center',
		// 	ellipsis: true,
		// 	dataIndex: 'projectId'
		// },
		// {
		// 	title: '房屋类型',
		// 	align: 'center',
		// 	ellipsis: true,
		// 	dataIndex: 'houseType'
		// },
		{
			title: '房屋状态',
			align: 'center',
			ellipsis: true,
			dataIndex: 'status'
		}
		// {
		// 	title: '扩展信息',
		// 	align: 'center',
		// 	ellipsis: true,
		// 	dataIndex: 'extJson'
		// }
	]
	// 操作栏通过权限判断是否显示
	if (hasPerm(['reLedgerInfoEdit', 'reLedgerInfoDelete'])) {
		columns.push({
			title: '操作',
			dataIndex: 'action',
			align: 'center',
			width: '150px'
		})
	}

	//房屋楼号选择框回调
	const changeSect = (value) => {
		selectValue.value = value
	}
	let selectedRowKeys = ref([])
	// 列表选择配置
	const options = {
		alert: {
			show: false,
			clear: () => {
				selectedRowKeys = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}

	const sureTime = (date) => {
		searchFormState.value.subscribeTime = []
		searchFormState.value.subscribeTime[0] =
			date[0].$y +
			'-' +
			(date[0].$M >= 10 ? date[0].$M + 1 : '0' + (date[0].$M + 1)) +
			'-' +
			(date[0].$D > 10 ? date[0].$D : '0' + date[0].$D) +
			' 00:00:00'
		searchFormState.value.subscribeTime[1] =
			date[1].$y +
			'-' +
			(date[1].$M >= 10 ? date[1].$M + 1 : '0' + (date[1].$M + 1)) +
			'-' +
			(date[1].$D > 10 ? date[1].$D : '0' + date[1].$D) +
			' 23:59:59'
		searchFormState.value.subscribeTime = searchFormState.value.subscribeTime.join(',')
	}
	// 收起展开回调
	const openOrClose = () => {
		isOpen.value = !isOpen.value
	}
	//台账导出
	const exportLedger = () => {
		exportHoemLedgerApi.exportHoemLedger({ projectId: projectId.value, houseType: '1' }).then((res) => {
			console.log(res, '[---]')

			downloadUtil.resultDownload(res)
		})
	}
	//税务列表导出
	const exportLedgerList = () => {
		exportHoemLedgerApi.exportLedegerList({ projectId: projectId.value, houseType: '1' }).then((res) => {
			downloadUtil.resultDownload(res)
		})
	}

	// table表格数据
	const loadData = (parameter) => {
		searchFormState.value.buildCode = selectValue.value.length > 0 ? selectValue.value.join(',') : ''
		searchFormState.value.isHistory = false
		return reLedgerInfoApi
			.reLedgerInfoPage({
				...parameter,
				houseType: '1',
				...searchFormState.value,
				projectId: projectId.value
			})
			.then((data) => {
				for (let i = 0; i < data.records.length; i++) {
					if (data.records[i].isHistory) {
						data.records.splice(i, 1)
						i--
					}
				}
				searchFormState.value.buildCode = searchFormState.value.buildCode?.split(',')
				// data.total = data.records.length
				return data
			})
	}
	// 删除
	const deleteReLedgerInfo = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		reLedgerInfoApi.reLedgerInfoDelete(params).then(() => {
			table.value.refresh(true)
		})
	}
	// 批量删除
	const deleteBatchReLedgerInfo = () => {
		if (selectedRowKeys.value.length < 1) {
			message.warning('请选择一条或多条数据')
			return false
		}
		const params = selectedRowKeys.value.map((m) => {
			return {
				id: m
			}
		})
		reLedgerInfoApi.reLedgerInfoDelete(params).then(() => {
			table.value.clearRefreshSelected()
		})
	}
	//刷新列表
	const refreshList = (queryParams) => {
		for (let key in queryParams) {
			if (queryParams[key] == '') {
				delete queryParams[key]
			}
		}
		loadData({
			houseType: 1,
			...queryParams
		}) //  houseType 住宅1、商业2、储藏间3
		setTimeout(() => {
			table.value.refresh(true)
		}, 500)
	}

	//重置
	const onReset = () => {
		buildCodes.value = []
		selectValue.value = []
		searchFormState.value = {}
		searchFormRef.value.resetFields()
		value1.value = ''
		table.value.refresh(true)
	}

	// 🔧 修复：优先使用后端返回的欠款金额，与详情页面保持一致
	const calculateDebt = (record) => {
		// 优先使用后端返回的欠款金额
		if (record.debt !== undefined && record.debt !== null) {
			return Number(record.debt).toFixed(2);
		}

		// 备用计算逻辑（当后端没有返回debt字段时）
		const totalHousePrice = Number(record.totalHousePrice) || 0;
		const totalPayment = Number(record.totalPayment) || 0;

		const debt = totalHousePrice - totalPayment;
		return debt >= 0 ? debt.toFixed(2) : '--';
	}

	//获取楼栋列表
	const getBuildingList = () => {
		reBuildingApi.reBuildingPage({ projectId: projectId.value }).then((res) => {
			budingList.value = res.records
		})
	}

	onMounted(() => {
		getBuildingList()
		if (route.query.phone || route.query.name) {
			searchFormState.value.phone = route.query.phone
			searchFormState.value.name = route.query.name
			setTimeout(() => {
				table.value.refresh(true)
			}, 50)
		}
	})
</script>
<style lang="scss" scoped>
	.drawTitle {
		font-weight: 900;
		font-size: 18px;
	}

	.drawRow {
		padding-left: 50px;
	}
	.custom-table {
		:deep(.ant-table-tbody > tr:hover > td) {
			background-color:#E6F4FF !important;
		}
	}
</style>
