<template>
	<xn-form-container
		:title="formData.id ? '编辑' : '房屋认购'"
		width="50%"
		:visible="visible"
		:destroy-on-close="false"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-row :gutter="16">
				<a-divider>房源信息</a-divider>
				<a-col :span="12">
					<a-form-item label="楼号" name="buildCode">
						<a-input disabled v-model:value="formData.code" placeholder="请输入楼号" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="单元" name="unit">
						<a-input disabled v-model:value="formData.unit" placeholder="请输入单元" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="楼层" name="floor">
						<a-input disabled v-model:value="formData.floor" placeholder="请输入楼层" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="房号" name="houseNumber">
						<a-input disabled v-model:value="formData.houseNumber" placeholder="请输入房号" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="建筑面积" name="actualBuildArea">
						<a-input disabled v-model:value="formData.actualBuildArea" placeholder="请输入面积" allow-clear />
					</a-form-item>
				</a-col>
				<a-divider>客户信息</a-divider>
				<a-col :span="12">
					<a-form-item label="村落编号" name="villageId">
						<a-input v-model:value="formData.villageId" placeholder="请输入村落编号" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="选房序号" name="houseCode">
						<a-input v-model:value="formData.houseCode" placeholder="请输入选房序号" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="认购时间" name="subscribeTime">
						<a-date-picker
							v-model:value="formData.subscribeTime"
							value-format="YYYY-MM-DD"
							placeholder="请选择认购时间"
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="姓名" name="name">
						<a-input v-model:value="formData.name" placeholder="请输入姓名" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="身份证号" name="idCard">
						<a-input v-model:value="formData.idCard" placeholder="请输入身份证号" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="电话" name="phone">
						<a-input v-model:value="formData.phone" placeholder="请输入电话" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="地址" name="address">
						<a-input v-model:value="formData.address" placeholder="请输入地址" allow-clear />
					</a-form-item>
				</a-col>
				<a-divider>共有人信息</a-divider>

				<a-col :span="24">
					<a-form-item label="" name="shareholderIds">
						<a-row :gutter="10" class="form-row">
							<a-col :span="2" class="form-row-con"> 序号 </a-col>
							<a-col :span="4" class="form-row-con"> 姓名 </a-col>
							<a-col :span="6" class="form-row-con"> 身份证号 </a-col>
							<a-col :span="4" class="form-row-con"> 电话 </a-col>
							<a-col :span="4" class="form-row-con"> 操作 </a-col>
							<a-col :span="4" class="form-row-con">
								<a-button type="primary" @click="addCus(1)" size="small">
									<PlusOutlined />
									增加
								</a-button>
							</a-col>
						</a-row>
						<div :key="index" v-for="(item, index) in formData.shareholderList">
							<a-row :gutter="10">
								<a-col :span="2">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ index + 1 }}</div>
									</a-form-item>
								</a-col>
								<a-col :span="4">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.name }}</div>
									</a-form-item>
								</a-col>
								<a-col :span="6">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.idCard }}</div>
									</a-form-item>
								</a-col>
								<a-col :span="4">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.phone }}</div>
									</a-form-item>
								</a-col>
								<a-col :span="4" class="xn-mt4">
									<div style="width: 100%; display: flex; justify-content: center">
										<a-button size="small" type="text" danger @click="editUser(1, index, item)">编辑</a-button>
										<a-popconfirm title="确定要删除此人员吗？" @confirm="delUser(1, index, item.id)">
											<a-button size="small" type="text" danger>移除</a-button>
										</a-popconfirm>
									</div>
								</a-col>
							</a-row>
						</div>
					</a-form-item>
				</a-col>
				<a-divider>名额信息</a-divider>
				<a-col :span="24">
					<a-form-item label="" name="quotaIds">
						<a-row :gutter="10" class="form-row">
							<a-col :span="2" class="form-row-con"> 序号 </a-col>
							<a-col :span="6" class="form-row-con"> 姓名 </a-col>
							<a-col :span="8" class="form-row-con"> 身份证号 </a-col>
							<!-- <a-col :span="4" class="form-row-con"> 电话 </a-col> -->
							<a-col :span="4" class="form-row-con"> 操作 </a-col>
							<a-col :span="4" class="form-row-con">
								<a-button type="primary" @click="addCus(2)" size="small">
									<PlusOutlined />
									增加
								</a-button>
							</a-col>
						</a-row>
						<div :key="index" v-for="(item, index) in formData.quotaList">
							<a-row :gutter="10">
								<a-col :span="2">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ index + 1 }}</div>
									</a-form-item>
								</a-col>
								<a-col :span="6">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.name }}</div>
									</a-form-item>
								</a-col>
								<a-col :span="8">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.idCard }}</div>
									</a-form-item>
								</a-col>
								<!-- <a-col :span="4">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.phone }}</div>
									</a-form-item>
								</a-col> -->
								<a-col :span="4" class="xn-mt4">
									<div style="width: 100%; display: flex; justify-content: center">
										<a-button size="small" type="text" danger @click="editUser(2, index, item)">编辑</a-button>
										<a-popconfirm title="确定要删除此人员吗？" @confirm="delUser(2, index, item.id)">
											<a-button size="small" type="text" danger>移除</a-button>
										</a-popconfirm>
									</div>
								</a-col>
							</a-row>
						</div>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="人数" name="persionNum">
						<a-input disabled v-model:value="formData.quotaList.length" placeholder="请输入人数" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="启用面积" name="enableArea">
						<a-input placeholder="请输入启用面积" v-model:value="formData.enableArea" suffix="m²" />
					</a-form-item>
				</a-col>

				<a-divider>签约信息</a-divider>
				<a-col :span="24">
					<a-form-item label="签约形式" name="signType">
						<a-radio-group :disabled="signTypeDisabled" v-model:value="formData.signType" :options="options" />
					</a-form-item> </a-col
				><!-- 销售 -->
				<div style="width: 100%; display: flex; flex-wrap: wrap" v-if="formData.signType == 1">
					<a-col :span="12">
						<a-form-item label="销售单价" name="saleSign.unitPrice">
							<a-input
								type="number"
								placeholder="请输入销售单价"
								v-model:value="formData.saleSign.unitPrice"
								suffix="元/m²"
								@blur="unitPriceBlur"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="销售总价" name="saleSign.totalPrice">
							<a-input disabled placeholder="请输入销售总价" v-model:value="formData.saleSign.totalPrice" suffix="元" />
						</a-form-item>
					</a-col>
					<a-col :span="18">
						<a-form-item label="执行优惠：" name="saleSign.discount">
							<a-input
								type="number"
								v-model:value="formData.saleSign.discount"
								placeholder="请输入执行优惠"
								allow-clear
								@blur="saleSignDisBlur"
								:suffix="formData.saleSign.discountType == 1 ? '%' : '元'"
							>
								<template #addonAfter>
									<a-select v-model:value="formData.saleSign.discountType" style="width: 80px" @change="discountChange">
										<a-select-option value="1">折扣</a-select-option>
										<a-select-option value="2">直减</a-select-option>
									</a-select>
								</template>
							</a-input>
						</a-form-item>
					</a-col>
					<a-col :span="24">
						<a-form-item label="优惠备注" name="saleSign.discountRemark">
							<a-input v-model:value="formData.saleSign.discountRemark" placeholder="请输入优惠备注" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="签约总价" name="saleSign.contractPrice">
							<a-input
								type="number"
								disabled
								placeholder="请输入签约总价"
								v-model:value="formData.saleSign.contractPrice"
								suffix="元"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="签约单价" name="saleSign.contractUnitPrice">
							<a-input
								type="number"
								disabled
								placeholder="请输入签约单价"
								v-model:value="formData.saleSign.contractUnitPrice"
								suffix="元/m²"
							/>
						</a-form-item>
					</a-col>

					<a-col :span="12">
						<a-form-item label="维修基金" name="saleSign.maintenanceFundUnitPrice">
							<a-input
								type="number"
								placeholder="请输入维修基金"
								v-model:value="formData.saleSign.maintenanceFundUnitPrice"
								suffix="元/m²"
								@blur="maintenanceBlur"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="维修基金总计" name="saleSign.totalPrice">
							<a-input
								type="number"
								disabled
								placeholder="请输入维修基金总计"
								v-model:value="formData.saleSign.maintenanceFund"
								suffix="元"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="24">
						<a-form-item label="付款形式" name="paymentMethod">
							<a-radio-group
								v-model:value="formData.saleSign.paymentMethod"
								:options="payTypeOptions"
								@change="paymentMethodChange"
							/>
						</a-form-item>
					</a-col>
					<!-- 付款形式 - 一次性付款 -->
					<div style="width: 100%; display: flex; flex-wrap: wrap" v-if="formData.saleSign.paymentMethod == 1">
						<a-col :span="12">
							<a-form-item label="房款合计:" name="saleSign.totalHousePrice">
								<a-input
									type="number"
									disabled
									placeholder="请输入房款合计"
									v-model:value="formData.saleSign.totalHousePrice"
									suffix="元"
								/>
							</a-form-item>
						</a-col>
					</div>
					<!-- 付款形式 - 全款分期 -->
					<div style="width: 100%; display: flex; flex-wrap: wrap" v-if="formData.saleSign.paymentMethod == 2">
						<a-col :span="12">
							<a-form-item label="定金:" name="saleSign.earnestMoney">
								<a-input
									type="number"
									placeholder="请输入定金"
									v-model:value="formData.saleSign.earnestMoney"
									suffix="元"
									@blur="xshjBlur"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="房款合计:" name="saleSign.totalHousePrice">
								<a-input
									disabled
									type="number"
									placeholder="请输入房款合计"
									v-model:value="formData.saleSign.totalHousePrice"
									suffix="元"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="全款分期" name="positionJson">
								<a-row :gutter="10" class="form-row">
									<a-col :span="2" class="form-row-con"> 序号 </a-col>
									<!-- <a-col :span="3" class="form-row-con"> 交款类型 </a-col> -->
									<a-col :span="7" class="form-row-con"> 交款金额 </a-col>
									<a-col :span="8" class="form-row-con"> 交款时间 </a-col>
									<!-- <a-col :span="4" class="form-row-con"> 附件 </a-col> -->
									<a-col :span="4" class="form-row-con"> 操作 </a-col>
									<a-col :span="3" class="form-row-con">
										<a-button type="primary" @click="addPay(1)" size="small">
											<PlusOutlined />
											增加
										</a-button>
									</a-col>
								</a-row>
								<div :key="positionInfo" v-for="(item, index) in formData.installmentPaymentList">
									<a-row :gutter="10">
										<a-col :span="2">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px">{{ index + 1 }}</div>
											</a-form-item>
										</a-col>
										<!-- <a-col :span="3">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px">
													{{ $TOOL.dictTypeData('payment', item.paymentType) }}
												</div>
											</a-form-item>
										</a-col> -->
										<a-col :span="7">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.paymentAmount }}</div>
											</a-form-item>
										</a-col>

										<a-col :span="8">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.paymentTime }}</div>
											</a-form-item>
										</a-col>
										<!-- <a-col :span="4">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px" v-if="item.paymentAttachment">
													<img :src="item.paymentAttachment" style="width: 60px; height: 60px" alt="" />
													<a-image style="width: 60px; height: 60px" :src="item.paymentAttachment" />
												</div>
											</a-form-item>
										</a-col> -->
										<a-col :span="4" class="xn-mt4">
											<div style="width: 100%; display: flex; justify-content: center">
												<a-button size="small" type="text" danger ghost @click="editPay(1, index, item)">编辑</a-button>
												<a-popconfirm title="确定要删除吗？" @confirm="delPay(1, index, item.id)">
													<a-button size="small" type="text" danger>移除</a-button>
												</a-popconfirm>
											</div>
										</a-col>
									</a-row>
								</div>
							</a-form-item>
						</a-col>
					</div>
					<!-- 付款形式 - 贷款 -->
					<div style="width: 100%; display: flex; flex-wrap: wrap" v-if="formData.saleSign.paymentMethod == 3">
						<a-col :span="12">
							<a-form-item label="首付" name="saleSign.firstPayment">
								<a-input
									type="number"
									placeholder="请输入首付"
									v-model:value="formData.saleSign.firstPayment"
									suffix="元"
									@blur="xsdkhjBlur"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="贷款" name="saleSign.loan">
								<a-input
									type="number"
									placeholder="请输入贷款"
									v-model:value="formData.saleSign.loan"
									suffix="元"
									@blur="xsdkhjBlur"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="房款合计:" name="saleSign.totalHousePrice">
								<a-input
									type="number"
									disabled
									placeholder="请输入房款合计"
									v-model:value="formData.saleSign.totalHousePrice"
									suffix="元"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="贷款银行" name="saleSign.loanBank">
								<a-input placeholder="请输入贷款银行" v-model:value="formData.saleSign.loanBank" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="办理贷款时间" name="saleSign.loanTime">
								<a-date-picker
									v-model:value="formData.saleSign.loanTime"
									value-format="YYYY-MM-DD"
									placeholder="请选择办理贷款时间"
									style="width: 100%"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="贷款回款时间" name="saleSign.loanReturnTime">
								<a-date-picker
									v-model:value="formData.saleSign.loanReturnTime"
									value-format="YYYY-MM-DD"
									placeholder="请选择贷款回款时间"
									style="width: 100%"
								/>
							</a-form-item>
						</a-col>
					</div>
				</div>
				<!-- 租赁 -->
				<div style="width: 100%; display: flex; flex-wrap: wrap" v-if="formData.signType == 2">
					<a-col :span="12">
						<a-form-item label="租赁价格" name="leaseSign.leaseUnitPrice">
							<a-input
								type="number"
								placeholder="请输入租赁价格"
								v-model:value="formData.leaseSign.leaseUnitPrice"
								suffix="元/m²/月"
								@blur="leaseUnitPriceBlur"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="租赁保证金" name="leaseSign.leaseDeposit">
							<a-input
								type="number"
								placeholder="请输入租赁保证金"
								v-model:value="formData.leaseSign.leaseDeposit"
								suffix="元"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="租赁起始时间：" name="leaseSign.leaseStartTime">
							<a-date-picker
								v-model:value="formData.leaseSign.leaseStartTime"
								value-format="YYYY-MM-DD"
								placeholder="请选择租赁起始时间："
								style="width: 100%"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="租赁结束时间：" name="leaseSign.leaseEndTime">
							<a-date-picker
								v-model:value="formData.leaseSign.leaseEndTime"
								value-format="YYYY-MM-DD"
								placeholder="请选择租赁结束时间"
								style="width: 100%"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="到期提醒日：" name="leaseSign.expireRemind">
							<a-date-picker
								v-model:value="formData.leaseSign.expireRemind"
								value-format="YYYY-MM-DD"
								placeholder="请选择到期提醒日："
								style="width: 100%"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="18">
						<a-form-item label="执行优惠：" name="leaseSign.discount">
							<a-input
								type="number"
								v-model:value="formData.leaseSign.discount"
								:suffix="formData.leaseSign.discountType == 1 ? '%' : '元'"
								placeholder="请输入执行优惠"
								allow-clear
								@blur="leaseUnitPriceBlur"
							>
								<template #addonAfter>
									<a-select
										v-model:value="formData.leaseSign.discountType"
										style="width: 80px"
										@change="leaseSignChange"
									>
										<a-select-option value="1">折扣</a-select-option>
										<a-select-option value="2">直减</a-select-option>
									</a-select>
								</template>
							</a-input>
						</a-form-item>
					</a-col>
					<a-col :span="24">
						<a-form-item label="优惠备注" name="leaseSign.discountRemark">
							<a-input v-model:value="formData.leaseSign.discountRemark" placeholder="请输入优惠备注" allow-clear />
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="租期" name="leaseSign.leaseTerm">
							<a-input
								type="number"
								placeholder="请输入租期"
								v-model:value="formData.leaseSign.leaseTerm"
								suffix="月"
								@blur="leaseUnitPriceBlur"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="12">
						<a-form-item label="免租期" name="leaseSign.rentFreePeriod">
							<a-input
								type="number"
								placeholder="请输入免租期"
								v-model:value="formData.leaseSign.rentFreePeriod"
								suffix="月"
								@blur="leaseUnitPriceBlur"
							/>
						</a-form-item>
					</a-col>

					<a-col :span="12">
						<a-form-item label="租赁总价" name="leaseSign.leaseTotalPrice">
							<a-input
								disabled
								type="number"
								placeholder="请输入租赁总价"
								v-model:value="formData.leaseSign.leaseTotalPrice"
								suffix="元"
							/>
						</a-form-item>
					</a-col>
					<!-- <a-col :span="12">
						<a-form-item label="租赁单价" name="leaseSign.leaseUnitPrice">
							<a-input
								disabled
								type="number"
								placeholder="请输入租赁单价"
								v-model:value="formData.leaseSign.leaseUnitPrice"
								suffix="元/m²"
							/>
						</a-form-item>
					</a-col> -->
					<a-col :span="24">
						<a-form-item label="付款形式" name="leaseSign.paymentMethod">
							<a-radio-group
								v-model:value="formData.leaseSign.paymentMethod"
								:options="payTypeOptions"
								@change="paymentMethodChange"
							/>
						</a-form-item>
					</a-col>
					<!-- 租赁付款形式 - 一次性付款 -->
					<div style="width: 100%; display: flex; flex-wrap: wrap" v-if="formData.leaseSign.paymentMethod == 1">
						<a-col :span="12">
							<a-form-item label="房款合计:" name="leaseSign.totalHousePrice">
								<a-input
									disabled
									type="number"
									placeholder="请输入房款合计"
									v-model:value="formData.leaseSign.totalHousePrice"
									suffix="元"
								/>
							</a-form-item>
						</a-col>
					</div>
					<!-- 租赁付款形式 - 全款分期 -->
					<div style="width: 100%; display: flex; flex-wrap: wrap" v-if="formData.leaseSign.paymentMethod == 2">
						<a-col :span="12">
							<a-form-item label="定金:" name="leaseSign.earnestMoney">
								<a-input
									type="number"
									placeholder="请输入定金"
									v-model:value="formData.leaseSign.earnestMoney"
									suffix="元"
									@blur="zlhjBlur"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="房款合计:" name="leaseSign.totalHousePrice">
								<a-input
									disabled
									type="number"
									placeholder="请输入房款合计"
									v-model:value="formData.leaseSign.totalHousePrice"
									suffix="元"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="分期付款">
								<a-row :gutter="10" class="form-row">
									<a-col :span="2" class="form-row-con"> 序号 </a-col>
									<!-- <a-col :span="3" class="form-row-con"> 交款类型 </a-col> -->
									<a-col :span="5" class="form-row-con"> 交款金额 </a-col>
									<a-col :span="6" class="form-row-con"> 交款时间 </a-col>
									<a-col :span="4" class="form-row-con"> 附件 </a-col>
									<a-col :span="4" class="form-row-con"> 操作 </a-col>
									<a-col :span="3" class="form-row-con">
										<a-button type="primary" @click="addPay(1)" size="small">
											<PlusOutlined />
											增加
										</a-button>
									</a-col>
								</a-row>
								<div :key="positionInfo" v-for="(item, index) in formData.installmentPaymentList">
									<a-row :gutter="10">
										<a-col :span="2">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px">{{ index + 1 }}</div>
											</a-form-item>
										</a-col>
										<!-- <a-col :span="3">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px">
													{{ $TOOL.dictTypeData('payment', item.paymentType) }}
												</div>
											</a-form-item>
										</a-col> -->
										<a-col :span="5">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.paymentAmount }}</div>
											</a-form-item>
										</a-col>

										<a-col :span="6">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.paymentTime }}</div>
											</a-form-item>
										</a-col>
										<a-col :span="4">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px" v-if="item.paymentAttachment">
													<!-- <img :src="item.paymentAttachment" style="width: 60px; height: 60px" alt="" /> -->
													<a-image style="width: 60px; height: 60px" :src="item.paymentAttachment" />
												</div>
											</a-form-item>
										</a-col>
										<a-col :span="4" class="xn-mt4">
											<div style="width: 100%; display: flex; justify-content: center">
												<a-button size="small" type="text" danger ghost @click="editPay(1, index, item)">编辑</a-button>
												<a-popconfirm title="确定要删除吗？" @confirm="delPay(1, index, item.id)">
													<a-button size="small" type="text" danger>移除</a-button>
												</a-popconfirm>
											</div>
										</a-col>
									</a-row>
								</div>
							</a-form-item>
						</a-col>
					</div>
					<!-- 租赁付款形式 - 贷款 -->
					<div style="width: 100%; display: flex; flex-wrap: wrap" v-if="formData.leaseSign.paymentMethod == 3">
						<a-col :span="12">
							<a-form-item label="首付:" name="leaseSign.firstPayment">
								<a-input
									type="number"
									placeholder="请输入首付"
									v-model:value="formData.leaseSign.firstPayment"
									suffix="元"
									@blur="zldkhjBlur"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="贷款:" name="leaseSign.loan">
								<a-input
									type="number"
									placeholder="请输入贷款"
									v-model:value="formData.leaseSign.loan"
									suffix="元"
									@blur="zldkhjBlur"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="房款合计:" name="leaseSign.totalHousePrice">
								<a-input
									type="number"
									disabled
									placeholder="请输入房款合计"
									v-model:value="formData.leaseSign.totalHousePrice"
									suffix="元"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="贷款银行:" name="leaseSign.loanBank">
								<a-input placeholder="请输入贷款银行" v-model:value="formData.leaseSign.loanBank" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="办理贷款时间:" name="leaseSign.loanTime">
								<a-date-picker
									v-model:value="formData.leaseSign.loanTime"
									value-format="YYYY-MM-DD"
									placeholder="请选择办理贷款时间"
									style="width: 100%"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="贷款回款时间:" name="leaseSign.loanReturnTime">
								<a-date-picker
									v-model:value="formData.leaseSign.loanReturnTime"
									value-format="YYYY-MM-DD"
									placeholder="请选择贷款回款时："
									style="width: 100%"
								/>
							</a-form-item>
						</a-col>
					</div>
				</div>
				<!-- 安置 -->
				<div style="width: 100%; display: flex; flex-wrap: wrap" v-if="formData.signType == 3">
					<a-col :span="8">
						<a-form-item label="基准价:" name="placeSign.benchmarkPrice">
							<a-input
								type="number"
								placeholder="请输入基准价"
								v-model:value="formData.placeSign.benchmarkPrice"
								suffix="元/m²"
								@blur="benchmarkBlur"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="面积:" name="placeSign.area">
							<a-input
								type="number"
								placeholder="请输入面积"
								v-model:value="formData.placeSign.area"
								suffix="m²"
								@blur="benchmarkBlur"
							/>
						</a-form-item> </a-col
					><a-col :span="8">
						<a-form-item label="小计:" name="placeSign.subtotal">
							<a-input
								disabled
								type="number"
								placeholder="请输入小计"
								v-model:value="formData.placeSign.subtotal"
								suffix="元"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="补贴价:" name="placeSign.subsidyPrice">
							<a-input
								type="number"
								placeholder="请输入基准价"
								v-model:value="formData.placeSign.subsidyPrice"
								suffix="元/m²"
								@blur="subsidyBlur"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="面积:" name="placeSign.subsidyArea">
							<a-input
								type="number"
								placeholder="请输入面积"
								v-model:value="formData.placeSign.subsidyArea"
								suffix="m²"
								@blur="subsidyBlur"
							/>
						</a-form-item> </a-col
					><a-col :span="8">
						<a-form-item label="小计:" name="placeSign.subsidySubtotal">
							<a-input
								disabled
								type="number"
								placeholder="请输入小计"
								v-model:value="formData.placeSign.subsidySubtotal"
								suffix="元"
							/>
						</a-form-item>
					</a-col>

					<a-col :span="8">
						<a-form-item label="市场价:" name="placeSign.marketPrice">
							<a-input
								type="number"
								placeholder="请输入市场价"
								v-model:value="formData.placeSign.marketPrice"
								suffix="元/m²"
								@blur="marketBlur"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="面积:" name="placeSign.marketArea">
							<a-input
								type="number"
								placeholder="请输入面积"
								v-model:value="formData.placeSign.marketArea"
								suffix="m²"
								@blur="marketBlur"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="小计:" name="placeSign.marketSubtotal">
							<a-input
								disabled
								type="number"
								placeholder="请输入小计"
								v-model:value="formData.placeSign.marketSubtotal"
								suffix="元"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="18">
						<a-form-item label="执行优惠：" name="placeSign.discount">
							<a-input
								v-model:value="formData.placeSign.discount"
								placeholder="请输入执行优惠"
								:suffix="formData.placeSign.discountType == 1 ? '%' : '元'"
								allow-clear
								@blur="benchmarkBlur"
							>
								<template #addonAfter>
									<a-select v-model:value="formData.placeSign.discountType" style="width: 80px">
										<a-select-option value="1">折扣</a-select-option>
										<a-select-option value="2">直减</a-select-option>
									</a-select>
								</template>
							</a-input>
						</a-form-item>
					</a-col>
					<a-col :span="24">
						<a-form-item label="优惠备注:" name="placeSign.discountRemark">
							<a-input
								v-model:value="formData.placeSign.discountRemark"
								placeholder="请输入优惠备注"
								allow-clear
							/>
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="签约总价:" name="placeSign.contractPrice">
							<a-input
								disabled
								type="number"
								placeholder="请输入签约总价"
								v-model:value="formData.placeSign.contractPrice"
								suffix="元"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="维修基金:" name="placeSign.maintenanceFund">
							<a-input
								type="number"
								placeholder="请输入维修基金"
								v-model:value="formData.placeSign.maintenanceFund"
								@blur="maintenanceFundBlur"
								suffix="元"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="合计:" name="placeSign.total">
							<a-input
								type="number"
								disabled
								placeholder="请输入合计"
								v-model:value="formData.placeSign.total"
								suffix="元"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="8">
						<a-form-item label="签约时间:" name="placeSign.contractTime">
							<a-date-picker
								v-model:value="formData.placeSign.contractTime"
								value-format="YYYY-MM-DD"
								placeholder="签约时间"
								style="width: 100%"
							/>
						</a-form-item>
					</a-col>
					<a-col :span="24">
						<a-form-item label="付款形式:" name="placeSign.paymentMethod">
							<a-radio-group v-model:value="formData.placeSign.paymentMethod" :options="payTypeOptions" />
						</a-form-item>
					</a-col>
					<!-- 安置付款形式 - 一次性付款 -->
					<div style="width: 100%; display: flex; flex-wrap: wrap" v-if="formData.placeSign.paymentMethod == 1">
						<a-col :span="12">
							<a-form-item label="房款合计:" name="placeSign.totalHousePrice">
								<a-input
									disabled
									type="number"
									placeholder="请输入房款合计"
									v-model:value="formData.placeSign.totalHousePrice"
									suffix="元"
								/>
							</a-form-item>
						</a-col>
					</div>
					<!-- 安置付款形式 - 全款分期 -->
					<div style="width: 100%; display: flex; flex-wrap: wrap" v-if="formData.placeSign.paymentMethod == 2">
						<a-col :span="12">
							<a-form-item label="定金:" name="placeSign.earnestMoney">
								<a-input
									type="number"
									placeholder="请输入定金"
									v-model:value="formData.placeSign.earnestMoney"
									suffix="元"
									@blur="azhjBlur"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="房款合计:" name="placeSign.totalHousePrice">
								<a-input
									disabled
									type="number"
									placeholder="请输入房款合计"
									v-model:value="formData.placeSign.totalHousePrice"
									suffix="元"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="分期付款:">
								<a-row :gutter="10" class="form-row">
									<a-col :span="2" class="form-row-con"> 序号 </a-col>
									<!-- <a-col :span="3" class="form-row-con"> 交款类型 </a-col> -->
									<a-col :span="5" class="form-row-con"> 交款金额 </a-col>
									<a-col :span="6" class="form-row-con"> 交款时间 </a-col>
									<a-col :span="4" class="form-row-con"> 附件 </a-col>
									<a-col :span="4" class="form-row-con"> 操作 </a-col>
									<a-col :span="3" class="form-row-con">
										<a-button type="primary" @click="addPay(1)" size="small">
											<PlusOutlined />
											增加
										</a-button>
									</a-col>
								</a-row>
								<div :key="positionInfo" v-for="(item, index) in formData.installmentPaymentList">
									<a-row :gutter="10">
										<a-col :span="2">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px">{{ index + 1 }}</div>
											</a-form-item>
										</a-col>
										<!-- <a-col :span="3">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px">
													{{ $TOOL.dictTypeData('payment', item.paymentType) }}
												</div>
											</a-form-item>
										</a-col> -->
										<a-col :span="5">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.paymentAmount }}</div>
											</a-form-item>
										</a-col>

										<a-col :span="6">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.paymentTime }}</div>
											</a-form-item>
										</a-col>
										<a-col :span="4">
											<a-form-item>
												<div style="width: 100%; text-align: center; margin-left: 6px" v-if="item.paymentAttachment">
													<!-- <img :src="item.paymentAttachment" style="width: 60px; height: 60px" alt="" /> -->
													<a-image style="width: 60px; height: 60px" :src="item.paymentAttachment" />
												</div>
											</a-form-item>
										</a-col>
										<a-col :span="4" class="xn-mt4">
											<div style="width: 100%; display: flex; justify-content: center">
												<a-button size="small" type="text" danger ghost @click="editPay(1, index, item)">编辑</a-button>
												<a-popconfirm title="确定要删除吗？" @confirm="delPay(1, index, item.id)">
													<a-button size="small" type="text" danger>移除</a-button>
												</a-popconfirm>
											</div>
										</a-col>
									</a-row>
								</div>
							</a-form-item>
						</a-col>
					</div>
					<!-- 安置付款形式 - 贷款 -->
					<div style="width: 100%; display: flex; flex-wrap: wrap" v-if="formData.placeSign.paymentMethod == 3">
						<a-col :span="12">
							<a-form-item label="首付:" name="placeSign.firstPayment">
								<a-input
									type="number"
									placeholder="请输入首付"
									v-model:value="formData.placeSign.firstPayment"
									suffix="元"
									@blur="azdkhjBlur"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="贷款:" name="placeSign.loan">
								<a-input
									type="number"
									placeholder="请输入贷款"
									v-model:value="formData.placeSign.loan"
									suffix="元"
									@blur="azdkhjBlur"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="房款合计:" name="placeSign.totalHousePrice">
								<a-input
									type="number"
									disabled
									placeholder="请输入房款合计"
									v-model:value="formData.placeSign.totalHousePrice"
									suffix="元"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="贷款银行:" name="placeSign.loanBank">
								<a-input placeholder="请输入贷款银行" v-model:value="formData.placeSign.loanBank" />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="办理贷款时间:" name="placeSign.loanTime">
								<a-date-picker
									v-model:value="formData.placeSign.loanTime"
									value-format="YYYY-MM-DD"
									show-time
									placeholder="请选择办理贷款时间"
									style="width: 100%"
								/>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="贷款回款时间:" name="placeSign.loanReturnTime">
								<a-date-picker
									v-model:value="formData.placeSign.loanReturnTime"
									value-format="YYYY-MM-DD"
									show-time
									placeholder="请选择贷款回款时间"
									style="width: 100%"
								/>
							</a-form-item>
						</a-col>
					</div>
				</div>
				<a-divider>交款信息</a-divider>

				<!-- <a-col :span="8">
					<a-form-item label="欠款金额" name="arrears">
						<a-input
							type="number"
							disabled
							v-model:value="formData.arrears"
							placeholder="请输入欠款金额："
							allow-clear
							suffix="元"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="结清状态" name="endStatus">
						<a-input disabled v-model:value="formData.endStatus" placeholder="请输入结清状态：" />
					</a-form-item>
				</a-col> -->
				<a-col :span="24">
					<a-form-item label="付款记录" name="positionJson">
						<a-row :gutter="10" class="form-row">
							<a-col :span="2" class="form-row-con"> 序号 </a-col>
							<a-col :span="3" class="form-row-con"> 交款类型 </a-col>
							<a-col :span="4" class="form-row-con"> 交款金额 </a-col>
							<a-col :span="4" class="form-row-con"> 交款时间 </a-col>
							<a-col :span="4" class="form-row-con"> 附件 </a-col>
							<a-col :span="3" class="form-row-con"> 操作 </a-col>
							<a-col :span="3" class="form-row-con">
								<a-button type="primary" @click="addPay(2)" size="small">
									<PlusOutlined />
									增加
								</a-button>
							</a-col>
						</a-row>

						<div :key="positionInfo" v-for="(item, index) in formData.paymentInfoList">
							<a-row :gutter="10">
								<a-col :span="2">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ index + 1 }}</div>
									</a-form-item>
								</a-col>
								<a-col :span="3">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">
											{{ $TOOL.dictTypeData('payment', item.paymentType) }}
										</div>
									</a-form-item>
								</a-col>
								<a-col :span="4">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.paymentAmount }}</div>
									</a-form-item>
								</a-col>

								<a-col :span="4">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px">{{ item.paymentTime }}</div>
									</a-form-item>
								</a-col>
								<a-col :span="4">
									<a-form-item>
										<div style="width: 100%; text-align: center; margin-left: 6px" v-if="item.paymentAttachment">
											<!-- <img :src="item.paymentAttachment" style="width: 60px; height: 60px" alt="" /> -->
											<a-image style="width: 60px; height: 60px" :src="item.paymentAttachment" />
										</div>
									</a-form-item>
								</a-col>
								<a-col :span="4" class="xn-mt4">
									<div style="width: 100%; display: flex; justify-content: center">
										<a-button size="small" type="text" danger ghost @click="editPay(2, index, item)">编辑</a-button>
										<a-popconfirm title="确定要删除吗？" @confirm="delPay(2, index, item.id)">
											<a-button size="small" type="text" danger>移除</a-button>
										</a-popconfirm>
									</div>
								</a-col>
							</a-row>
						</div>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="合计交款:" name="PaymentTotal">
						<a-input
							type="number"
							disabled
							v-model:value="formData.PaymentTotal"
							placeholder="请输入合计交款"
							allow-clear
							suffix="元"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="欠款金额" name="arrears">
						<a-input
							type="number"
							disabled
							v-model:value="formData.arrears"
							placeholder="请输入欠款金额"
							allow-clear
							suffix="元"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="结清状态" name="settleStatus">
						<a-input disabled v-model:value="formData.settleStatus" placeholder="请输入结清状态" />
					</a-form-item>
				</a-col>
				<a-divider>合同信息</a-divider>
				<a-col :span="12">
					<a-form-item label="合同备案签约日:" name="contractInfo.contractRecordTime">
						<a-date-picker
							v-model:value="formData.contractInfo.contractRecordTime"
							value-format="YYYY-MM-DD"
							placeholder="请选择合同备案签约日"
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="合同编号:" name="contractInfo.contractNumber">
						<a-input v-model:value="formData.contractInfo.contractNumber" placeholder="请输入合同编号" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="承诺结清日期:" name="contractInfo.promiseSettlementDate">
						<a-date-picker
							v-model:value="formData.contractInfo.promiseSettlementDate"
							value-format="YYYY-MM-DD"
							placeholder="请选择承诺结清日期"
							style="width: 100%"
						/>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="车位状态:" name="contractInfo.parkingStatus">
						<a-select
							allowClear
							v-model:value="formData.contractInfo.parkingStatus"
							style="width: 100%"
							placeholder="请选择车位状态"
						>
							<a-select-option value="1">已购买</a-select-option>
							<a-select-option value="2">未购买</a-select-option>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="发票状态:" name="contractInfo.invoiceStatus">
						<a-select
							allowClear
							v-model:value="formData.contractInfo.invoiceStatus"
							style="width: 100%"
							placeholder="请选择发票状态"
						>
							<a-select-option value="1">已开</a-select-option>
							<a-select-option value="2">未开</a-select-option>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="办证申请表/发票:" name="contractInfo.certificateApplication">
						<a-input
							v-model:value="formData.contractInfo.certificateApplication"
							placeholder="办证申请表/发票"
							allow-clear
						/>
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="备注:" name="contractInfo.remark">
						<a-textarea type="textarea" v-model:value="formData.contractInfo.remark" placeholder="备注" allow-clear />
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
		</template>
	</xn-form-container>
	<a-modal :keyboard="false" :maskClosable="false" v-model:visible="userVisible" :title="userTitle" @ok="userHandleOk">
		<a-form
			ref="userForm"
			:model="userData"
			name="basic"
			style="margin-top: 30px"
			:rules="userRules"
			:label-col="{ span: 5 }"
			:wrapper-col="{ span: 16 }"
			autocomplete="off"
		>
			<a-form-item label="姓名" name="name">
				<a-input v-model:value="userData.name" placeholder="请输入姓名" />
			</a-form-item>

			<a-form-item label="身份证号" name="idCard">
				<a-input v-model:value="userData.idCard" placeholder="请输入身份证号" />
			</a-form-item>

			<a-form-item label="手机号" name="phone" v-if="userType == 1">
				<a-input v-model:value="userData.phone" placeholder="请输入手机号" />
			</a-form-item>
		</a-form>
	</a-modal>
	<a-modal :keyboard="false" :maskClosable="false" v-model:visible="payVisible" :title="payTitle" @ok="payHandleOk">
		<a-form
			ref="payForm"
			:model="payData"
			name="basic"
			style="margin-top: 30px"
			:rules="payRules"
			:label-col="{ span: 5 }"
			:wrapper-col="{ span: 16 }"
			autocomplete="off"
		>
			<a-form-item label="交款类型:" name="paymentType" v-if="payType == 2">
				<a-select v-model:value="payData.paymentType" style="width: 100%" placeholder="请选择交款类型">
					<a-select-option :value="item.value" v-for="item in paymentType">{{ item.label }}</a-select-option>
				</a-select>
			</a-form-item>

			<a-form-item label="交款金额:" name="paymentAmount">
				<a-input type="number" v-model:value="payData.paymentAmount" suffix="元" placeholder="请输入交款金额" />
			</a-form-item>
			<a-form-item label="付款时间:" name="paymentTime">
				<a-date-picker
					v-model:value="payData.paymentTime"
					value-format="YYYY-MM-DD"
					placeholder="请选择付款时间："
					style="width: 100%"
				/>
			</a-form-item>
			<a-form-item label="附件:" name="paymentAttachment" v-if="payType == 2">
				<xn-upload v-if="payVisible" v-model:value="payData.paymentAttachment" uploadMode="image" />
			</a-form-item>
		</a-form>
	</a-modal>
</template>

<script setup name="reLeaseContractForm">
	import { cloneDeep } from 'lodash-es'
	import { required, rules } from '@/utils/formRules'
	import reCustomerApi from '@/api/biz/reCustomerApi'
	import reCustomerInfoApi from '@/api/biz/reCustomerInfoApi' //客户信息
	import rePaymentInfoApi from '@/api/biz/rePaymentInfoApi'
	import reLedgerInfoApi from '@/api/biz/reLedgerInfoApi'
	import reSalesContractApi from '@/api/biz/reSalesContractApi' //销售签约
	import reLeaseContractApi from '@/api/biz/reLeaseContractApi' //租赁签约
	import rePlacementContractApi from '@/api/biz/rePlacementContractApi' //安置签约
	import reContractInfoApi from '@/api/biz/reContractInfoApi' //合同信息
	import reHouseApi from '@/api/biz/reHouseApi'
	import tool from '@/utils/tool'
	import { message } from 'ant-design-vue'

	const paymentType = tool.dictList('payment')

	// 抽屉状态
	const visible = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	const userForm = ref()
	const payForm = ref()

	// 表单数据
	const formData = ref({
		shareholderList: [],
		quotaList: []
	})
	const userData = ref({})
	const payData = ref({})
	const submitLoading = ref(false)
	const userVisible = ref(false)
	const payVisible = ref(false)
	const signTypeDisabled = ref(false)
	const value1 = ref('1')
	const value2 = ref('1')
	const userTitle = ref('')
	const userType = ref(0)
	const userIndex = ref(0)
	const payTitle = ref('')
	const payType = ref(0)
	const payIndex = ref(0)
	const options = ref([
		{ label: '销售', value: '1' },
		{ label: '租赁', value: '2' },
		{ label: '安置', value: '3' }
	])
	const payTypeOptions = ref([
		{ label: '一次性付款', value: '1' },
		{ label: '分期全款', value: '2' },
		{ label: '贷款', value: '3' }
	])

	// 打开抽屉
	const onOpen = (record, type) => {
		// 新增
		if (type == 1) {
			record.houseId = record.id
			delete record.id
			let recordData = cloneDeep(record)
			signTypeDisabled.value = false
			formData.value = Object.assign({}, recordData)
			formData.value.customerInfo = {}
			formData.value.saleSign = {
				discountType: '1',
				paymentMethod: '1'
			}
			formData.value.leaseSign = {
				discountType: '1',
				paymentMethod: '1'
			}
			formData.value.placeSign = {
				discountType: '1',
				paymentMethod: '1'
			}
			formData.value.contractInfo = {
				// parkingStatus: '1',
				// invoiceStatus: '1'
			}

			formData.value.signType = '1'
			if (!formData.value.shareholderList) {
				formData.value.shareholderList = []
			}
			if (!formData.value.quotaList) {
				formData.value.quotaList = []
			}
			if (!formData.value.paymentInfos) {
				formData.value.installmentPaymentList = []
				formData.value.paymentInfoList = []
			}

			getHouseDetail(record.houseId)
			visible.value = true
		}
		// 修改
		if (type == 2) {
			record.houseId = record.id
			delete record.id
			let recordData = cloneDeep(record)
			signTypeDisabled.value = true
			getHouseDetail(record.houseId)
			reLedgerInfoApi.reLedgerInfoByHouseId({ id: record.houseId }).then((res) => {
				// 序列化数据
				let infoData = {
					billId: res.ledgerInfo.id,
					villageId: res.customerInfo.villageId,
					houseCode: res.customerInfo.code,
					code: res.customerInfo.code,
					quotaIds: res.customerInfo.quotaIds,
					address: res.customerInfo.address,
					enableArea: res.customerInfo.enableArea,
					subscribeTime: res.customerInfo.subscribeTime,
					name: res.ledgerInfo.name,
					idCard: res.ledgerInfo.idCard,
					phone: res.ledgerInfo.phone,
					customerId: res.customerInfo.customerId,
					id: res.customerInfo.id,
					saleSign: {
						discountType: '1',
						paymentMethod: '1'
					},
					leaseSign: {
						discountType: '1',
						paymentMethod: '1'
					},
					placeSign: {
						discountType: '1',
						paymentMethod: '1'
					},
					contractInfo: {
						// parkingStatus: '1',
						// invoiceStatus: '1'
					},
					signType: res.ledgerInfo.contractType,
					shareholderList: [],
					quotaList: [],
					installmentPaymentList: [],
					paymentInfoList: res.paymentInfos || []
				}
				infoData.shareholderList = res.customerInfo.shareholderList || []
				infoData.quotaList = res.customerInfo.quotaList || []
				infoData.contractInfo = res.contractInfo || {}
				if (res.ledgerInfo.contractType == 1) {
					infoData.saleSign = res.saleSign || { discountType: '1', paymentMethod: '1' }
					infoData.leaseSign = { discountType: '1', paymentMethod: '1' }
					infoData.placeSign = { discountType: '1', paymentMethod: '1' }
					// infoData.contractId:res.saleSign.id||res.placeSign.id||res.leaseSign.id,
					infoData.contractId = res.saleSign.id
					if (res.saleSign.paymentMethod == 2) {
						infoData.installmentPaymentList = res.saleSign.paymentInfoList || []
					}
				}
				if (res.ledgerInfo.contractType == 2) {
					infoData.leaseSign = res.leaseSign || { discountType: '1', paymentMethod: '1' }
					infoData.saleSign = { discountType: '1', paymentMethod: '1' }
					infoData.placeSign = { discountType: '1', paymentMethod: '1' }
					infoData.contractId = res.leaseSign.id
					if (res.leaseSign.paymentMethod == 2) {
						infoData.installmentPaymentList = res.leaseSign.paymentInfoList || []
					}
				}
				if (res.ledgerInfo.contractType == 3) {
					infoData.placeSign = res.placeSign || { discountType: '1', paymentMethod: '1' }
					infoData.saleSign = { discountType: '1', paymentMethod: '1' }
					infoData.leaseSign = { discountType: '1', paymentMethod: '1' }
					infoData.contractId = res.placeSign.id
					if (res.placeSign.paymentMethod == 2) {
						infoData.installmentPaymentList = res.placeSign.paymentInfoList || []
					}
				}
				formData.value = Object.assign({}, { ...recordData, ...infoData })
				visible.value = true
				computedTotal()
			})
		}
	}

	const getHouseDetail = (id) => {
		reHouseApi.reHouseDetail({ id: id }).then((res) => {
			delete res.id
			formData.value = { ...formData.value, ...res }
		})
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
		formRef.value.resetFields()
		// formData.value = {}
	}
	// 默认要校验的
	const formRules = {
		villageId: [{ required: true, message: '请输入村落编号', trigger: 'change' }],
		houseCode: [{ required: true, message: '请输入选房序号', trigger: 'change' }],
		subscribeTime: [{ required: true, message: '请选择认购时间', trigger: 'change' }],
		name: [{ required: true, message: '请输入姓名', trigger: 'change' }],
		idCard: [{ required: true, message: '请输入身份证号', trigger: 'change' }, rules.idCard],
		phone: [{ required: true, message: '请输入电话', trigger: 'change' }, rules.phone]
	}

	// 人员名额要校验的
	const userRules = {
		name: [{ required: true, message: '请输入姓名', trigger: 'change' }],
		idCard: [{ required: true, message: '请输入身份证号', trigger: 'change' }, rules.idCard],
		phone: [{ required: true, message: '请输入电话', trigger: 'change' }, rules.phone]
	}

	// 支付要校验的
	const payRules = {
		paymentAmount: [{ required: true, message: '请输入交款金额', trigger: 'change' }],
		paymentType: [{ required: true, message: '请选择交款类型', trigger: 'change' }],
		paymentTime: [{ required: true, message: '请选择交款时间', trigger: 'change' }]
	}
	// 验证并提交数据
	const onSubmit = () => {
		formRef.value.validate().then(async () => {
			submitLoading.value = true
			const formDataParam = cloneDeep(formData.value)
			//处理数据
			// 共有人id 名额人id
			let shareholderIds = formDataParam.shareholderList.map((obj) => obj.id)
			let quotaIds = formDataParam.quotaList.map((obj) => obj.id)
			// 付款信息  交款信息
			let installmentPaymentIds = formDataParam.installmentPaymentList.map((obj) => obj.id)
			let paymentInfoIds = formDataParam.paymentInfoList.map((obj) => obj.id)
			let parasmData = {
				ledgerId: formDataParam.billId,
				id: formDataParam.id,
				houseId: formDataParam.houseId,
				customerInfo: {
					villageId: formDataParam.villageId,
					code: formDataParam.houseCode,
					address: formDataParam.address,
					subscribeTime: formDataParam.subscribeTime,
					name: formDataParam.name,
					idCard: formDataParam.idCard,
					phone: formDataParam.phone,
					shareholderIds: shareholderIds.toString(), //共有人id
					quotaIds: quotaIds.toString(), //名额人id
					houseId: formDataParam.houseId,
					enableArea: formDataParam.enableArea,
					ledgerId: formDataParam.billId,
					id: formDataParam.customerId,
					customerId: formDataParam.customerId,
					isLedger: true
				},
				signType: formDataParam.signType,
				saleSign: {},
				placeSign: {},
				leaseSign: {},
				installmentPaymentIds: installmentPaymentIds.toString(), //分期付款Ids
				paymentInfoIds: paymentInfoIds.toString(), //交款信息Ids
				contractInfo: formDataParam.contractInfo
			}
			if (formDataParam.signType == 1) {
				parasmData.saleSign = { ...formDataParam.saleSign, ledgerId: formDataParam.billId }
			}
			if (formDataParam.signType == 2) {
				parasmData.leaseSign = { ...formDataParam.leaseSign, ledgerId: formDataParam.billId }
			}
			if (formDataParam.signType == 3) {
				parasmData.placeSign = { ...formDataParam.placeSign, ledgerId: formDataParam.billId }
			}

			// submitLoading.value = false
			//修改 分模块单个修改
			if (parasmData.ledgerId) {
				try {
					//修改客户信息
					await reCustomerInfoApi.reCustomerInfoSubmitForm(
						parasmData.customerInfo,
						parasmData.customerInfo.customerId,
						2
					)
					//修改客户管理信息
					let pars = cloneDeep(parasmData.customerInfo)
					pars.id = parasmData.id
					await reCustomerApi.reCustomerSubmitForm(pars, pars.id, 2)
					// 修改签约信息
					if (formDataParam.signType == 1) {
						await reSalesContractApi.reSalesContractSubmitForm(parasmData.saleSign, parasmData.saleSign.id, 2)
					}
					if (formDataParam.signType == 2) {
						await reLeaseContractApi.reLeaseContractSubmitForm(parasmData.leaseSign, parasmData.leaseSign.id, 2)
					}
					if (formDataParam.signType == 3) {
						await rePlacementContractApi.rePlacementContractSubmitForm(parasmData.placeSign, parasmData.placeSign.id, 2)
					}
					//修改合同信息
					await reContractInfoApi.reContractInfoSubmitForm(parasmData.contractInfo, parasmData.contractInfo.id, 2)
					onClose()
					setTimeout(() => {
						emit('successful')
						message.success('修改成功')
					}, 500)
				} catch (error) {
					message.error('修改失败')
				} finally {
					submitLoading.value = false
				}
			} else {
				reLedgerInfoApi
					.reLedgerInfoSubmitForm(parasmData, parasmData.id)
					.then(() => {
						onClose()
						emit('successful')
					})
					.finally(() => {
						submitLoading.value = false
					})
			}
		})
	}

	//添加客户
	const addCus = (e) => {
		userType.value = e
		if (e == 1) {
			userTitle.value = '添加共有人信息'
		}
		if (e == 2) {
			userTitle.value = '添加名额信息'
		}
		userData.value = {}
		userVisible.value = true
	}
	//修改客户
	const editUser = (e, index, item) => {
		userType.value = e
		userIndex.value = index
		userData.value = cloneDeep(item)
		userVisible.value = true
	}

	// 删减行
	const delUser = (e, index, id) => {
		userType.value = e
		if (e == 1) {
			formData.value.shareholderList.splice(index, 1)
		}
		if (e == 2) {
			formData.value.quotaList.splice(index, 1)
		}
		deleteReCustomerInfo({ id })
	}
	// 删除客户
	const deleteReCustomerInfo = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		reCustomerInfoApi.reCustomerInfoDelete(params).then(() => {
			table.value.refresh(true)
		})
	}

	// 人添加确定
	const userHandleOk = (e) => {
		userForm.value.validate().then(() => {
			const formDataParam = cloneDeep(userData.value)
			reCustomerInfoApi
				.reCustomerInfoSubmitForm(formDataParam, formDataParam.id)
				.then((res) => {
					//修改
					if (formDataParam.id) {
						if (userType.value == 1) {
							formData.value.shareholderList[userIndex.value] = formDataParam
						}
						if (userType.value == 2) {
							formData.value.quotaList[userIndex.value] = formDataParam
						}
					} else {
						formDataParam.id = res
						//共有人数组
						if (userType.value == 1) {
							formData.value.shareholderList.push(formDataParam)
						}
						// 名额数组
						if (userType.value == 2) {
							formData.value.quotaList.push(formDataParam)
						}
					}

					formData.value.persionNum = formData.value.quotaList.length
				})
				.finally(() => {
					userVisible.value = false
					userForm.value.resetFields()
				})
		})
	}

	//添加支付
	const addPay = (e) => {
		payType.value = e
		if (e == 1) {
			payTitle.value = '添加分期付款信息'
		}
		if (e == 2) {
			payTitle.value = '添加交款信息'
		}
		payData.value = {}
		payVisible.value = true
	}
	//修改支付
	const editPay = (e, index, item) => {
		payType.value = e
		payIndex.value = index
		payData.value = cloneDeep(item)
		payVisible.value = true
	}

	// 删减支付行
	const delPay = (e, index, id) => {
		payType.value = e
		payIndex.value = index
		deletePay({ id })
		if (formData.value.signType == 1) {
			// 计算销售全款分期合计合计
			xshjBlur()
		}
		if (formData.value.signType == 2) {
			// 计算租赁全款分期合计合计
			zlhjBlur()
		}
		if (formData.value.signType == 3) {
			// 计算贷款分期合计合计
			azhjBlur()
		}
	}

	// 删除支付
	const deletePay = (record) => {
		let params = [
			{
				id: record.id,
				extJson: 'house'
			}
		]
		rePaymentInfoApi.rePaymentInfoDelete(params).then(() => {
			if (payType.value == 1) {
				formData.value.installmentPaymentList.splice(payIndex.value, 1)
			}
			if (payType.value == 2) {
				formData.value.paymentInfoList.splice(payIndex.value, 1)
				computedTotal()
			}
		})
	}

	//付款添加确定
	const payHandleOk = () => {
		payForm.value.validate().then(() => {
			const formDataParam = cloneDeep(payData.value)
			formDataParam.type = payType.value
			formDataParam.extJson = 'house'
			if (payType.value == 1) {
				formDataParam.paymentType = '-1'
			}
			formDataParam.contractId = formData.value.contractId || null
			formDataParam.ledgerId = formData.value.billId || null
			rePaymentInfoApi
				.rePaymentInfoSubmitForm(formDataParam, formDataParam.id)
				.then((res) => {
					//修改
					if (formDataParam.id) {
						if (payType.value == 1) {
							formData.value.installmentPaymentList[payIndex.value] = formDataParam
							if (formData.value.signType == 1) {
								// 计算销售合计
								xshjBlur()
							}
							if (formData.value.signType == 2) {
								// 计算租赁全款分期合计
								zlhjBlur()
							}
							if (formData.value.signType == 3) {
								// 计算贷款合计
								azhjBlur()
							}
						}
						if (payType.value == 2) {
							formData.value.paymentInfoList[payIndex.value] = formDataParam

							computedTotal()
						}
					} else {
						formDataParam.id = res
						//分期付款记录
						if (payType.value == 1) {
							formData.value.installmentPaymentList.push(formDataParam)
							if (formData.value.signType == 1) {
								// 计算销售合计
								xshjBlur()
							}
							if (formData.value.signType == 2) {
								// 计算租赁全款分期合计
								zlhjBlur()
							}
							if (formData.value.signType == 3) {
								// 计算贷款合计
								azhjBlur()
							}
						}
						// 付款记录
						if (payType.value == 2) {
							formData.value.paymentInfoList.push(formDataParam)
							computedTotal()
						}
					}
				})
				.finally(() => {
					payVisible.value = false
					payForm.value.resetFields()
				})
		})
	}
	// 以下是计算---------------------------------------------------------------------
	const unitPriceBlur = (e) => {
		if (formData.value.saleSign.unitPrice) {
			formData.value.saleSign.totalPrice = formData.value.saleSign.unitPrice * formData.value.actualBuildArea
		} else {
			formData.value.saleSign.totalPrice = 0
		}
		saleSignDisBlur()
		computedTotal()
	}

	const saleSignDisBlur = (e) => {
		if (formData.value.saleSign.discountType == '1') {
			// 签约总价
			let price =
				formData.value.saleSign.totalPrice *
				((formData.value.saleSign.discount ? formData.value.saleSign.discount : 100) / 100)
			formData.value.saleSign.contractPrice = Number(price.toFixed(2))
			// 签约单价
			let UnitPrice = price / formData.value.actualBuildArea
			formData.value.saleSign.contractUnitPrice = Number(UnitPrice.toFixed(2))
		} else {
			// 签约总价
			let price =
				formData.value.saleSign.totalPrice - (formData.value.saleSign.discount ? formData.value.saleSign.discount : 0)
			formData.value.saleSign.contractPrice = Number(price.toFixed(2))
			// 签约单价
			let UnitPrice = price / formData.value.actualBuildArea
			formData.value.saleSign.contractUnitPrice = Number(UnitPrice.toFixed(2))
		}
		if (formData.value.saleSign.maintenanceFundUnitPrice) {
			maintenanceBlur()
		}
		computedTotal()
	}

	const maintenanceBlur = () => {
		// 维修基金总计
		let price = formData.value.saleSign.maintenanceFundUnitPrice * formData.value.actualBuildArea
		formData.value.saleSign.maintenanceFund = Number(price.toFixed(2))
		//房款合计
		let totals = formData.value.saleSign.contractPrice + formData.value.saleSign.maintenanceFund
		formData.value.saleSign.totalHousePrice = Number(totals.toFixed(2))
		if (formData.value.saleSign.paymentMethod == 3) {
			xsdkhjBlur()
		}
		computedTotal()
	}

	const leaseUnitPriceBlur = () => {
		let totalPrice = 0
		if (formData.value.leaseSign.discountType == '1') {
			// 租赁总价 = 租赁价格*真实租期*面积
			totalPrice =
				Number(formData.value.leaseSign.leaseUnitPrice) *
				Number(formData.value.leaseSign.leaseTerm - formData.value.leaseSign.rentFreePeriod) *
				Number(formData.value.actualBuildArea) *
				Number((formData.value.leaseSign.discount ? formData.value.leaseSign.discount : 100) / 100)
		} else {
			// 租赁总价 = 租赁价格*真实租期*面积
			totalPrice =
				Number(formData.value.leaseSign.leaseUnitPrice) *
					Number(formData.value.leaseSign.leaseTerm - formData.value.leaseSign.rentFreePeriod) *
					Number(formData.value.actualBuildArea) -
				Number(formData.value.leaseSign.discount ? formData.value.leaseSign.discount : 0)
		}
		formData.value.leaseSign.leaseTotalPrice = Number(totalPrice.toFixed(2))
		formData.value.leaseSign.totalHousePrice = Number(totalPrice.toFixed(2))
		// 租赁单价 = 租赁总价/租期/面积
		// let unit =
		// 	totalPrice /
		// 	(formData.value.leaseSign.leaseTerm - formData.value.leaseSign.rentFreePeriod) /
		// 	formData.value.actualBuildArea
		// formData.value.leaseSign.leaseUnitPrice = Number(unit.toFixed(2))
		computedTotal()
	}

	const benchmarkBlur = () => {
		// 计算小计
		if (formData.value.placeSign.benchmarkPrice && formData.value.placeSign.area) {
			let price = formData.value.placeSign.benchmarkPrice * formData.value.placeSign.area
			formData.value.placeSign.subtotal = Number(price.toFixed(2))
		} else {
			// 清空小计
			formData.value.placeSign.subtotal = 0
		}
		preferential()
		computedTotal()
	}
	const subsidyBlur = () => {
		// 计算小计
		if (formData.value.placeSign.subsidyPrice && formData.value.placeSign.subsidyArea) {
			let price = formData.value.placeSign.subsidyPrice * formData.value.placeSign.subsidyArea
			formData.value.placeSign.subsidySubtotal = Number(price.toFixed(2))
		} else {
			// 清空小计
			formData.value.placeSign.subsidySubtotal = 0
		}
		preferential()
		computedTotal()
	}
	const marketBlur = () => {
		// 计算小计
		if (formData.value.placeSign.marketPrice && formData.value.placeSign.marketArea) {
			let price = formData.value.placeSign.marketPrice * formData.value.placeSign.marketArea
			formData.value.placeSign.marketSubtotal = Number(price.toFixed(2))
		} else {
			// 清空小计
			formData.value.placeSign.marketSubtotal = 0
		}
		preferential()
		computedTotal()
	}

	const preferential = () => {
		let totalPrice = 0
		// 计算执行优惠
		if (formData.value.placeSign.discountType == '1') {
			totalPrice =
				((formData.value.placeSign.subtotal ? formData.value.placeSign.subtotal : 0) +
					(formData.value.placeSign.subsidySubtotal ? formData.value.placeSign.subsidySubtotal : 0) +
					(formData.value.placeSign.marketSubtotal ? formData.value.placeSign.marketSubtotal : 0)) *
				((formData.value.placeSign.discount ? formData.value.placeSign.discount : 100) / 100)
			formData.value.placeSign.contractPrice = Number(totalPrice.toFixed(2))
		} else {
			totalPrice =
				(formData.value.placeSign.subtotal ? formData.value.placeSign.subtotal : 0) +
				(formData.value.placeSign.subsidySubtotal ? formData.value.placeSign.subsidySubtotal : 0) +
				(formData.value.placeSign.marketSubtotal ? formData.value.placeSign.marketSubtotal : 0) -
				(formData.value.placeSign.discount ? formData.value.placeSign.discount : 0)
			formData.value.placeSign.contractPrice = Number(totalPrice.toFixed(2))
		}
		// 总价变化合计也变化
		maintenanceFundBlur()
	}

	// 计算合计
	const maintenanceFundBlur = () => {
		let price =
			(formData.value.placeSign.contractPrice ? formData.value.placeSign.contractPrice : 0) +
			Number(formData.value.placeSign.maintenanceFund ? formData.value.placeSign.maintenanceFund : 0)

		formData.value.placeSign.total = Number(price.toFixed(2))
		formData.value.placeSign.totalHousePrice = Number(price.toFixed(2))
		if (formData.value.placeSign.paymentMethod == 3) {
			azdkhjBlur()
		}
		computedTotal()
	}

	// 销售分期付款的房款合计 定金 + 付款列表已交的
	const xshjBlur = () => {
		let price = 0
		if (formData.value.installmentPaymentList.length > 0) {
			formData.value.installmentPaymentList.forEach((el) => {
				price += Number(el.paymentAmount)
			})
			let totals = Number(formData.value.saleSign.earnestMoney) + price
			formData.value.saleSign.totalHousePrice = Number(totals.toFixed(2))
		} else {
			let totals = Number(formData.value.saleSign.earnestMoney) + 0
			formData.value.saleSign.totalHousePrice = Number(totals.toFixed(2))
		}
	}

	// 销售贷款的合计  首付+贷款+维修基金
	const xsdkhjBlur = () => {
		let price =
			Number(formData.value.saleSign.firstPayment) +
			Number(formData.value.saleSign.loan) +
			Number(formData.value.saleSign.maintenanceFund)
		formData.value.saleSign.totalHousePrice = Number(price.toFixed(2))
	}

	// 租赁分期付款的房款合计 定金 + 付款列表已交的
	const zlhjBlur = () => {
		let price = 0
		if (formData.value.installmentPaymentList.length > 0) {
			formData.value.installmentPaymentList.forEach((el) => {
				price += Number(el.paymentAmount)
			})
			let totals = Number(formData.value.leaseSign.earnestMoney) + price
			formData.value.leaseSign.totalHousePrice = Number(totals.toFixed(2))
		} else {
			let totals = Number(formData.value.leaseSign.earnestMoney) + 0
			formData.value.leaseSign.totalHousePrice = Number(totals.toFixed(2))
		}
		computedTotal()
	}

	// 租赁贷款的合计  首付+贷款+维修基金
	const zldkhjBlur = () => {
		let price = Number(formData.value.leaseSign.firstPayment) + Number(formData.value.leaseSign.loan)
		formData.value.leaseSign.totalHousePrice = Number(price.toFixed(2))
		computedTotal()
	}

	// 安置分期付款的房款合计 定金 + 付款列表已交的
	const azhjBlur = () => {
		let price = 0
		if (formData.value.installmentPaymentList.length > 0) {
			formData.value.installmentPaymentList.forEach((el) => {
				price += Number(el.paymentAmount)
			})
			let totals = Number(formData.value.placeSign.earnestMoney) + price
			formData.value.placeSign.totalHousePrice = Number(totals.toFixed(2))
		} else {
			let totals = Number(formData.value.placeSign.earnestMoney) + 0
			formData.value.placeSign.totalHousePrice = Number(totals.toFixed(2))
		}
	}

	// 安置贷款的合计  首付+贷款+维修基金
	const azdkhjBlur = () => {
		let price = Number(formData.value.placeSign.firstPayment) + Number(formData.value.placeSign.loan) // Number(formData.value.placeSign.maintenanceFund) 维修基金
		formData.value.placeSign.totalHousePrice = Number(price.toFixed(2))
	}

	// 算交款信息的合计
	const computedTotal = () => {
		//算房款记录合计
		if (formData.value.paymentInfoList.length > 0) {
			let total = 0
			formData.value.paymentInfoList.forEach((el) => {
				total += Number(el.paymentAmount)
			})
			formData.value.PaymentTotal = total.toFixed(1)
		} else {
			formData.value.PaymentTotal = 0
		}

		//计算剩余款
		if (formData.value.signType == 1) {
			formData.value.arrears = (
				Number(formData.value.saleSign.totalHousePrice) - Number(formData.value.PaymentTotal)
			).toFixed(2)
		}
		if (formData.value.signType == 2) {
			formData.value.arrears = (
				Number(formData.value.leaseSign.leaseTotalPrice) - Number(formData.value.PaymentTotal)
			).toFixed(2)
		}
		if (formData.value.signType == 3) {
			formData.value.arrears = (Number(formData.value.placeSign.total) - Number(formData.value.PaymentTotal)).toFixed(2)
		}
		if (formData.value.arrears > 0) {
			formData.value.settleStatus = '未结清'
		}
		if (formData.value.arrears <= 0) {
			formData.value.settleStatus = '已结清'
		}
	}

	// 切换重新计算总价
	const paymentMethodChange = (e) => {
		if (formData.value.signType == 1) {
			if (e.target.value == 1) {
				maintenanceBlur()
			}
			if (e.target.value == 2) {
				xshjBlur()
			}
			if (e.target.value == 3) {
				xsdkhjBlur()
			}
		}
		if (formData.value.signType == 2) {
			if (e.target.value == 1) {
				leaseUnitPriceBlur()
			}
			if (e.target.value == 2) {
				zlhjBlur()
			}
			if (e.target.value == 3) {
				zldkhjBlur()
			}
		}
		computedTotal()
	}

	const discountChange = () => {
		saleSignDisBlur()
	}

	const leaseSignChange = () => {
		leaseUnitPriceBlur()
	}

	// 抛出函数
	defineExpose({
		onOpen
	})
</script>

<style scoped lang="less">
	.form-row {
		background-color: var(--item-hover-bg);
		margin-left: 0 !important;
		margin-bottom: 10px;
	}
	.form-row-con {
		padding-bottom: 5px;
		padding-top: 5px;
		padding-left: 15px;
		text-align: center;
	}
</style>
<style>
	input::-webkit-outer-spin-button,
	input::-webkit-inner-spin-button {
		-webkit-appearance: none;
	}
	input[type='number'] {
		-moz-appearance: textfield;
	}
</style>
