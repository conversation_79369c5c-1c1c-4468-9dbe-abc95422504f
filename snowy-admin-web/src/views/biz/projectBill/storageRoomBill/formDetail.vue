<template>
	<a-drawer
		title="房屋详情"
		:width="1000"
		:visible="visible"
		:destroy-on-close="true"
		:footer-style="{ textAlign: 'right' }"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="horizontal">
			<a-row :gutter="24">
				<a-col :span="5">
					<a-form-item label="楼号：" name="code">
						<span>{{ formData.code }}</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="单元：" name="unit">
						<span>{{ formData.unit }}</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="楼层：" name="floor">
						<span>{{ formData.floor }}</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="房号：" name="houseNumber">
						<span>{{ formData.houseNumber }}</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="建筑面积：" name="actualBuildArea">
						<span>{{ formData.actualBuildArea }}㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="市场价：" name="marketPrice">
						<span>{{ formData.marketPrice }}元/㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="5">
					<a-form-item label="安置价：" name="placementPrice">
						<span>{{ formData.placementPrice }}元/㎡</span>
					</a-form-item>
				</a-col>
				<a-col :span="4">
					<a-form-item label="房屋状态" name="actualBuildArea">
						<span v-for="(dict, num) in houseStatus" :key="num">
							<span v-if="dict.value === formData.status">{{ dict.label }}</span>
						</span>
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<a-tabs v-model:activeKey="activeKey" type="card">
			<a-tab-pane key="1" tab="客户信息">
				<a-form layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="6">
							<a-form-item label="村落编号：" name="villageId">
								<span>{{ houseInfo.customerInfo.villageId || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="选房序号：" name="code">
								<span>{{ houseInfo.customerInfo.code || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="认购时间：" name="subscribeTime">
								<span>{{ houseInfo.customerInfo.subscribeTime || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="姓名：" name="name">
								<span>{{ houseInfo.customerInfo.personInfo.name || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="身份证号：" name="idCard">
								<span>{{ houseInfo.customerInfo.personInfo.idCard || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="电话：" name="phone">
								<span>{{ houseInfo.customerInfo.personInfo.phone || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="地址：" name="address">
								<span>{{ houseInfo.customerInfo.address || '-' }}</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
				<h4>共有人信息</h4>
				<a-table :dataSource="houseInfo.customerInfo.shareholderList" :columns="columns" />
				<h4>名额信息</h4>
				<a-table :dataSource="houseInfo.customerInfo.quotaList" :columns="columns" />
				<a-form layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="6">
							<a-form-item label="人数：" name="code">
								<span>{{ houseInfo.customerInfo.quotaList ? houseInfo.customerInfo.quotaList.length : 0 }}人</span>
							</a-form-item>
						</a-col>
						<a-col :span="6">
							<a-form-item label="启用面积：" name="code">
								<span>{{ houseInfo.customerInfo.enableArea || 0 }}㎡</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
			</a-tab-pane>
			<a-tab-pane key="2" tab="签约信息">
				<a-form layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="8">
							<a-form-item label="销售单价：" name="code">
								<span>{{ houseInfo.saleSign.unitPrice || 0 }}元/㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="销售总价：" name="houseNumber">
								<span>{{ houseInfo.saleSign.totalPrice || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="执行优惠：" name="floor">
								<span>{{ houseInfo.saleSign.discount || '-' }}直减</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="优惠备注：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.discountRemark || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="签约总价：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.contractPrice || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="签约单价：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.contractUnitPrice || 0 }}元/㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="维修基金：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.maintenanceFundUnitPrice || 0 }}元/㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="维修基金：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.maintenanceFund || 0 }}㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="付款形式：" name="actualBuildArea">
								{{ $TOOL.dictTypeData('pay_type', houseInfo.saleSign.paymentMethod) }}
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="定金：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.earnestMoney || 0 }}㎡</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="房款合计：" name="actualBuildArea">
								<span>{{ houseInfo.saleSign.totalHousePrice || 0 }}㎡</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
				<h4>分期付款</h4>
				<a-table :dataSource="houseInfo.saleSign.paymentInfoList" :columns="columns2" />
			</a-tab-pane>
			<a-tab-pane key="3" tab="交款信息">
				<a-form layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="8">
							<a-form-item label="合计交款：" name="code">
								<span>{{ houseInfo.paymentInfos.paymentAmount || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="欠款金额：" name="houseNumber">
								<span>{{ houseInfo.paymentInfos.aa || 0 }}元</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="结清状态：" name="floor">
								<span>{{ houseInfo.paymentInfos.paymentType === 1 ? '交款' : '付款' }}</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
				<h4>付款记录</h4>
				<a-table :dataSource="dataSource" :columns="columns2" />
			</a-tab-pane>
			<a-tab-pane key="4" tab="合同信息">
				<a-form layout="horizontal">
					<a-row :gutter="24">
						<a-col :span="8">
							<a-form-item label="合同备案签约：" name="code">
								<span>{{ houseInfo.contractInfo.contractRecordTime || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="合同编号：" name="houseNumber">
								<span>{{ houseInfo.contractInfo.contractNumber || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="承诺结清日期：" name="floor">
								<span>{{ houseInfo.contractInfo.promiseSettlementDate || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="车位状态：" name="floor">
								{{ $TOOL.dictTypeData('car_status', houseInfo.contractInfo.parkingStatus) }}
							</a-form-item>
						</a-col>
						<a-col :span="8">
							<a-form-item label="发票状态：" name="floor">
								{{ $TOOL.dictTypeData('invoice_status', houseInfo.contractInfo.invoiceStatus) }}
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="办证申请表/发票：" name="floor">
								<span>{{ houseInfo.contractInfo.certificateApplication || '-' }}</span>
							</a-form-item>
						</a-col>
						<a-col :span="24">
							<a-form-item label="备注信息：" name="floor">
								<span>{{ houseInfo.contractInfo.remark || '-' }}</span>
							</a-form-item>
						</a-col>
					</a-row>
				</a-form>
			</a-tab-pane>
			<a-tab-pane key="5" tab="历史台账">
				<a-table :dataSource="dataSource" :columns="columns3" />
			</a-tab-pane>
		</a-tabs>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
		</template>
	</a-drawer>
</template>

<script setup name="reHouseForm">
	import { message } from 'ant-design-vue'
	import { cloneDeep } from 'lodash-es'
	import { required } from '@/utils/formRules'
	import reHouseApi from '@/api/biz/reHouseApi'
	import reLedgerInfoApi from '@/api/biz/reLedgerInfoApi'
	import tool from '@/utils/tool'
	// 房屋朝向
	const houseOrientation = tool.dictList('house_orientation')
	// 房屋户型
	const houseLayout = tool.dictList('house_layout')
	// 房屋类型
	const houseType = tool.dictList('house_type')
	// 房屋状态
	const houseStatus = tool.dictList('house_status')
	// 抽屉状态
	const visible = ref(false)
	const emit = defineEmits({ successful: null })
	const formRef = ref()
	// 表单数据
	const formData = ref({})
	const houseInfo = ref({
		ledgerInfo: {},
		customerInfo: {},
		saleSign: {
			paymentInfoList: []
		},
		placeSign: {},
		leaseSign: {},
		paymentInfos: {},
		contractInfo: {}
	})
	const submitLoading = ref(false)
	const activeKey = ref('1')
	const columns = ref([
		{
			title: '姓名',
			dataIndex: 'name'
		},
		{
			title: '身份证号',
			dataIndex: 'idCard'
		},
		{
			title: '电话',
			dataIndex: 'phone'
		}
	])
	const columns2 = ref([
		{
			title: '交款类型',
			dataIndex: 'paymentType'
		},
		{
			title: '交款金额(元)',
			dataIndex: 'paymentAmount'
		},
		{
			title: '交款时间',
			dataIndex: 'paymentTime'
		},
		{
			title: '附件',
			dataIndex: 'paymentAttachment'
		}
	])
	const columns3 = ref([
		{
			title: '客户姓名',
			dataIndex: 'name'
		},
		{
			title: '客户电话',
			dataIndex: 'age'
		},
		{
			title: '签约类型',
			dataIndex: 'address'
		},
		{
			title: '结清状态',
			dataIndex: 'file'
		},
		{
			title: '签约日期',
			dataIndex: 'file'
		}
	])

	// 打开抽屉
	const onOpen = (record) => {
		console.log(record, '--------')
		if (record.contractStatus) {
			//有台账信息
			visible.value = true
			if (record) {
				let recordData = cloneDeep(record)
				formData.value = Object.assign({}, recordData)
				getDetail(record.id)
			}
		} else {
			message.error('暂无详情数据！')
		}
	}
	const getDetail = (id) => {
		reLedgerInfoApi.reLedgerInfoByHouseId({ id: id }).then((res) => {
			if (!res.paymentInfos) {
				res.paymentInfos = {}
			}
			if (!res.saleSign.paymentInfoList) {
				res.saleSign.paymentInfoList = []
			}
			console.log(res, '--------res')
			houseInfo.value = res
		})
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		formData.value = {}
		visible.value = false
	}
	// 默认要校验的
	const formRules = {}
	// 验证并提交数据
	const onSubmit = () => {
		formRef.value.validate().then(() => {
			submitLoading.value = true
			const formDataParam = cloneDeep(formData.value)
			reHouseApi
				.reHouseSubmitForm(formDataParam, formDataParam.id)
				.then(() => {
					onClose()
					emit('successful')
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}
	// 抛出函数
	defineExpose({
		onOpen
	})
</script>
