<template>
	<div style="display: flex; height: 98%; justify-content: space-between">
		<a-card :bordered="false" class="card-lf">
			<a-input-search v-model:value="searchValue" style="margin-bottom: 8px" placeholder="输入关键字进行过滤" />
			<div class="tree-div" style="height: calc(100% - 28px); overflow: hidden; overflow-y: auto">
				<a-tree
					v-if="gData.length"
					defaultExpandAll
					:tree-data="gData"
					:selectedKeys="selectedKeys"
					:auto-expand-parent="autoExpandParent"
					@expand="onExpand"
					@select="onSelect"
				>
					<template #title="{ title, type }">
						<span v-if="title.indexOf(searchValue) > -1">
							{{ title.substring(0, title.indexOf(searchValue)) }}
							<span style="color: #f50">{{ searchValue }}</span>
							{{ title.substring(title.indexOf(searchValue) + searchValue.length) }}
						</span>
						<span v-else>{{ title }}</span>
					</template>
				</a-tree>
			</div>
		</a-card>
		<a-card :bordered="false" class="card-rt" style="height: 100%">
			<a-breadcrumb style="margin-bottom: 20px">
				<a-breadcrumb-item>{{ projectObj.name }}</a-breadcrumb-item>
				<a-breadcrumb-item v-if="buildObj.code">{{ buildObj.code + '号楼' }}</a-breadcrumb-item>
			</a-breadcrumb>
			<a-form ref="searchFormRef" name="advanced_search" :model="queryParams" class="ant-advanced-search-form">
				<a-row :gutter="24">
					<a-col :span="6">
						<a-input-group compact>
							<a-select v-model:value="searchType" style="width: 35%">
								<a-select-option value="1">房源状态</a-select-option>
								<a-select-option value="2">房源编号</a-select-option>
								<a-select-option value="3">客户姓名</a-select-option>
								<a-select-option value="4">客户电话</a-select-option>
							</a-select>
							<a-select
								v-if="searchType === '1'"
								v-model:value="queryParams.houseStatus"
								placeholder="请选择房源状态"
								:options="houseStatus"
								style="width: 65%"
							/>
							<a-input v-model:value="queryParams.houseCode" allow-clear style="width: 65%" v-if="searchType === '2'" />
							<a-input
								v-model:value="queryParams.customerName"
								allow-clear
								style="width: 65%"
								v-if="searchType === '3'"
							/>
							<a-input
								v-model:value="queryParams.customerPhone"
								allow-clear
								style="width: 65%"
								v-if="searchType === '4'"
							/>
						</a-input-group>
					</a-col>
					<a-col :span="4">
						<a-button type="primary" :icon="h(SearchOutlined)" @click="onSearch">搜索</a-button>
						<a-button class="snowy-button-left" @click="onReset">
							<template #icon><redo-outlined /></template>
							重置
						</a-button>
					</a-col>
					<a-col :span="6">
						<div class="tips">
							<div style="display: flex; align-items: center">
								<div>未售面积：{{ areaStatus.saleArea || '0' }} ㎡</div>
							</div>
							<div style="display: flex; align-items: center">
								<div>已售面积：{{ areaStatus.saolArea || '0' }}㎡</div>
							</div>
						</div>
					</a-col>
					<a-col :span="7">
						<div class="tips">
							<div style="display: flex; align-items: center">
								<div class="tips-color" style="background-color: #838383"></div>
								<div>待售({{ resultObj[Object.keys(resultObj)[0]] }}套)</div>
							</div>
							<div style="display: flex; align-items: center">
								<div class="tips-color" style="background-color: #faad14"></div>
								<div>售出-未结({{ resultObj[Object.keys(resultObj)[1]] }}套)</div>
							</div>
							<div style="display: flex; align-items: center">
								<div class="tips-color" style="background-color: #ff4d4f"></div>
								<div>售出-已结({{ resultObj[Object.keys(resultObj)[2]] }}套)</div>
							</div>
						</div>
					</a-col>
				</a-row>
			</a-form>
			<div class="content">
				<div class="floww" v-if="houseList.length > 0">
					<div
						:class="item.status === '1' ? 'item' : item.status === '2' ? 'item item-o' : 'item item-r'"
						v-for="(item, index) in houseList"
						:key="index"
					>
						<div :class="item.status === '1' ? 'news news-g' : item.status === '2' ? 'news news-o' : 'news news-r'">
							{{ buildObj.code }}号楼{{ item.houseNumber }}号房
						</div>
						<div class="fs">套内面积:{{ item.actualHouseArea || '-' }}㎡</div>
						<div class="fs">建筑面积:{{ item.actualBuildArea || '-' }}㎡</div>
						<div class="fs">销售总价:{{ item.totalPrice || '-' }}</div>
						<div class="sp user">
							<span class="customer">客户：{{ item.customerName || '-' }}</span>
							<span class="customer">电话：{{ item.customerPhone || '-' }}</span>
							<div class="mask">
								<a-tooltip v-if="item.status == 1">
									<template #title>新增</template>
									<plus-outlined
										:style="{ fontSize: '20px', color: '#fff', cursor: 'pointer' }"
										@click="formRef.onOpen({ ...item, buildId: buildObj.id, code: buildObj.code }, 1)"
									/>
								</a-tooltip>
								<a-tooltip>
									<template #title>详情</template>
									<CopyOutlined
										:style="{ fontSize: '20px', color: '#fff', cursor: 'pointer' }"
										@click="detailRef.onOpen({ ...item, buildId: buildObj.id, code: buildObj.code })"
									/>
								</a-tooltip>
								<a-tooltip v-if="item.status == 2 || item.status == 3">
									<template #title>修改</template>
									<FormOutlined
										:style="{ fontSize: '20px', color: '#fff', cursor: 'pointer' }"
										@click="formRef.onOpen({ ...item, buildId: buildObj.id, code: buildObj.code }, 2)"
									/>
								</a-tooltip>
								<a-tooltip v-if="item.status == 2 || item.status == 3">
									<template #title>退款</template>
									<a-popconfirm title="确定要退款吗？" @confirm="checkOut(item)">
										<span
											class="iconfont icon-tui"
											:style="{ fontSize: '22px', color: '#fff', cursor: 'pointer' }"
										></span>
									</a-popconfirm>
								</a-tooltip>
							</div>
						</div>
						<img :src="item.contractStatusUrl" alt="暂无图片" class="statusImg" v-if="item.contractStatusUrl" />
					</div>
				</div>
				<a-empty v-else class="noData" />
			</div>
		</a-card>
	</div>
	<Form ref="formRef" @successful="refreshList" />
	<FormDetail ref="detailRef" @successful="refreshList" />
	<!-- <ImpExp ref="ImpExpRef" /> -->
</template>

<script setup name="rehouse">
	import shoImg from '@/assets/images/sh-o.png'
	import shrImg from '@/assets/images/sh-r.png'
	import zuoImg from '@/assets/images/zu-o.png'
	import zurImg from '@/assets/images/zu-r.png'
	import anoImg from '@/assets/images/an-o.png'
	import anrImg from '@/assets/images/an-r.png'

	import { h } from 'vue'
	import { SearchOutlined } from '@ant-design/icons-vue'
	import Form from './form.vue'
	import FormDetail from '../../rehouse/storeRoomDetail.vue'
	// import ImpExp from './ImpExp.vue'
	import reHouseApi from '@/api/biz/reHouseApi'
	import reBuildingApi from '@/api/biz/reBuildingApi'
	import { useMenuStore } from '@/store/menu'
	import { useRoute, useRouter } from 'vue-router'
	import reProjectApi from '@/api/biz/reProjectApi'

	const route = useRoute()
	const router = useRouter()
	const table = ref()
	const formRef = ref()
	const detailRef = ref()
	const ImpExpRef = ref()
	const searchType = ref('1')
	const queryParams = ref({})
	const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
	import tool from '@/utils/tool'
	import { cloneDeep } from 'lodash-es'

	// 房屋朝向
	const houseOrientation = tool.dictList('house_orientation')
	// 房屋户型
	const houseLayout = tool.dictList('house_layout')
	// 房屋类型
	const houseType = tool.dictList('house_type')
	// 房屋状态
	const houseStatus = tool.dictList('house_status')
	// 签约类型
	const contractType = tool.dictList('contract_type')
	// ---------------------------------------------------------------------

	const menuStore = useMenuStore()
	const searchValue = ref('')
	const searchFormRef = ref()
	const dataList = []

	const expandedKeys = ref([])
	const selectedKeys = ref([])
	const autoExpandParent = ref(true)
	const gData = ref([])
	const houseList = ref([])
	const buildId = ref(null)
	const projectObj = ref({})
	const buildObj = ref({})
	const resultObj = ref({})
	const areaStatus = ref({
		saleArea: 0,
		saolArea: 0
	})
	const onExpand = (keys) => {
		expandedKeys.value = keys
		autoExpandParent.value = true
	}
	const onSelect = (keys, e) => {
		if (keys.length === 0) return
		if (e.node.isParent === 1) return

		buildObj.value = e.node
		selectedKeys.value = keys
		loadData({ id: keys[0], houseType: 3 }) //  houseType 住宅1、商业2、储藏间3
	}
	//重置
	const onReset = () => {
		searchType.value = '1'
		queryParams.value = {}
		searchFormRef.value.resetFields()
		refreshList(queryParams.value)
	}
	//搜索
	const onSearch = () => {
		refreshList(queryParams.value)
	}
	watch(searchValue, (value) => {
		const expanded = dataList
			.map((item) => {
				if (item.title.indexOf(value) > -1) {
					return getParentKey(item.key, gData.value)
				}
				return null
			})
			.filter((item, i, self) => item && self.indexOf(item) === i)
		expandedKeys.value = expanded
		searchValue.value = value
		autoExpandParent.value = true
	})

	// ---------------------------------------------------------------------

	const checkOut = (e) => {
		reHouseApi
			.checkOut({
				reHouseIdParamList: [{ id: e.id }]
			})
			.then((res) => {
				refreshList()
			})
	}
	let selectedRowKeys = ref([])
	// 列表选择配置
	const options = {
		alert: {
			show: false,
			clear: () => {
				selectedRowKeys = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}
	const loadData = (parameter) => {
		reHouseApi.reHouseList(parameter).then((data) => {
			if (data && data.length > 0) {
				// 创建一个映射表
				const contractStatusMapping = {
					12: shoImg,
					13: shrImg,
					22: zuoImg,
					23: zurImg,
					32: anoImg,
					33: anrImg
				}

				data.forEach((item) => {
					if (item.contractStatus && item.status) {
						// 组合 `contractStatus` 和 `status` 作为键
						const key = `${item.contractStatus}${item.status}`
						// 根据键设置 `contractStatusUrl`
						item.contractStatusUrl = contractStatusMapping[key] || null
					}
				})
			}
			houseList.value = data
			// 执行统计
			const result = countStatus(data)
			resultObj.value = result
		})
	}
	// 统计每种状态的数量
	const countStatus = (houses) => {
		const statusCounts = {}
		let saleArea = 0 //待售
		let saolArea = 0 //已售
		// 初始化所有可能的状态值
		;['1', '2', '3'].forEach((status) => {
			statusCounts[status] = 0
		})

		houses.forEach((house) => {
			const status = house.status
			statusCounts[status]++
			if (house.status == 1) {
				saleArea += house.actualBuildArea || 0
			}
			if (house.status == 2 || house.status == 3) {
				saolArea += house.actualBuildArea || 0
			}
		})
		areaStatus.value = {
			saleArea: saleArea.toFixed(2),
			saolArea: saolArea.toFixed(2)
		}
		delete statusCounts[0]
		return statusCounts
	}
	// 删除
	const deleteReHouse = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		reHouseApi.reHouseDelete(params).then(() => {
			refreshList()
		})
	}

	const getReBuildingList = async (data, is) => {
		reBuildingApi
			.reBuildingPage({
				projectId: data.id
			})
			.then((res) => {
				let treeData = [
					{
						...data,
						title: data.name,
						key: data.id,
						children: []
					}
				]
				projectObj.value = data
				if (res.records && res.records.length > 0) {
					res.records.forEach((item) => {
						item.title = item.isParent == 1 ? item.code : item.code + '号楼'
						item.key = item.id
					})
					treeData[0].children = res.records

					selectedKeys.value = []
					selectedKeys.value.push(is.id) //默认树结构选项
					buildId.value = is.id //默认树结构选项ID

					buildObj.value = is //默认树结构选项ID
					loadData({ id: is.id, houseType: 3 }) //  houseType 住宅1、商业2、储藏间3
				}
				gData.value = treeData
			})
	}

	const refreshList = (queryParams) => {
		loadData({ id: buildObj.value.id, houseType: 3, ...queryParams }) //  houseType 住宅1、商业2、储藏间3
	}
	onMounted(() => {
		if (route.query.record) {
			let is = JSON.parse(cloneDeep(route.query.record))
			is.isParent = 1
			reProjectApi.reProjectDetail({ id: is.projectId }).then((res) => {
				getReBuildingList(res, is)
			})
		}
	})
</script>

<style lang="scss" scoped>
	.snowy-button-left {
		margin-left: 8px;
	}
	.card-lf {
		width: 15%;
		height: 100%;
	}

	.card-rt {
		width: 84%;
		height: 100%;
	}

	.tips {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 100%;
	}

	.tips-color {
		width: 12px;
		height: 12px;
		margin-right: 4px;
	}

	.unitName {
		width: 100%;
		height: 40px;
		line-height: 40px;
		font-size: 18px;
		font-weight: bold;
		text-align: center;
	}

	.content {
		width: 100%;
		min-height: 70vh;
		height: 76vh;
		margin-top: 10px;
		position: relative;
		overflow: hidden;
		overflow-y: auto;
		.noData {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}
	}

	.floww {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-start;
		flex-wrap: wrap;
		.item-o {
			background-color: #fffaf1;
		}

		.item-r {
			background-color: #fff2f3;
		}

		.item {
			width: 263px;
			height: 100%;
			border: 1px solid #e0e0e0;
			border-radius: 10px;
			overflow: hidden;
			margin: 0 10px 10px 0;
			position: relative;

			.statusImg {
				width: 80px;
				height: 80px;
				position: absolute;
				top: 50%;
				right: 5%;
				transform: translateY(-50%);
			}

			.news {
				font-size: 14px;
				height: 32px;
				line-height: 32px;
				text-align: center;
				font-weight: 600;
				color: #fff;
			}
			.news-g {
				background-color: #838383;
			}
			.news-o {
				background-color: #faad14;
			}
			.news-r {
				background-color: #ff4d4f;
			}
			.fs {
				height: 32px;
				line-height: 32px;
				padding: 0 5px;
				box-sizing: border-box;
			}
			.sp {
				height: 32px;
				padding: 0 5px;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			.user {
				position: relative;
				.customer {
					width: 50%;
				}
				.mask {
					height: 32px;
					position: absolute;
					left: -99999px;
					right: 0;
					width: 100%;
					display: flex;
					justify-content: space-evenly;
					align-items: center;
					background-color: rgba(0, 0, 0, 0.5);
					cursor: pointer;
				}
			}
		}

		.item:hover .mask {
			left: 0 !important;
		}
	}

	::v-deep .ant-tree-node-selected {
		background-color: #4096ff !important;
		color: #fff;
	}

	::v-deep .card-lf .ant-card-body {
		height: 100% !important;
	}

	/* 滚动条样式 */
	.tree-div::-webkit-scrollbar {
		width: 4px;
	}
	/* 滑块样式 */
	.tree-div::-webkit-scrollbar-thumb {
		background-color: #888;
		border-radius: 10px;
		opacity: 0.5;
	}
	/* 滚动条轨道样式 */
	.tree-div::-webkit-scrollbar-track {
		background-color: #f2f2f2;
		border-radius: 10px;
	}

	/* 滚动条样式 */
	.content::-webkit-scrollbar {
		width: 4px;
	}
	/* 滑块样式 */
	.content::-webkit-scrollbar-thumb {
		background-color: #888;
		border-radius: 10px;
		opacity: 0.5;
	}
	/* 滚动条轨道样式 */
	.content::-webkit-scrollbar-track {
		background-color: #f2f2f2;
		border-radius: 10px;
	}
</style>
