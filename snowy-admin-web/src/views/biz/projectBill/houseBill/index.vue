<template>
	<div style="display: flex; height: 98%; justify-content: space-between">
		<a-card :bordered="false" class="card-lf">
			<a-input-search v-model:value="searchValue" style="margin-bottom: 8px" placeholder="输入关键字进行过滤" />
			<div class="tree-div" style="height: calc(100% - 28px); overflow: hidden; overflow-y: auto">
				<a-tree
					v-if="gData.length"
					defaultExpandAll
					:tree-data="gData"
					:selectedKeys="selectedKeys"
					:auto-expand-parent="autoExpandParent"
					@expand="onExpand"
					@select="onSelect"
				>
					<template #title="{ title, type }">
						<span v-if="title.indexOf(searchValue) > -1">
							{{ title.substring(0, title.indexOf(searchValue)) }}
							<span style="color: #f50">{{ searchValue }}</span>
							{{ title.substring(title.indexOf(searchValue) + searchValue.length) }}
						</span>
						<span v-else>{{ title }}</span>
					</template>
				</a-tree>
			</div>
		</a-card>
		<a-card :bordered="false" class="card-rt" style="height: 100%">
			<a-breadcrumb style="margin-bottom: 20px">
				<a-breadcrumb-item>{{ projectObj.name }}</a-breadcrumb-item>
				<a-breadcrumb-item v-if="buildObj.code">{{ buildObj.code + '号楼' }}</a-breadcrumb-item>
			</a-breadcrumb>
			<a-form ref="searchFormRef" name="advanced_search" :model="queryParams" class="ant-advanced-search-form">
				<a-row :gutter="24">
					<a-col :span="6">
						<a-input-group compact>
							<a-select v-model:value="searchType" style="width: 35%">
								<a-select-option value="1">房源状态</a-select-option>
								<a-select-option value="2">房源编号</a-select-option>
								<a-select-option value="3">客户姓名</a-select-option>
								<a-select-option value="4">客户电话</a-select-option>
							</a-select>
							<a-select
								v-if="searchType === '1'"
								v-model:value="queryParams.houseStatus"
								placeholder="请选择房源状态"
								:options="houseStatus"
								style="width: 65%"
							/>
							<a-input v-model:value="queryParams.houseCode" allow-clear style="width: 65%" v-if="searchType === '2'" />
							<a-input
								v-model:value="queryParams.customerName"
								allow-clear
								style="width: 65%"
								v-if="searchType === '3'"
							/>
							<a-input
								v-model:value="queryParams.customerPhone"
								allow-clear
								style="width: 65%"
								v-if="searchType === '4'"
							/>
						</a-input-group>
					</a-col>
					<a-col :span="4">
						<a-button type="primary" :icon="h(SearchOutlined)" @click="onSearch">搜索</a-button>
						<a-button class="snowy-button-left" @click="onReset">
							<template #icon><redo-outlined /></template>
							重置
						</a-button>
					</a-col>
					<a-col :span="6">
						<div class="tips">
							<div style="display: flex; align-items: center">
								<div>未售面积：{{ areaStatus.saleArea || '0' }}㎡</div>
							</div>
							<div style="display: flex; align-items: center">
								<div>已售面积：{{ areaStatus.saolArea || '0' }} ㎡</div>
							</div>
						</div>
					</a-col>
					<a-col :span="7">
						<div class="tips">
							<div style="display: flex; align-items: center">
								<div class="tips-color" style="background-color: #838383"></div>
								<div>待售({{ resultObj[Object.keys(resultObj)[0]] }}套)</div>
							</div>
							<div style="display: flex; align-items: center">
								<div class="tips-color" style="background-color: #faad14"></div>
								<div>售出-未结({{ resultObj[Object.keys(resultObj)[1]] }}套)</div>
							</div>
							<div style="display: flex; align-items: center">
								<div class="tips-color" style="background-color: #ff4d4f"></div>
								<div>售出-已结({{ resultObj[Object.keys(resultObj)[2]] }}套)</div>
							</div>
						</div>
					</a-col>
				</a-row>
			</a-form>
			<div class="content" v-if="houseList.length > 0">
				<div class="unit" v-for="(item, index) in houseList" :key="index">
					<div class="floor-code" v-if="index == 0">楼号</div>
					<div class="unit-name" :class="index === 0 ? 'unit-name-marginl' : 'unit-name'" style="text-align: center">
						{{ item.unit + '单元' }}
					</div>
					<div class="floor" v-for="(flw, idx) in item.floor" :key="idx">
						<div class="floor-name" v-if="index == 0">{{ flw.floorName + '层' }}</div>
						<div v-if="flw.data.length == 0" class="item" style="width: 263px; height: 162px; border: none"></div>
						<div
							:class="hse.status === '1' ? 'item' : hse.status === '2' ? 'item item-o' : 'item item-r'"
							v-for="(hse, hdx) in flw.data"
						>
							<div :class="hse.status === '1' ? 'news news-g' : hse.status === '2' ? 'news news-o' : 'news news-r'">
								{{ hse.buildCode }}号楼{{ item.unit }}单元{{ hse.floor }}层{{ hse.houseNumber }}号房
							</div>
							<div class="fs">
								户型:{{ hse.houseLayout ? $TOOL.dictTypeData('house_layout', hse.houseLayout) : '-' }}
							</div>
							<div class="sp user">
								<div style="width: 50%">朝向:{{ hse.houseOrientation }}</div>
								<div style="width: 50%">建筑面积:{{ hse.actualBuildArea || '-' }}㎡</div>
							</div>
							<div class="sp user">
								<div style="width: 50%">安置单价:{{ hse.placementPrice || '-' }}</div>
								<div style="width: 50%">市场单价:{{ hse.marketPrice || '-' }}</div>
							</div>
							<div class="sp user">
								<span class="customer">客户：{{ hse.customerName || '-' }}</span>
								<span class="customer">电话：{{ hse.customerPhone || '-' }}</span>
								<div class="mask">
									<a-tooltip v-if="hse.status == 1">
										<template #title>新增台账</template>
										<plus-outlined
											:style="{ fontSize: '20px', color: '#fff', cursor: 'pointer' }"
											@click="formRef.onOpen({ ...hse, buildId: buildObj.id, code: buildObj.code }, 1)"
										/>
									</a-tooltip>
									<a-tooltip>
										<template #title>详情</template>
										<CopyOutlined
											:style="{ fontSize: '20px', color: '#fff', cursor: 'pointer' }"
											@click="detailRef.onOpen({ ...hse, buildId: buildObj.id, code: buildObj.code })"
										/>
									</a-tooltip>
									<a-tooltip v-if="hse.contractStatus == 2">
										<template #title>续租</template>
										<ClockCircleOutlined
											:style="{ fontSize: '20px', color: '#fff', cursor: 'pointer' }"
											@click="
												formRef.onOpen({ ...hse, buildId: buildObj.id, code: buildObj.code, isLeaseRenewal: 1 }, 2)
											"
										/>
									</a-tooltip>
									<a-tooltip v-if="hse.status == 2 || hse.status == 3">
										<template #title>修改</template>
										<FormOutlined
											:style="{ fontSize: '20px', color: '#fff', cursor: 'pointer' }"
											@click="formRef.onOpen({ ...hse, buildId: buildObj.id, code: buildObj.code }, 2)"
										/>
									</a-tooltip>

									<a-tooltip v-if="hse.status == 2 || hse.status == 3">
										<template #title>退房</template>
										<a-popconfirm title="确定要退房吗？" @confirm="checkOut(hse)">
											<span
												class="iconfont icon-tui"
												:style="{ fontSize: '22px', color: '#fff', cursor: 'pointer' }"
											></span>
										</a-popconfirm>
									</a-tooltip>
								</div>
							</div>
							<img :src="hse.contractStatusUrl" alt="暂无图片" class="statusImg" v-if="hse.contractStatusUrl" />
						</div>
					</div>
					<div style="height: 30px"></div>
				</div>
			</div>
			<a-empty v-else class="noData" />
		</a-card>
	</div>
	<Form ref="formRef" @successful="refreshList" />
	<FormDetail ref="detailRef" @successful="refreshList" />
</template>

<script setup name="rehouse">
	import shoImg from '@/assets/images/sh-o.png'
	import shrImg from '@/assets/images/sh-r.png'
	import zuoImg from '@/assets/images/zu-o.png'
	import zurImg from '@/assets/images/zu-r.png'
	import anoImg from '@/assets/images/an-o.png'
	import anrImg from '@/assets/images/an-r.png'

	import { h } from 'vue'
	import { SearchOutlined } from '@ant-design/icons-vue'
	import Form from './form.vue'
	import FormDetail from '../../rehouse/formDetail.vue'
	import reHouseApi from '@/api/biz/reHouseApi'
	import reBuildingApi from '@/api/biz/reBuildingApi'
	import { useMenuStore } from '@/store/menu'
	import { cloneDeep } from 'lodash-es'
	import reProjectApi from '@/api/biz/reProjectApi'
	import { useRoute, useRouter } from 'vue-router'
	const route = useRoute()
	const router = useRouter()
	const table = ref()
	const formRef = ref()
	const detailRef = ref()
	const searchType = ref('1')
	const queryParams = ref({})
	const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
	import tool from '@/utils/tool'
	// 房屋朝向
	const houseOrientation = tool.dictList('house_orientation')
	// 房屋户型
	const houseLayout = tool.dictList('house_layout')
	// 房屋类型
	const houseType = tool.dictList('house_type')
	// 房屋状态
	const houseStatus = tool.dictList('house_status')
	// 签约类型
	const contractType = tool.dictList('contract_type')
	// ---------------------------------------------------------------------

	const menuStore = useMenuStore()
	const searchValue = ref('')
	const searchFormRef = ref()
	const dataList = []

	const expandedKeys = ref([])
	const selectedKeys = ref([])
	const autoExpandParent = ref(true)
	const gData = ref([])
	const houseList = ref([])
	const buildId = ref(null)
	const projectObj = ref({})
	const buildObj = ref({})
	const resultObj = ref({})
	const areaStatus = ref({
		saleArea: 0,
		saolArea: 0
	})

	const onExpand = (keys) => {
		expandedKeys.value = keys
		autoExpandParent.value = true
	}
	const onSelect = (keys, e) => {
		if (keys.length === 0) return
		if (e.node.isParent === 1) return
		buildObj.value = e.node
		selectedKeys.value = keys
		loadData({ id: keys[0], houseType: 1 }) //  houseType 住宅1、商业2、储藏间3
	}

	//重置
	const onReset = () => {
		searchType.value = '1'
		queryParams.value = {}
		searchFormRef.value.resetFields()
		refreshList(queryParams.value)
	}
	//搜索
	const onSearch = () => {
		refreshList(queryParams.value)
	}
	watch(searchValue, (value) => {
		const expanded = dataList
			.map((item) => {
				if (item.title.indexOf(value) > -1) {
					return getParentKey(item.key, gData.value)
				}
				return null
			})
			.filter((item, i, self) => item && self.indexOf(item) === i)
		expandedKeys.value = expanded
		searchValue.value = value
		autoExpandParent.value = true
	})
	// watch(
	// 	() => menuStore.projectObj,
	// 	(newValue, oldValue) => {
	// 		getReBuildingList(newValue)
	// 	}
	// )
	// ---------------------------------------------------------------------
	let selectedRowKeys = ref([])
	// 列表选择配置
	const options = {
		alert: {
			show: false,
			clear: () => {
				selectedRowKeys = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}
	const loadData = (parameter) => {
		reHouseApi.reHouseList(parameter).then(async (data) => {
			let house_data = []
			if (data && data.length > 0) {
				// 创建一个映射表
				const contractStatusMapping = {
					12: shoImg,
					13: shrImg,
					22: zuoImg,
					23: zurImg,
					32: anoImg,
					33: anrImg
				}

				data.forEach((e) => {
					Object.keys(e).forEach((floor) => {
						if (Array.isArray(e[floor])) {
							e[floor].forEach((house) => {
								if (house.contractStatus && house.status) {
									// 组合 `contractStatus` 和 `status` 作为键
									const key = `${house.contractStatus}${house.status}`
									// 根据键设置 `contractStatusUrl`
									house.contractStatusUrl = contractStatusMapping[key] || null
								}
							})
						}
					})
				})
			}
			// 执行统计
			const result = countStatus(data)
			resultObj.value = result
			// 执行排序重新组织数据
			const dataSort = await houseSort(data)

			houseList.value = dataSort

			await nextTick()
		})
	}
	// 统计每种状态的数量
	const countStatus = (houses) => {
		const statusCounts = {}
		//待售面积和已售面积
		let saleArea = 0 //待售
		let saolArea = 0 //已售
		// 初始化所有可能的状态值
		;['1', '2', '3'].forEach((status) => {
			statusCounts[status] = 0
		})

		houses.forEach((unit) => {
			Object.keys(unit).forEach((floor) => {
				if (Array.isArray(unit[floor])) {
					unit[floor].forEach((house) => {
						if (statusCounts.hasOwnProperty(house.status)) {
							const status = house.status
							statusCounts[status]++
						}
						if (house.status == 1) {
							saleArea += house.actualBuildArea || 0
						}
						if (house.status == 2 || house.status == 3) {
							saolArea += house.actualBuildArea || 0
						}
					})
					areaStatus.value = {
						saleArea: saleArea.toFixed(2),
						saolArea: saolArea.toFixed(2)
					}
				}
			})
		})
		return statusCounts
	}
	const houseSort = (data) => {
		let house_data = []
		if (data && data.length > 0) {
			data.forEach((el) => {
				let obj = {
					floor: []
				}
				obj.unit = el.unit
				for (let key in el) {
					if (key !== 'unit') {
						// unshift? 倒叙
						obj.floor.push({
							floorName: key,
							data: el[key]
						})
					}
				}
				house_data.push(obj)
			})
			houseList.value = house_data
		}

		return house_data
	}
	// 删除
	const deleteReHouse = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		reHouseApi.reHouseDelete(params).then(() => {
			refreshList()
		})
	}

	const getReBuildingList = async (data, is) => {
		reBuildingApi
			.reBuildingPage({
				projectId: data.id
			})
			.then((res) => {
				let treeData = [
					{
						...data,
						title: data.name,
						key: data.id,
						children: []
					}
				]
				projectObj.value = data
				if (res.records && res.records.length > 0) {
					res.records.forEach((item) => {
						item.title = item.isParent == 1 ? item.code : item.code + '号楼'
						item.key = item.id
					})
					treeData[0].children = res.records

					selectedKeys.value = []

					selectedKeys.value.push(is.id) //默认树结构选项
					buildId.value = is.id //默认树结构选项ID
					buildObj.value = is //默认树结构选项ID
					loadData({ id: is.id, houseType: 1 }) //  houseType 住宅1、商业2、储藏间3
				}
				gData.value = treeData
			})
	}

	const refreshList = (queryParams) => {
		loadData({ id: buildObj.value.id, houseType: 1, ...queryParams }) //  houseType 住宅1、商业2、储藏间3
	}

	const checkOut = (e) => {
		reHouseApi
			.checkOut({
				reHouseIdParamList: [{ id: e.id }]
			})
			.then((res) => {
				refreshList()
			})
	}

	onMounted(() => {
		if (route.query.record) {
			let is = JSON.parse(cloneDeep(route.query.record))
			is.isParent = 1
			reProjectApi.reProjectDetail({ id: is.projectId }).then((res) => {
				getReBuildingList(res, is)
			})
		}
	})
</script>

<style lang="scss" scoped>
	.snowy-button-left {
		margin-left: 8px;
	}
	.card-lf {
		width: 15%;
		height: 100%;
	}

	.card-rt {
		width: 84%;
		height: 100%;
	}

	.tips {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 100%;
	}

	.tips-color {
		width: 12px;
		height: 12px;
		margin-right: 4px;
	}

	.content {
		width: 100%;
		min-height: 70vh;
		height: 76vh;
		margin-top: 10px;
		position: relative;
		display: flex;
		overflow: hidden;
		overflow-x: auto;
		overflow-y: auto;
		padding-bottom: 10px;
		.noData {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}
	}

	.unit {
		margin-right: 10px;
		position: relative;
		.unitName {
			width: 100%;
			height: 40px;
			line-height: 40px;
			font-size: 18px;
			font-weight: bold;
			text-align: center;
		}

		.floor-code {
			position: absolute;
			top: 0;
			left: 0;
			width: 61px;
			height: 34px;
			text-align: center;
			line-height: 34px;
			font-weight: bold;
			font-size: 15px;
			border: 1px solid #e0e0e0;
		}

		.floor {
			display: flex;
			border-bottom: 1px solid #e0e0e0;
			border-left: 1px solid #e0e0e0;
			border-right: 1px solid #e0e0e0;
		}
		.item-o {
			background-color: #fffaf1;
		}

		.item-r {
			background-color: #fff2f3;
		}

		.unit-name {
			text-align: center;
			height: 34px;
			background-color: #f6f6f6;
			line-height: 34px;
			font-size: 16px;
			font-weight: bold;
		}

		.unit-name-marginl {
			margin-left: 61px;
			text-align: center;
			height: 34px;
			background-color: #f6f6f6;
			line-height: 34px;
			font-size: 16px;
			font-weight: bold;
		}

		.floor-name {
			width: 60px;
			height: 182px;
			display: flex;
			justify-content: center;
			align-items: center;
			font-weight: bold;
			font-size: 16px;
			border-right: 1px solid #e0e0e0;
		}

		.item {
			width: 263px;
			height: 100%;
			border: 1px solid #e0e0e0;
			border-radius: 10px;
			overflow: hidden;
			margin: 10px;
			position: relative;

			.statusImg {
				width: 80px;
				height: 80px;
				position: absolute;
				top: 50%;
				right: 5%;
				transform: translateY(-50%);
			}

			.news {
				font-size: 14px;
				height: 32px;
				line-height: 32px;
				text-align: center;
				font-weight: 600;
				color: #fff;
			}
			.news-g {
				background-color: #838383;
			}
			.news-o {
				background-color: #faad14;
			}
			.news-r {
				background-color: #ff4d4f;
			}
			.fs {
				height: 32px;
				line-height: 32px;
				padding: 0 5px;
				box-sizing: border-box;
			}
			.sp {
				height: 32px;
				padding: 0 5px;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			.user {
				position: relative;
				.customer {
					width: 50%;
				}
				.mask {
					height: 32px;
					position: absolute;
					left: -99999px;
					right: 0;
					width: 100%;
					display: flex;
					justify-content: space-evenly;
					align-items: center;
					background-color: rgba(0, 0, 0, 0.5);
					cursor: pointer;
				}
			}
		}

		.item:hover .mask {
			left: 0 !important;
		}
	}

	::v-deep .ant-tree-node-selected {
		background-color: #4096ff !important;
		color: #fff;
	}

	::v-deep .card-lf .ant-card-body {
		height: 100% !important;
	}

	/* 滚动条样式 */
	.tree-div::-webkit-scrollbar {
		width: 4px;
	}
	/* 滑块样式 */
	.tree-div::-webkit-scrollbar-thumb {
		background-color: #888;
		border-radius: 10px;
		opacity: 0.5;
	}
	/* 滚动条轨道样式 */
	.tree-div::-webkit-scrollbar-track {
		background-color: #f2f2f2;
		border-radius: 10px;
	}

	/* 滚动条样式 */
	.content::-webkit-scrollbar {
		width: 4px;
	}
	/* 滑块样式 */
	.content::-webkit-scrollbar-thumb {
		background-color: #888;
		border-radius: 10px;
		opacity: 0.5;
	}
	/* 滚动条轨道样式 */
	.content::-webkit-scrollbar-track {
		background-color: #f2f2f2;
		border-radius: 10px;
	}
</style>
