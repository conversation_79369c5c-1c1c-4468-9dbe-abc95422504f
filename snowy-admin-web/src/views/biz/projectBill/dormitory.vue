<template>
	<div style="display: flex; height: 98%; justify-content: space-between">
		<a-card :bordered="false" class="card-lf">
			<a-input-search v-model:value="searchValue" style="margin-bottom: 8px" placeholder="输入关键字进行过滤" />
			<a-tree
				v-if="gData.length"
				defaultExpandAll
				:tree-data="gData"
				:selectedKeys="selectedKeys"
				:auto-expand-parent="autoExpandParent"
				@expand="onExpand"
				@select="onSelect"
			>
				<template #title="{ title }">
					<span v-if="title.indexOf(searchValue) > -1">
						{{ title.substring(0, title.indexOf(searchValue)) }}
						<span style="color: #f50">{{ searchValue }}</span>
						{{ title.substring(title.indexOf(searchValue) + searchValue.length) }}
					</span>
					<span v-else>{{ title }}</span>
				</template>
			</a-tree>
		</a-card>
		<a-card :bordered="false" class="card-rt" style="height: 100%">
			<a-breadcrumb style="margin-bottom: 20px">
				<a-breadcrumb-item>{{ projectObj.name }}</a-breadcrumb-item>
				<a-breadcrumb-item>{{ buildObj.code + '号楼' }}</a-breadcrumb-item>
			</a-breadcrumb>
			<a-form ref="searchFormRef" name="advanced_search" :model="queryParams" class="ant-advanced-search-form">
				<a-row :gutter="24">
					<a-col :span="5">
						<a-input-group compact>
							<a-select v-model:value="searchType" style="width: 40%">
								<a-select-option value="1">房源状态</a-select-option>
								<a-select-option value="2">房源编号</a-select-option>
								<a-select-option value="3">客户姓名</a-select-option>
							</a-select>
							<a-select
								v-if="searchType === '1'"
								v-model:value="queryParams.houseStatus"
								placeholder="请选择房源状态"
								:options="houseStatus"
								style="width: 60%"
							/>
							<a-input v-model:value="queryParams.houseNum" allow-clear style="width: 60%" v-if="searchType === '2'" />
							<a-input
								v-model:value="queryParams.customerName"
								allow-clear
								style="width: 60%"
								v-if="searchType === '3'"
							/>
						</a-input-group>
					</a-col>
					<a-col :span="7">
						<a-button type="primary" :icon="h(SearchOutlined)" @click="onSearch">搜索</a-button>
						<a-button class="snowy-button-left" @click="onReset">
							<template #icon><redo-outlined /></template>
							重置
						</a-button>
					</a-col>
					<a-col :span="5">
						<div class="tips">
							<div style="display: flex; align-items: center">
								<div>已售面积：{{ areaData.soldArea || '0' }} ㎡</div>
							</div>
							<div style="display: flex; align-items: center">
								<div>未售面积：{{ areaData.unsoldArea || '0' }}㎡</div>
							</div>
						</div>
					</a-col>
					<a-col :span="7">
						<div class="tips">
							<div style="display: flex; align-items: center">
								<div class="tips-color" style="background-color: #838383"></div>
								<div>待租赁({{ resultObj[Object.keys(resultObj)[0]] }}套)</div>
							</div>
							<div style="display: flex; align-items: center">
								<div class="tips-color" style="background-color: #faad14"></div>
								<div>租赁-未结({{ resultObj[Object.keys(resultObj)[1]] }}套)</div>
							</div>
							<div style="display: flex; align-items: center">
								<div class="tips-color" style="background-color: #ff4d4f"></div>
								<div>租赁-已结({{ resultObj[Object.keys(resultObj)[2]] }}套)</div>
							</div>
						</div>
					</a-col>
				</a-row>
			</a-form>
			<div class="content">
				<div class="floww" v-if="houseList.length > 0">
					<InfiniteScroll @load="onLoad" :finished="finished" :finished-text="finishedText" :loading="loading">
						<div
							:class="item.status === '0' ? 'item' : item.status === '1' ? 'item item-o' : 'item item-r'"
							v-for="(item, index) in houseList"
							:key="index"
						>
							<div :class="item.status === '0' ? 'news news-g' : item.status === '1' ? 'news news-o' : 'news news-r'">
								{{ buildObj.code }}号楼{{ item.floor }}层{{ item.houseNum }}号房
							</div>
							<div class="fs">建筑面积:{{ item.buildArea || '-' }}㎡</div>
							<div class="fs">租赁单价:{{ item.leaseUnitPrice || '-' }}</div>
							<div class="fs">租赁时间:{{ item.leaseTime || '-' }}</div>
							<div class="sp user">
								<span class="customer">客户：{{ item.customerName || '-' }}</span>
								<span class="customer">电话：{{ item.customerPhone || '-' }}</span>
								<div class="mask">
									<a-tooltip v-if="item.status !== '0'">
										<template #title>续租</template>
										<HistoryOutlined
											:style="{ fontSize: '16px', color: '#fff', cursor: 'pointer' }"
											@click="
												formRef.onOpen({
													...item,
													buildId: buildObj.id,
													code: buildObj.code,
													houseType: 'dormitory',
													type: '续租'
												})
											"
										/>
									</a-tooltip>
									<a-tooltip v-if="item.status !== '0'">
										<template #title>补充</template>
										<FileSyncOutlined
											:style="{ fontSize: '16px', color: '#fff', cursor: 'pointer' }"
											@click="
												formRef.onOpen({
													...item,
													buildId: buildObj.id,
													code: buildObj.code,
													houseType: 'dormitory',
													type: '补充'
												})
											"
										/>
									</a-tooltip>
									<a-tooltip v-if="item.status !== '0'">
										<template #title>退房</template>
										<a-popconfirm
											title="是否确认取消该房厂房认购,确认后清除认购信息,厂房销售状态改为待售状态,请谨慎处理！"
											@confirm="resetReHouse(item)"
										>
											<ExceptionOutlined :style="{ fontSize: '16px', color: '#fff', cursor: 'pointer' }" />
										</a-popconfirm>
									</a-tooltip>
									<a-tooltip v-if="item.status === '0'">
										<template #title>新增</template>
										<FileDoneOutlined
											:style="{ fontSize: '16px', color: '#fff', cursor: 'pointer' }"
											@click="
												formRef.onOpen({
													...item,
													buildId: buildObj.id,
													code: buildObj.code,
													houseType: 'dormitory',
													type: '新增'
												})
											"
										/>
									</a-tooltip>
									<a-tooltip>
										<template #title>详情</template>
										<CopyOutlined
											:style="{ fontSize: '16px', color: '#fff', cursor: 'pointer' }"
											@click="
												detailRef.onOpen({ ...item, buildId: buildObj.id, code: buildObj.code, houseType: 'dormitory' })
											"
										/>
									</a-tooltip>
								</div>
							</div>
							<img :src="item.contractStatusUrl" alt="暂无图片" class="statusImg" v-if="item.contractStatusUrl" />
						</div>
					</InfiniteScroll>
				</div>
				<a-empty v-else class="noData" />
			</div>
		</a-card>
	</div>
	<Form ref="formRef" @successful="onReset" />
	<FormDetail ref="detailRef" @successful="refreshList" />
	<ImpExp ref="ImpExpRef" @successful="onReset" />
</template>

<script setup name="rehouse">
	import shoImg from '@/assets/images/sh-o.png'
	import shrImg from '@/assets/images/sh-r.png'
	import zuoImg from '@/assets/images/zu-o.png'
	import zurImg from '@/assets/images/zu-r.png'
	import anoImg from '@/assets/images/an-o.png'
	import anrImg from '@/assets/images/an-r.png'

	import { h } from 'vue'
	import { SearchOutlined } from '@ant-design/icons-vue'
	import Form from './form.vue'
	import FormDetail from './detail.vue'
	import ImpExp from './ImpExp.vue'
	import reGardenHouseApi from '@/api/biz/reGardenHouseApi'
	import reGardenledGerinfo from '@/api/biz/reGardenledGerinfo'
	import reBuildingApi from '@/api/biz/reBuildingApi'
	import { useMenuStore } from '@/store/menu'
	import reProjectApi from '@/api/biz/reProjectApi'
	import { cloneDeep } from 'lodash-es'
	import { useRoute, useRouter } from 'vue-router'
	const route = useRoute()
	const router = useRouter()
	const page = ref(1)
	const size = ref(30)
	const finished = ref(false)
	const loading = ref(false)
	const finishedText = ref('没有更多数据了')

	const table = ref()
	const formRef = ref()
	const detailRef = ref()
	const ImpExpRef = ref()
	const searchType = ref('1')
	const queryParams = ref({})
	const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
	import tool from '@/utils/tool'
	// 房屋朝向
	const houseOrientation = tool.dictList('house_orientation')
	// 房屋户型
	const houseLayout = tool.dictList('house_layout')
	// 房屋类型
	const houseType = tool.dictList('RE_HOUSE_TYPE')
	// 房屋状态
	const houseStatus = tool.dictList('park_house_type')
	// 签约类型
	const contractType = tool.dictList('contract_type')
	// ---------------------------------------------------------------------

	const menuStore = useMenuStore()
	const searchValue = ref('')
	const searchFormRef = ref()
	const dataList = []

	const expandedKeys = ref([])
	const selectedKeys = ref([])
	const autoExpandParent = ref(true)
	const gData = ref([])
	const houseList = ref([])
	const buildId = ref(null)
	const projectObj = ref({})
	const buildObj = ref({
		code: ''
	})
	const resultObj = ref({})
	const areaData = ref({})
	const floorData = ref({})
	const onExpand = (keys) => {
		expandedKeys.value = keys
		autoExpandParent.value = true
	}
	const onSelect = (keys, e) => {
		if (e.node.pos === '0-0') return
		page.value = 1
		houseList.value = []
		if (keys && keys.length > 0) {
			selectedKeys.value = keys
			buildId.value = keys[0]
			if (floorData.value.length > 0) {
				floorData.value.forEach((e) => {
					if (e.id === keys[0]) {
						buildObj.value = e
					}
				})
			}
			loadData({ buildId: keys[0], houseType: 'dormitory' }) // houseType 厂房 workshop  办公楼 officebuilding 宿舍 dormitory
		}
	}

	//重置
	const onReset = () => {
		page.value = 1
		houseList.value = []
		searchType.value = '1'
		queryParams.value = {}
		searchFormRef.value.resetFields()
		refreshList(queryParams.value)
	}
	//搜索
	const onSearch = () => {
		console.log('or use this.value', queryParams.value)
		page.value = 1
		houseList.value = []
		refreshList(queryParams.value)
	}
	watch(searchValue, (value) => {
		const expanded = dataList
			.map((item) => {
				if (item.title.indexOf(value) > -1) {
					return getParentKey(item.key, gData.value)
				}
				return null
			})
			.filter((item, i, self) => item && self.indexOf(item) === i)
		expandedKeys.value = expanded
		searchValue.value = value
		autoExpandParent.value = true
	})

	// ---------------------------------------------------------------------

	const columns = [
		{
			title: '楼号',
			dataIndex: 'buildId'
		},
		{
			title: '楼栋编号',
			dataIndex: 'buildCode'
		},
		{
			title: '楼层',
			dataIndex: 'floor'
		},
		{
			title: '单元',
			dataIndex: 'unit'
		},
		{
			title: '房屋编号',
			dataIndex: 'houseNumber'
		},
		{
			title: '房屋类型--字典（住宅、商业、储藏间）',
			dataIndex: 'houseType'
		},
		{
			title: '预测建筑面积',
			dataIndex: 'forecastBuildArea'
		},
		{
			title: '预测套内面积',
			dataIndex: 'forecastHouseArea'
		},
		{
			title: '实测建筑面积',
			dataIndex: 'actualBuildArea'
		},
		{
			title: '实测套内面积',
			dataIndex: 'actualHouseArea'
		},
		{
			title: '房屋朝向--字典',
			dataIndex: 'houseOrientation'
		},
		{
			title: '房屋户型--字典',
			dataIndex: 'houseLayout'
		},
		{
			title: '安置价',
			dataIndex: 'placementPrice'
		},
		{
			title: '市场价',
			dataIndex: 'marketPrice'
		},
		{
			title: '房源位置',
			dataIndex: 'houseLocation'
		},
		{
			title: '总价--储藏室使用',
			dataIndex: 'totalPrice'
		},
		{
			title: '单价--储藏室使用',
			dataIndex: 'unitPrice'
		},
		{
			title: '项目编号',
			dataIndex: 'projectId'
		},
		{
			title: '房屋状态--字典（待售、售出-未结、售出-已结，租赁）',
			dataIndex: 'status'
		},
		{
			title: '客户姓名',
			dataIndex: 'customerName'
		},
		{
			title: '客户电话',
			dataIndex: 'customerPhone'
		},
		{
			title: '销售单价',
			dataIndex: 'salesUnitPrice'
		},
		{
			title: '销售总价',
			dataIndex: 'salesTotalPrice'
		},
		{
			title: '扩展信息',
			dataIndex: 'extJson'
		}
	]
	// 操作栏通过权限判断是否显示
	columns.push({
		title: '操作',
		dataIndex: 'action',
		align: 'center',
		width: '150px'
	})
	let selectedRowKeys = ref([])
	// 列表选择配置
	const options = {
		alert: {
			show: false,
			clear: () => {
				selectedRowKeys = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}

	//上拉加载更多
	const onLoad = async () => {
		console.log('onLoad', finished.value)
		return
		if (!finished.value) {
			loading.vue = true
			page.value = page.value + 1
			await refreshList(queryParams.value)
			loading.vue = false
		} else {
			finished.value = true
		}
	}
	const loadData = (parameter) => {
		parameter.projectId = menuStore.projectObj.id
		parameter.current = 1
		parameter.size = 10000
		reGardenHouseApi.reGardenHousePage(parameter).then((data) => {
			if (data.records && data.records.length > 0) {
				// 创建一个映射表
				const contractStatusMapping = {
					11: shoImg,
					21: shrImg,
					10: zuoImg,
					20: zurImg
				}

				data.records.forEach((item) => {
					if (item.contractStatus && item.status) {
						// 组合 `contractStatus` 和 `status` 作为键
						const key = `${item.status}${item.contractStatus}`
						// 根据键设置 `contractStatusUrl`
						item.contractStatusUrl = contractStatusMapping[key] || null
					}
				})
			}
			let arr1 = houseList.value
			let arr2 = data.records
			arr1 = arr1.concat(arr2)
			houseList.value = arr1
			finished.value = data.records.length < size.value
			// 执行统计
			const result = countStatus(houseList.value)
			resultObj.value = result
		})
		let obj = Object.assign({}, parameter)
		delete obj.current
		delete obj.size
		reGardenHouseApi.reGardenHouseArea(obj).then((data) => {
			areaData.value = data
		})
	}
	// 统计每种状态的数量
	const countStatus = (houses) => {
		const statusCounts = {}

		// 初始化所有可能的状态值
		;['0', '1', '2'].forEach((status) => {
			statusCounts[status] = 0
		})

		houses.forEach((house) => {
			const status = house.status
			statusCounts[status]++
		})

		return statusCounts
	}
	// 删除
	const deleteReHouse = (record, index) => {
		let params = [
			{
				id: record.id
			}
		]
		houseList.value.splice(index, 1)
		reGardenHouseApi.reGardenHouseDelete(params).then(() => {})
	}

	const getReBuildingList = async (data,is) => {
		reBuildingApi
			.reBuildingPage({
				projectId: data.id
			})
			.then((res) => {
				let treeData = [
					{
						...data,
						title: data.name,
						key: data.id,
						children: []
					}
				]
				if (res.records && res.records.length > 0) {
					res.records.forEach((item) => {
						item.title = item.isParent == 1 ? item.code : item.code + '号楼'
						item.key = item.id
					})
					treeData[0].children = res.records
					floorData.value = res.records

					selectedKeys.value = []
					projectObj.value = data
					selectedKeys.value.push(is.id) //默认树结构选项
					buildId.value = is.id //默认树结构选项ID
					buildObj.value = is //默认树结构选项ID
					loadData({ buildId: is.id, houseType: 'dormitory' }) // houseType 厂房 workshop  办公楼 officebuilding 宿舍 dormitory
				}
				gData.value = treeData
			})
	}

	const refreshList = (queryParams) => {
		loadData({ buildId: buildId.value, houseType: 'dormitory', ...queryParams }) // houseType 厂房 workshop  办公楼 officebuilding 宿舍 dormitory
	}

	const resetReHouse = (item) => {
		reGardenledGerinfo.resetGardenledger(item).then(() => {
			refreshList()
		})
	}
	onMounted(() => {
		if (route.query.record) {
			let is = JSON.parse(cloneDeep(route.query.record))
			is.isParent = 1
			reProjectApi.reProjectDetail({ id: is.projectId }).then((res) => {
				getReBuildingList(res, is)
			})
		}
	})
</script>

<style lang="scss" scoped>
	.snowy-button-left {
		margin-left: 8px;
	}
	.card-lf {
		width: 15%;
		height: 100%;
	}

	.card-rt {
		width: 84%;
		height: 100%;
	}

	.tips {
		display: flex;
		justify-content: space-between;
		align-items: center;
		height: 100%;
	}

	.tips-color {
		width: 12px;
		height: 12px;
		margin-right: 4px;
	}

	.unitName {
		width: 100%;
		height: 40px;
		line-height: 40px;
		font-size: 18px;
		font-weight: bold;
		text-align: center;
	}
	.content {
		width: 100%;
		min-height: 70vh;
		height: 75vh;
		margin-top: 10px;
		position: relative;

		.noData {
			position: absolute;
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%);
		}
	}

	.floww {
		width: 100%;
		height: 100%;
		overflow: hidden;

		&::-webkit-scrollbar {
			width: 4px;
		}
		&::-webkit-scrollbar-thumb {
			background-color: #888;
			border-radius: 10px;
			opacity: 0.5;
		}
		.item-o {
			background-color: #fffaf1;
		}

		.item-r {
			background-color: #fff2f3;
		}

		.item {
			float: left;
			width: 263px;
			border: 1px solid #e0e0e0;
			border-radius: 10px;
			overflow: hidden;
			margin: 0 10px 10px 0;
			position: relative;

			.statusImg {
				width: 80px;
				height: 80px;
				position: absolute;
				top: 50%;
				right: 5%;
				transform: translateY(-50%);
			}

			.news {
				font-size: 14px;
				height: 32px;
				line-height: 32px;
				text-align: center;
				font-weight: 600;
				color: #fff;
			}
			.news-g {
				background-color: #838383;
			}
			.news-o {
				background-color: #faad14;
			}
			.news-r {
				background-color: #ff4d4f;
			}
			.fs {
				height: 32px;
				line-height: 32px;
				padding: 0 5px;
				box-sizing: border-box;
			}
			.sp {
				height: 32px;
				padding: 0 5px;
				box-sizing: border-box;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			.user {
				position: relative;
				.customer {
					width: 50%;
				}
				.mask {
					height: 32px;
					position: absolute;
					left: -99999px;
					right: 0;
					width: 100%;
					display: flex;
					justify-content: space-evenly;
					align-items: center;
					background-color: rgba(0, 0, 0, 0.5);
					cursor: pointer;
				}
				&:hover {
					.mask {
						left: 0;
					}
				}
			}
		}
	}
</style>
