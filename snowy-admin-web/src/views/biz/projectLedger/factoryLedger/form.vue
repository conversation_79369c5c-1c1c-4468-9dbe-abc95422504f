<template>
	<a-drawer :title="title" :width="1000" :visible="visible" :destroy-on-close="true"
		:footer-style="{ textAlign: 'right' }" @close="onClose">
		<a-form ref="formRef" layout="horizontal">
			<h3>房源信息</h3>
			<a-row :gutter="16">
				<a-col :span="8">
					<a-form-item label="楼号：">
						<a-input v-model:value="houseData.buildCode" placeholder="请输入楼号" allow-clear disabled />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="房号：">
						<a-input-number v-model:value="houseData.houseNumber" :min="1" placeholder="请输入房号" allow-clear
							style="width: 100%" disabled />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="建筑面积：">
						<a-input v-model:value="houseData.area" placeholder="请输入建筑面积" allow-clear addon-after="㎡"
							disabled />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="连廊面积：">
						<a-input v-model:value="houseData.corridorArea" placeholder="请输入连廊面积" allow-clear
							addon-after="㎡" disabled />
					</a-form-item>
				</a-col>
			</a-row>
			<h3>客户信息</h3>
			<a-row :gutter="16">
				<a-col :span="24">
					<a-form-item label="入住企业：" v-bind="validateInfos['customer.company']">
						<a-input v-model:value="formData.customer.company" placeholder="请输入入住企业" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="企业法人：" v-bind="validateInfos['customer.legalPerson']">
						<a-input v-model:value="formData.customer.legalPerson" placeholder="请输入企业法人" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="身份证号：">
						<a-input v-model:value="formData.customer.idCard" placeholder="请输入身份证号" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="8"></a-col>
				<a-col :span="8">
					<a-form-item label="企业联络人：" v-bind="validateInfos['customer.contactPerson']">
						<a-input v-model:value="formData.customer.contactPerson" placeholder="请输入企业联络人" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="联系电话：">
						<a-input v-model:value="formData.customer.phone" placeholder="请输入联系电话" allow-clear />
					</a-form-item>
				</a-col>
			</a-row>
			<h3>签约信息</h3>
			<a-row :gutter="16">
				<a-col :span="8">
					<a-form-item label="物业类别：">
						<a-input v-model:value="formData.contract.propertyType" placeholder="请输入物业类别" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="物业现状：">
						<a-input v-model:value="formData.contract.propertyStatus" placeholder="请输入物业现状" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="签约日期：">
						<a-date-picker v-model:value="formData.contract.contractTime" style="width: 100%"
							value-format="YYYY-MM-DD" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="租赁合同：">
						<a-radio-group v-model:value="formData.contract.leaseContract" :options="leaseAgreement" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="入区协议：">
						<a-radio-group v-model:value="formData.contract.entryAgreement" :options="entryAgreement" />
					</a-form-item>
				</a-col>
				<a-col :span="8"></a-col>
				<a-col :span="8">
					<a-form-item label="租赁价格：">
						<a-input v-model:value="formData.contract.leasePrice" placeholder="请输入租赁价格" allow-clear
							addon-after="元/月/㎡" @change="changePrice" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="连廊租金：">
						<a-input v-model:value="formData.contract.corridorRent" placeholder="请输入连廊租金" allow-clear
							addon-after="元" @change="changePrice" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="月租金：">
						<a-input v-model:value="formData.contract.monthlyRent" placeholder="请输入月租金" allow-clear
							addon-after="元" disabled />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="租赁保证金：">
						<a-input v-model:value="formData.contract.leaseDeposit" placeholder="请输入租赁保证金" allow-clear
							addon-after="元" />
					</a-form-item>
				</a-col>
				<a-col :span="8"></a-col>
				<a-col :span="8"></a-col>
				<a-col :span="8">
					<a-form-item label="租赁起始日期：">
						<a-date-picker v-model:value="formData.contract.leaseStartTime" style="width: 100%"
							value-format="YYYY-MM-DD" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="租赁截止日期：">
						<a-date-picker v-model:value="formData.contract.leaseEndTime" style="width: 100%"
							value-format="YYYY-MM-DD" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="租赁计算日期：">
						<a-date-picker v-model:value="formData.contract.leaseCalculationDate" style="width: 100%"
							value-format="YYYY-MM-DD" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="租期：">
						<a-input v-model:value="formData.contract.leaseTerm" placeholder="请输入租期" allow-clear
							addon-after="年" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="免租期：">
						<a-input v-model:value="formData.contract.rentFreePeriod" placeholder="请输入免租期" allow-clear
							addon-after="年" />
					</a-form-item>
				</a-col>
				<a-col :span="8"> </a-col>
				<a-col :span="8">
					<a-form-item label="应缴租金：">
						<a-input v-model:value="formData.contract.rentPayable" placeholder="请输入应缴租金" allow-clear
							addon-after="元" @change="changePayPrice" />
					</a-form-item>
				</a-col>

				<a-col :span="8">
					<a-form-item label="已缴租金：">
						<a-input v-model:value="formData.contract.rentPaid" placeholder="请输入已缴租金" allow-clear
							addon-after="元" @change="changePayPrice" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-form-item label="欠缴租金：">
						<a-input v-model:value="formData.contract.rentArrears" placeholder="请输入欠缴租金" allow-clear
							disabled addon-after="元" />
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="备注：">
						<a-textarea v-model:value="formData.contract.remark" show-count :maxlength="500"
							placeholder="请输入备注" />
					</a-form-item>
				</a-col>
			</a-row>
			<h3>其他信息</h3>
			<a-row :gutter="16">
				<a-col :span="24">
					<a-form-item label="企业优惠政策：">
						<a-textarea v-model:value="formData.other.companyPolicy" show-count :maxlength="500"
							placeholder="请输入企业优惠政策" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="经营产品：">
						<a-input v-model:value="formData.other.businessProduct" placeholder="请输入经营产品" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="投资额：">
						<a-input v-model:value="formData.other.investmentAmount" placeholder="请输入投资额" allow-clear
							addon-after="元" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="员工人数：">
						<a-input v-model:value="formData.other.staffNumber" placeholder="请输入员工人数" allow-clear
							addon-after="个" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="年营业额：">
						<a-input v-model:value="formData.other.annualTurnover" placeholder="请输入年营业额" allow-clear
							addon-after="元" />
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="配套需求：">
						<a-textarea v-model:value="formData.other.supportingRequirements" show-count :maxlength="500"
							placeholder="请输入配套需求" />
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="存在问题：">
						<a-textarea v-model:value="formData.other.existingProblems" show-count :maxlength="500"
							placeholder="请输入存在问题" />
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button style="margin-right: 8px" @click="onClose">关闭</a-button>
			<a-button type="primary" @click.prevent="onSubmit" :loading="submitLoading">保存</a-button>
		</template>
	</a-drawer>
</template>

<script setup name="reHouseForm">
import { message } from 'ant-design-vue'
import { reactive, toRaw } from 'vue'
import { Form } from 'ant-design-vue'
import { cloneDeep } from 'lodash-es'
import { required } from '@/utils/formRules'
import reGardenledGerinfo from '@/api/biz/reGardenledGerinfo'
import tool from '@/utils/tool'
// 园区入区协议
const entryAgreement = tool.dictList('entry_agreement')
// 园区租赁合同
const leaseAgreement = tool.dictList('lease_agreement')
// 抽屉状态
const visible = ref(false)
const emit = defineEmits({ successful: null })
const formRef = ref()

const title = ref(null)
const useForm = Form.useForm
const houseData = ref({})
// 表单数据
const formData = reactive({
	customer: {
		company: '',
		legalPerson: '',
		idCard: '',
		phone: '',
		contactPerson: ''
	},
	contract: {
		monthlyRent: 0,
		rentArrears: 0
	},
	other: {
		supportingRequirements:'',
		existingProblems:''
	}
})

const { resetFields, validate, validateInfos } = useForm(
	formData,
	reactive({
		'customer.company': [{ required: true, message: '请输入入住企业' }],
		'customer.legalPerson': [{ required: true, message: '请输入企业法人' }],
		'customer.idCard': [{ required: false, message: '请输入身份证号' }],
		'customer.phone': [{ required: false, message: '请输入联系电话' }],
		'customer.contactPerson': [{ required: true, message: '请输入企业联络人' }]
	})
)
const submitLoading = ref(false)

// 打开抽屉
const onOpen = async (record) => {
	switch (record.type) {
		case '新增':
			title.value = '新增台账信息'
			visible.value = true
			if (record) {
				let recordData = cloneDeep(record)
				houseData.value = Object.assign({}, recordData)
				formData.houseId = recordData.id
			}
			break
		case '续租':
			title.value = '台账信息续租'
			visible.value = true
			if (record) {
				let recordData = cloneDeep(record)
				houseData.value = Object.assign({}, recordData)
				formData.houseId = recordData.id
				getInfo(record.id)
			}
			break
		case '编辑':
			title.value = '台账信息编辑'
			visible.value = true
			if (record) {
				let recordData = cloneDeep(record)
				houseData.value = Object.assign({}, recordData)
				getInfo(record.id)
			}
			break
	}
}

const getInfo = (id) => {
	reGardenledGerinfo.getGardenledgerInfo({ id: id }).then((res) => {
		if (res.customer) {
			formData.customer = res.customer
		} else {
			formData.customer = {}
		}
		if (res.contract) {
			formData.contract = res.contract
		} else {
			formData.contract = {}
		}
		if (res.other) {
			formData.other = res.other
		} else {
			formData.other = {}
		}
		// if (res.history && res.history.length > 0) {
		// 	formData.history = res.history
		// } else {
		// 	formData.history = {}
		// }

		formData.id = res.id
	})
}
// 关闭抽屉
const onClose = () => {
	formRef.value.resetFields()
	formData.value = {}
	visible.value = false
	resetFields()
}
// 验证并提交数据
const onSubmit = () => {
	validate()
		.then((res) => {
			submitLoading.value = true
			const formDataParam = cloneDeep(formData)
			switch (title.value) {
				case '新增台账信息':
					addForm(formDataParam)
					break
				case '台账信息续租':
					reletForm(formDataParam)
					break
				case '台账信息编辑':
					uploadForm(formDataParam)
					break
			}
		})
		.catch((err) => {
			console.log('error', err)
		})
}

const addForm = (formDataParam) => {
	reGardenledGerinfo
		.AddreGardenledger(formDataParam)
		.then(() => {
			onClose()
			emit('successful')
			message.success('添加成功')
		})
		.finally(() => {
			submitLoading.value = false
		})
}

const reletForm = (formDataParam) => {
	reGardenledGerinfo
		.reletGardenledgerInfo(formDataParam)
		.then(() => {
			onClose()
			emit('successful')
			message.success('续租成功')
		})
		.finally(() => {
			submitLoading.value = false
		})
}
const uploadForm = (formDataParam) => {
	reGardenledGerinfo
		.uploadGardenledgerInfo(formDataParam)
		.then(() => {
			onClose()
			emit('successful')
			message.success('更新成功')
		})
		.finally(() => {
			submitLoading.value = false
		})
}

const changePrice = () => {
	formData.contract.monthlyRent = Number(formData.contract.leasePrice) + Number(formData.contract.corridorRent)
}

const changePayPrice = () => {
	formData.contract.rentArrears = Number(formData.contract.rentPayable) - Number(formData.contract.rentPaid)
}
// 抛出函数
defineExpose({
	onOpen
})
</script>
