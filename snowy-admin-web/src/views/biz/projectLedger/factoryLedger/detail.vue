<template>
    <xn-form-container title="详情" width="65%" :open="visible" :destroy-on-close="false" @close="onClose">
        <!-- <a-drawer v-model:open="open" class="custom-class" root-class-name="root-class-name" title="详情"
            placement="right" @after-open-change="afterOpenChange" width="60%"> -->
        <a-from :model="detailForm">
            <h3 class="drawTitle">客户信息</h3>
            <a-row class="drawRow">
                <a-col :span="8">
                    <a-form-item label="入住企业" >{{ detailForm.customer.company }}</a-form-item>
                </a-col>
                <a-col :span="8"></a-col>
                <a-col :span="8">
                    <a-form-item label="电话">{{ detailForm.customer.phone }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="企业法人">{{ detailForm.customer.legalPerson }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="身份证号">{{ detailForm.customer.idCard }}</a-form-item>
                </a-col>
                <a-col :span="8"></a-col>
                <a-col :span="8">
                    <a-form-item label="企业联络人">{{ detailForm.customer.contactPerson }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="联系电话">{{ detailForm.customer.phone }}</a-form-item>
                </a-col>
            </a-row>
            <h3 class="drawTitle">租售信息</h3>
            <a-row class="drawRow">
                <a-col :span="8">
                    <a-form-item label="楼号">{{ detailForm.buildCode }}</a-form-item>
                </a-col :span="8">
                <a-col :span="8">
                    <a-form-item label="房号">{{ detailForm.houseNumber }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="建筑面积">{{ detailForm.area }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="连廊面积">{{ detailForm.corridorArea }}</a-form-item>
                </a-col>
            </a-row>


            <h3 class="drawTitle">签约信息</h3>
            <!-- 销售签约 -->
            <a-row class="drawRow">
                <a-col :span="8">
                    <a-form-item label="物业类别">{{ detailForm.contract.propertyType }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="物业现状">{{ detailForm.contract.propertyStatus }}</a-form-item>
                </a-col>
                <a-col :span="8"></a-col>
                <a-col :span="8">
                    <a-form-item label="租赁合同">{{ $TOOL.dictTypeData('lease_agreement', detailForm.contract.leaseContract) != '无此字典项' ? $TOOL.dictTypeData('lease_agreement', detailForm.contract.leaseContract) :'-' }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="入园协议">{{ $TOOL.dictTypeData('entry_agreement', detailForm.contract.entryAgreement)  != '无此字典项' ? $TOOL.dictTypeData('entry_agreement', detailForm.contract.entryAgreement) : '-' }}</a-form-item>
                </a-col>
                <a-col :span="8"></a-col>
                <a-col :span="8">
                    <a-form-item label="租赁价格">{{ detailForm.contract.leasePrice }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="连廊租金">{{ detailForm.contract.corridorRent }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="月租金">{{ detailForm.contract.monthlyRent }}</a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="租赁保证金">{{ detailForm.contract.leaseDeposit }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="租赁起始日期">
                        {{ detailForm.contract.leaseStartTime }}
                    </a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="租赁截止日期">{{ detailForm.contract.leaseEndTime }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="租赁计算日期">{{ detailForm.contract.leaseCalculationDate }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="租期">{{ detailForm.contract.leaseTerm }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="免租期">{{ detailForm.contract.rentFreePeriod }}</a-form-item>
                </a-col>
                <a-col :span="8"></a-col>
                <a-col :span="8">
                    <a-form-item label="应缴租金">{{ detailForm.contract.rentPayable }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="已缴租金">{{ detailForm.contract.rentPaid }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="欠缴租金">{{ detailForm.contract.rentArrears }}</a-form-item>
                </a-col>
               
                <a-col :span="24">
                    <a-form-item label="备注">{{ detailForm.contract.remark }}</a-form-item>
                </a-col>
            </a-row>


            <h3 class="drawTitle">其他信息</h3>
            <a-row class="drawRow">
                <a-col :span="24">
                    <a-form-item label="企业优惠政策">{{ detailForm.other.companyPolicy }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="经营产品">{{ detailForm.other.businessProduct }}</a-form-item>
                </a-col>
                
                <a-col :span="8">
                    <a-form-item label="投资额">{{ detailForm.other.investmentAmount }}</a-form-item>
                </a-col>
                <a-col :span="8"></a-col>
                <a-col :span="8">
                    <a-form-item label="员工人数">{{ detailForm.other.staffNumber }}</a-form-item>
                </a-col>
                <a-col :span="8">
                    <a-form-item label="年营业额">{{ detailForm.other.annualTurnover }}</a-form-item>
                </a-col>
                <a-col :span="8"></a-col>
                <a-col :span="24">
                    <a-form-item label="配套需求">{{ detailForm.other.supportingRequirements }}</a-form-item>
                </a-col>
                <a-col :span="24">
                    <a-form-item label="存在问题">{{ detailForm.other.existingProblems }}</a-form-item>
                </a-col>
            </a-row>
        </a-from>
        <!-- </a-drawer> -->
    </xn-form-container>
</template>

<script name="Detail" setup>
import factoryLedgerApi from '@/api/biz/factoryLedgerApi'
const detailForm = ref({
    ledgerInfo: {},
    customerInfo: {
        personInfo: {},
        quotaList: []
    },
    contractInfo: {},
})
// 抽屉状态
const visible = ref(false)



// 打开抽屉
const onOpen = (record) => {
    factoryLedgerApi.facLedgerInfo({ id: record.id ,houseType:record.houseType}).then(res => {
        detailForm.value = { ...detailForm.value, ...res }
    })
    factoryLedgerApi.facLedgerInfoDetail({ id: record.id,houseType:"workshop" }).then(res => {
        console.log(res,"详情");
    })
    visible.value = true
}
// 关闭抽屉
const onClose = () => {
    visible.value = false
    // formRef.value.resetFields()
    detailForm.value = {}
    rentAndSaleInfo.value = {}
}

// 抛出函数
defineExpose({
    onOpen
})
</script>

<style lang="scss" scoped>
.drawTitle {
    font-weight: 900;
    font-size: 18px;
}

.drawRow {
    padding-left: 50px;
}
</style>