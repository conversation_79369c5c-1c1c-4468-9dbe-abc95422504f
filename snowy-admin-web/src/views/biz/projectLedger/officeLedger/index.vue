<template>
	<a-card :bordered="false">
		<s-table ref="table" :columns="columns" :data="loadData" :alert="options.alert.show" bordered
			:row-key="(record) => record.id" :row-selection="options.rowSelection">
			<template #operator class="table-operator">
				<a-row>
					<a-col :span="20">
						<a-form labelAlign="right" :model="searchFormState" ref="searchFormRef">
							<a-row :gutter="12">
								<a-col>
									<a-form-item label="客户姓名" name="name">
										<a-input autocomplete="off" v-model:value="searchFormState.name"
											placeholder="请输入客户姓名" style="width: 180px" allow-clear />
									</a-form-item>
								</a-col>
								<a-col>
									<a-form-item label="客户电话" name="phone">
										<a-input autocomplete="off" v-model:value="searchFormState.phone"
											placeholder="请输入客户电话" style="width: 180px" allow-clear />
									</a-form-item>
								</a-col>
								<a-col>
									<a-form-item label="身份证号" name="idCard">
										<a-input autocomplete="off" v-model:value="searchFormState.idCard"
											placeholder="请输入身份证号" style="width: 180px" allow-clear />
									</a-form-item>
								</a-col>

								<a-col>
									<a-form-item label="楼号">
										<a-input autocomplete="off" v-model:value="searchFormState.buildCode"
											placeholder="请输入身份证号" style="width: 180px" allow-clear />
									</a-form-item>
								</a-col>

								<a-col>
									<a-form-item label="楼层">
										<a-input autocomplete="off" v-model:value="searchFormState.floor"
											placeholder="请输入身份证号" style="width: 180px" allow-clear />
									</a-form-item>
								</a-col>
							</a-row>
							<a-row v-if="isOpen">
								<a-col>
									<a-form-item label="房屋房号">
										<a-input autocomplete="off" v-model:value="searchFormState.houseNumber"
											placeholder="请输入身份证号" style="width: 180px" allow-clear />
									</a-form-item>
								</a-col>
								<a-col style="margin-left: 10px">
									<a-form-item label="签约类型" name="contractType">
										<a-select v-model:value="searchFormState.contractType" placeholder="请选择"
											style="width: 180px" :options="contractDic"></a-select>
									</a-form-item>
								</a-col>
								<a-col style="margin-left: 10px">
									<a-form-item label="签约日期" name="contractTime">
										<a-date-picker v-model:value="value1" :format="YYYY / MM / DD"
											@change="sureTime" style="width: 180px" />
									</a-form-item>
								</a-col>
							</a-row>
							<a-row>
								<a-col>
									<a-row :gutter="12">
										<a-col>
											<a-button type="primary" @click="table.refresh(true)">
												<icon-font style="margin-right: 1px">
													<SearchOutlined />
												</icon-font>
												搜索</a-button>
										</a-col>
										<a-col>
											<a-button @click="onReset">
												<icon-font style="margin-right: 1px">
													<SearchOutlined />
												</icon-font>
												重置</a-button>
										</a-col>
										<a-col>
											<a-button style="background-color: #ffffff; color: black" @click="exportLedger"
												v-if="hasPerm('officeLedgerExport')">
												<icon-font style="margin-right: 1px">
													<CloudUploadOutlined />
												</icon-font>
												导出</a-button>
										</a-col>
										<a-col>
											<a-button danger @click="deleteBatchReLedgerInfo()">
												<icon-font style="margin-right: 1px">
													<redo-outlined />
												</icon-font>
												删除
											</a-button>
											<span style="color: rgb(22, 168, 255); margin-left: 20px"
												@click="openOrClose">{{
													isOpen ? '收起' : '更多'
												}}</span>
										</a-col>
									</a-row>
								</a-col>
							</a-row>
						</a-form>
					</a-col>
				</a-row>
				<a-row>
					<a-form>
						<a-row> </a-row>
					</a-form>
				</a-row>
			</template>
			<template #bodyCell="{ column, text, record, index }">
				<span v-if="column.dataIndex === 'serialNumber'">{{ index + 1 }}</span>
				<template v-if="column.dataIndex === 'action'">
					<a-space>
						<a-button @click="
							formRef.onOpen({
								...record,
								buildId: record.id,
								code: record.code,
								houseType: 'workshop',
								type: '编辑'
							})
							" v-if="hasPerm('officeLedgerEdit')" type="link" size="small">编辑</a-button>
						<a-button v-if="hasPerm('officeLedgerDetail')"
							@click="detailRef.onOpen({ ...record, buildId: record.id, code: record.code, houseType: 'workshop' })"
							type="link" size="small">详情</a-button>
						<a-popconfirm title="确定要删除吗？" @confirm="deleteReLedgerInfo(record)">
							<a-button type="link" danger size="small" v-if="hasPerm('officeLedgerDelete')">删除</a-button>
						</a-popconfirm>
					</a-space>
				</template>
				<template v-if="column.dataIndex === 'contractType'">
					<div>
						{{ $TOOL.dictTypeData('contract_type', record.contractType) }}
					</div>
				</template>
				<template v-if="column.dataIndex === 'status'">
					<div>
						{{ $TOOL.dictTypeData('house_status', record.status) }}
					</div>
				</template>
			</template>
		</s-table>
	</a-card>
	<Form ref="formRef" @successful="table.refresh(true)" />
	<Detail ref="detailRef" @successful="table.refresh(true)"></Detail>
</template>

<script setup name="OfficeLedger">
import reGardenledGerinfo from '@/api/biz/reGardenledGerinfo'
import { message } from 'ant-design-vue'
import Form from '../factoryLedger/form.vue'
import Detail from '../factoryLedger/detail.vue'
import factoryLedgerApi from '@/api/biz/factoryLedgerApi'
import  exportLedgerApi  from '@/api/biz/proParkExportApi'
import downloadUtil from '@/utils/downloadUtil'
import { useRoute, useRouter } from 'vue-router'
const route = useRoute()
const router = useRouter()
const table = ref()
const formRef = ref()
const detailRef = ref()
import tool from '@/utils/tool'
const contractDic = tool.dictList('contract_type')
const houseStatsu = tool.dictList('house_status')
const houseType = tool.dictList('house_type')
const searchFormState = ref({})
const searchFormRef = ref()
import { useMenuStore } from '@/store/menu'
const menuStore = useMenuStore()
const projectId = ref('')
projectId.value = menuStore.projectObj.id
const value1 = ref('')

const isOpen = ref(false)

//搜表参数
const formData = reactive({
	name: '',
	idCard: '',
	houseType: '',
	contractType: '',
	status: '',
	villageId: '',
	phone: '',
	contractTime: ''
})
// const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }
const columns = [
	{
		title: '序号',
		dataIndex: 'serialNumber', // 这个dataIndex仅作为标识，不对应实际数据字段
		width: '80px', // 可以自定义宽度
		scopedSlots: { customRender: 'bodyCell' }, // 对应模板中的具名插槽
		ellipsis: true,
		align: 'center'
	},
	{
		title: '入住企业',
		dataIndex: 'enterprise',
		ellipsis: true,
		align: 'center'
	},
	{
		title: '企业联络人',
		dataIndex: 'name',
		ellipsis: true,
		align: 'center'
	},
	{
		title: '身份证号',
		align: 'center',
		ellipsis: true,
		dataIndex: 'idCard',
		width: 180
	},
	{
		title: '联系电话',
		align: 'center',
		ellipsis: true,
		dataIndex: 'phone',
		width: 130
	},
	{
		title: '楼号',
		align: 'center',
		ellipsis: true,
		dataIndex: 'buildCode',
		width: 60
	},
	{
		title: '房号',
		align: 'center',
		ellipsis: true,
		dataIndex: 'houseNumber',
		width: 60
	},
	{
		title: '面积(㎡)',
		align: 'center',
		ellipsis: true,
		dataIndex: 'area'
	},
	{
		title: '签约类型',
		align: 'center',
		ellipsis: true,
		dataIndex: 'contractType'
	},
	// {
	//     title: '结清状态',
	//     align: 'center',
	//     ellipsis: true,
	//     dataIndex: 'status'
	// },
	{
		title: '签约日期',
		align: 'center',
		ellipsis: true,
		dataIndex: 'contractTime'
	},
	{
		title: '更新日期',
		align: 'center',
		ellipsis: true,
		dataIndex: 'updateTime'
	}
]
const sureTime = (date) => {
	searchFormState.value.contractTime =
		date.$y + '-' + (date.$M > 10 ? date.$M : '0' + (date.$M + 1)) + '-' + (date.$D > 10 ? date.$D : '0' + date.$D)
}
// 操作栏通过权限判断是否显示
if (hasPerm(['reLedgerInfoEdit', 'reLedgerInfoDelete'])) {
	columns.push({
		title: '操作',
		dataIndex: 'action',
		align: 'center',
		width: '150px'
	})
}
let selectedRowKeys = ref([])
// 列表选择配置
const options = {
	alert: {
		show: false,
		clear: () => {
			selectedRowKeys = ref([])
		}
	},
	rowSelection: {
		onChange: (selectedRowKey, selectedRows) => {
			selectedRowKeys.value = selectedRowKey
		}
	}
}
//台账导出
const exportLedger = () => {
	exportLedgerApi.exportLedger({ ProjectId: projectId.value, houseType: 'officebuilding' }).then(res => {
		downloadUtil.resultDownload(res)
	})
}

// 收起展开回调
const openOrClose = () => {
	isOpen.value = !isOpen.value
}

// 搜索按钮
const handleSearch = () => { }
// table表格数据
const loadData = (parameter) => {
	return factoryLedgerApi
		.facLedgerInfoPage({
			...parameter,
			houseType: 'officebuilding',
			...searchFormState.value,
			projectId: projectId.value
		})
		.then((data) => {
			data.records.forEach((item, index) => {
				if (item.isHistory) {
					data.records.splice(index, 1)
				}
			})
			return data
		})
}
// 删除
const deleteReLedgerInfo = (record) => {
	let params = [
		{
			id: record.id
		}
	]
	reGardenledGerinfo.reGardenHouseDelete(params).then(() => {
		table.value.refresh(true)
	})
}
// 批量删除
const deleteBatchReLedgerInfo = () => {
	if (selectedRowKeys.value.length < 1) {
		message.warning('请选择一条或多条数据')
		return false
	}
	const params = selectedRowKeys.value.map((m) => {
		return {
			id: m
		}
	})
	reGardenledGerinfo.reGardenHouseDelete(params).then(() => {
		table.value.clearRefreshSelected()
	})
}
//重置
const onReset = () => {
	searchFormState.value = {}
	searchFormRef.value.resetFields()
	table.value.refresh(true)
}
onMounted(() => {
	if (route.query.phone || route.query.name) {
		searchFormState.value.phone = route.query.phone
		searchFormState.value.name = route.query.name
		setTimeout(() => {
			table.value.refresh(true)
		}, 50)
	}
})
</script>
<style lang="scss" scoped>
.drawTitle {
	font-weight: 900;
	font-size: 18px;
}

.drawRow {
	padding-left: 50px;
}
</style>
