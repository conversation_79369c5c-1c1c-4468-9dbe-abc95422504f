<template>
	<a-card :bordered="false">
		<s-table
			ref="table"
			:columns="columns"
			:data="loadData"
			:alert="options.alert.show"
			bordered
			:row-key="(record) => record.id"
			:row-selection="options.rowSelection"
			class="custom-table"
		>
			<template #operator class="table-operator">
				<a-space>
					<a-row>
						<a-col>
							<a-form :labelAlign="right" :model="searchFormState" ref="searchFormRef">
								<a-row :gutter="12">
									<a-col>
										<a-form-item label="客户姓名" name="name">
											<a-input
												autocomplete="off"
												v-model:value="searchFormState.name"
												placeholder="请输入客户姓名"
												allow-clear
											/>
										</a-form-item>
									</a-col>
									<a-col>
										<a-form-item label="客户电话" name="phone">
											<a-input
												autocomplete="off"
												v-model:value="searchFormState.phone"
												placeholder="请输入客户电话"
												allow-clear
											/>
										</a-form-item>
									</a-col>
									<a-col>
										<a-form-item label="身份证号" name="idCard">
											<a-input
												autocomplete="off"
												v-model:value="searchFormState.idCard"
												placeholder="请输入身份证号"
												allow-clear
											/>
										</a-form-item>
									</a-col>

									<a-col>
										<a-form-item label="房屋楼号" name="buildCode">
											<!-- <a-input autocomplete="off" v-model:value="searchFormState.buildCode"
												placeholder="请输入楼号" allow-clear /> -->
											<a-select
												v-model:value="buildCodes"
												mode="multiple"
												placeholder="请选择楼号"
												style="width: 205px"
												@change="changeSect"
											>
												<a-select-option v-for="item in budingList" :value="item.code">{{ item.code }}</a-select-option>
											</a-select>
										</a-form-item>
									</a-col>
									<a-col>
										<a-form-item label="房屋楼层" name="floor">
											<a-input
												autocomplete="off"
												v-model:value="searchFormState.floor"
												placeholder="请输入楼层"
												allow-clear
											/>
										</a-form-item>
									</a-col>
								</a-row>
								<a-row v-if="isOpen">
									<a-col>
										<a-form-item label="房屋房号" name="houseNumber">
											<!-- <a-cascader v-model:value="value" :options="options" placeholder="请选择 select" /> -->
											<a-input
												autocomplete="off"
												v-model:value="searchFormState.houseNumber"
												placeholder="请输入房屋号"
												allow-clear
											/>
										</a-form-item>
									</a-col>

									<a-col style="margin-left: 10px">
										<a-form-item label="结清状态" name="status">
											<a-select
												v-model:value="searchFormState.status"
												:size="size"
												placeholder="请选择"
												style="width: 205px"
												:options="houseStatsu"
											></a-select>
										</a-form-item>
									</a-col>

									<a-col style="margin-left: 10px">
										<a-form-item label="认购时间" name="subscribeTime">
											<a-range-picker v-model:value="value1" @change="sureTime" />
										</a-form-item>
									</a-col>
								</a-row>
								<a-row>
									<a-col>
										<a-row :gutter="12">
											<a-col>
												<a-button type="primary" @click="table.refresh(true)">
													<icon-font style="margin-right: 1px">
														<SearchOutlined />
													</icon-font>
													搜索</a-button
												>
											</a-col>
											<a-col>
												<a-button style="background-color: #ffffff; color: black" @click="onReset">
													<icon-font style="margin-right: 1px">
														<SearchOutlined />
													</icon-font>
													重置</a-button
												>
											</a-col>
											<a-col>
												<a-button style="float: right" class="xn-mg08" @click="ImpExpRef.onOpen()">批量导入</a-button>
											</a-col>
											<a-col>
												<a-button
													style="background-color: #ffffff; color: black"
													v-if="hasPerm('reStorageLedgerinfoExport')"
													@click="exportLedger"
												>
													<icon-font style="margin-right: 1px">
														<CloudUploadOutlined />
													</icon-font>
													导出</a-button
												>
											</a-col>
											<a-col>
												<a-button danger @click="deleteBatchReLedgerInfo()" v-if="hasPerm('reStorageLedgerinfoDelete')">
													<icon-font style="margin-right: 1px">
														<DeleteOutlined />
													</icon-font>
													删除
												</a-button>
												<span style="color: rgb(22, 168, 255); margin-left: 15px" @click="openOrClose">{{
													isOpen ? '收起' : '更多'
												}}</span>
											</a-col>
										</a-row>
									</a-col>
								</a-row>
							</a-form>
						</a-col>
					</a-row>
				</a-space>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'action'">
					<a-space>
						<a-button
							@click="
								formRef.onOpen(
									{
										...record,
										id: record.houseId,
										code: record.buildCode,
										actualBuildArea: record.area,
										totalPrice: record.totalPrice
									},
									2
								)
							"
							type="link"
							size="small"
							v-if="hasPerm('reStorageLedgerinfoEdit')"
							>编辑</a-button
						>
						<a-button
							@click="detailRef.onOpen({ ...record }, 2)"
							type="link"
							size="small"
							v-if="hasPerm('reStorageLedgerinfoDetail')"
							>详情</a-button
						>
						<!-- <a-divider type="vertical" v-if="hasPerm(['reLedgerInfoEdit', 'reLedgerInfoDelete'], 'and')" /> -->
						<a-popconfirm title="确定要删除吗？" @confirm="deleteReLedgerInfo(record)">
							<a-button type="link" danger size="small" v-if="hasPerm('reStorageLedgerinfoDelete')">删除</a-button>
						</a-popconfirm>
					</a-space>
				</template>
				<template v-if="column.dataIndex === 'status'">
					<div>
						{{ $TOOL.dictTypeData('house_status', record.status) }}
					</div>
				</template>
				<template v-if="column.dataIndex === 'contractType'">
					<div>
						{{ $TOOL.dictTypeData('contract_type', record.contractType) }}
					</div>
				</template>
				<template v-if="column.dataIndex === 'debt'">
					<div>
						{{ calculateDebt(record) }}
					</div>
				</template>
			</template>
		</s-table>
	</a-card>
	<Form ref="formRef" @successful="table.refresh(true)" />
	<Detail ref="detailRef" @successful="table.refresh(true)"></Detail>
	<ImpExp ref="ImpExpRef" @successful="refreshList" />
</template>

<script setup name="reledgerinfo">
	import reBuildingApi from '@/api/biz/reBuildingApi'
	import { useMenuStore } from '@/store/menu'
	import { message } from 'ant-design-vue'
	import Form from '../bill/storageRoomBill/form.vue'
	import Detail from './detail.vue'
	import ImpExp from './ImpExp.vue'

	import reLedgerInfoApi from '@/api/biz/reLedgerInfoApi'
	import exportHoemLedgerApi from '@/api/biz/exportHoemLedgerApi'
	import downloadUtil from '@/utils/downloadUtil'
	const table = ref()
	const formRef = ref()
	const detailRef = ref()
	const detailForm = ref({})
	const searchFormState = ref({})
	const searchFormRef = ref()
	import tool from '@/utils/tool'
	const contractDic = tool.dictList('contract_type')
	const houseStatsu = tool.dictList('pay-type')
	const houseType = tool.dictList('house_type')
	import { useRoute, useRouter } from 'vue-router'
	const route = useRoute()
	const router = useRouter()
	const isOpen = ref(false)
	// 对话框显示与隐藏
	const open = ref(false)
	//获取项目ID
	const menuStore = useMenuStore()
	const projectId = ref('')
	const value1 = ref('')
	projectId.value = menuStore.projectObj.id
	const selectValue = ref([])
	const budingList = ref([])
	const buildCodes = ref([])

	const ImpExpRef = ref()

	//搜表参数
	const formData = reactive({
		name: '',
		idCard: '',
		houseType: '',
		contractType: '',
		status: '',
		villageId: '',
		phone: '',
		subscribeTime: ''
	})
	// const toolConfig = { refresh: true, height: true, columnSetting: true, striped: false }

	const columns = [
		{
			title: '客户姓名',
			dataIndex: 'name',
			ellipsis: true,
			align: 'center'
		},
		{
			title: '身份证号',
			align: 'center',
			ellipsis: true,
			dataIndex: 'idCard',
			width: 180
		},
		// {
		// 	title: '村落编号',
		// 	dataIndex: 'villageId',
		// 	ellipsis: true,
		// 	align: 'center'
		// },

		{
			title: '客户电话',
			align: 'center',
			ellipsis: true,
			dataIndex: 'phone',
			width: 130
		},
		{
			title: '楼号',
			align: 'center',
			ellipsis: true,
			dataIndex: 'buildCode',
			width: 60
		},
		{
			title: '楼层',
			align: 'center',
			ellipsis: true,
			dataIndex: 'floor',
			width: 60
		},
		// {
		//     title: '单元',
		//     align: 'center',
		//     ellipsis: true,
		//     dataIndex: 'unit'
		// },
		{
			title: '房号',
			align: 'center',
			ellipsis: true,
			dataIndex: 'houseNumber',
			width: 60
		},
		{
			title: '面积(㎡)',
			align: 'center',
			ellipsis: true,
			dataIndex: 'area'
		},
		{
			title: '签约类型',
			align: 'center',
			ellipsis: true,
			dataIndex: 'contractType'
		},
		// {
		//     title: '成交单价',
		//     align: 'center',
		//     ellipsis: true,
		//     dataIndex: 'dealUnitPrice'
		// },
		{
			title: '签约总价(元)',
			align: 'center',
			ellipsis: true,
			dataIndex: 'contractPrice'
		},
		{
			title: '认购时间',
			align: 'center',
			ellipsis: true,
			dataIndex: 'subscribeTime'
		},

		{
			title: '签约总价(元)',
			align: 'center',
			ellipsis: true,
			dataIndex: 'contractPrice'
		},
		{
			title: '合计交款(元)',
			align: 'center',
			ellipsis: true,
			dataIndex: 'totalPayment'
		},
		{
			title: '欠款金额(元)',
			align: 'center',
			ellipsis: true,
			dataIndex: 'debt'
		},

		// {
		//     title: '房屋id',
		//     align: 'center',
		//     ellipsis: true,
		//     dataIndex: 'houseId'
		// },
		// {
		// 	title: '是否历史',
		// 	align: 'center',
		// 	ellipsis: true,
		// 	dataIndex: 'isHistory',
		// 	width: 60
		// },
		// {
		//     title: '项目id',
		//     align: 'center',
		//     ellipsis: true,
		//     dataIndex: 'projectId'
		// },
		// {
		//     title: '房屋类型--字典（住宅、商业、储藏间）',
		//     align: 'center',
		//     ellipsis: true,
		//     dataIndex: 'houseType'
		// },
		{
			title: '房屋状态',
			align: 'center',
			ellipsis: true,
			dataIndex: 'status'
		}
		// {
		// 	title: '更新时间',
		// 	align: 'center',
		// 	ellipsis: true,
		// 	dataIndex: 'extJson'
		// }
	]
	// 操作栏通过权限判断是否显示
	if (hasPerm(['reLedgerInfoEdit', 'reLedgerInfoDelete'])) {
		columns.push({
			title: '操作',
			dataIndex: 'action',
			align: 'center',
			width: '150px'
		})
	}
	let selectedRowKeys = ref([])
	const changeSect = (value) => {
		selectValue.value = value
	}
	// 列表选择配置
	const options = {
		alert: {
			show: false,
			clear: () => {
				selectedRowKeys = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}
	const sureTime = (date) => {
		searchFormState.value.subscribeTime = []
		searchFormState.value.subscribeTime[0] =
			date[0].$y +
			'-' +
			(date[0].$M >= 10 ? date[0].$M + 1 : '0' + (date[0].$M + 1)) +
			'-' +
			(date[0].$D > 10 ? date[0].$D : '0' + date[0].$D) +
			' 00:00:00'
		searchFormState.value.subscribeTime[1] =
			date[1].$y +
			'-' +
			(date[1].$M >= 10 ? date[1].$M + 1 : '0' + (date[1].$M + 1)) +
			'-' +
			(date[1].$D > 10 ? date[1].$D : '0' + date[1].$D) +
			' 23:59:59'
		searchFormState.value.subscribeTime = searchFormState.value.subscribeTime.join(',')
	}
	// 收起展开回调
	const openOrClose = () => {
		isOpen.value = !isOpen.value
	}

	const exportLedger = () => {
		exportHoemLedgerApi.exportStoreRoomLedger({ projectId: projectId.value }).then((res) => {
			downloadUtil.resultDownload(res)
		})
	}

	//刷新列表
	const refreshList = (queryParams) => {
		loadData({
			houseType: 3,
			current: 1,
			size: 10
		}) //  houseType 住宅1、商业2、储藏间3
		setTimeout(() => {
			table.value.refresh(true)
		}, 500);

	}

	// 搜索按钮
	const handleSearch = () => {}
	// table表格数据
	const loadData = (parameter) => {
		searchFormState.value.isHistory = false
		searchFormState.value.buildCode = selectValue.value.length > 0 ? selectValue.value.join(',') : ''
		return reLedgerInfoApi
			.reLedgerInfoPage({ ...parameter, houseType: '3', ...searchFormState.value, projectId: projectId.value })
			.then((data) => {
				// 🔧 删除前端二次过滤逻辑，信任后端isHistory=false过滤结果
				// 这样可以确保分页准确性和性能
				searchFormState.value.buildCode =
					searchFormState.value.buildCode?.split(',').length > 0 ? searchFormState.value.buildCode?.split(',') : []

				return data
			})
	}
	// 删除
	const deleteReLedgerInfo = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		reLedgerInfoApi.reLedgerInfoDelete(params).then(() => {
			table.value.refresh(true)
		})
	}
	// 批量删除
	const deleteBatchReLedgerInfo = () => {
		if (selectedRowKeys.value.length < 1) {
			message.warning('请选择一条或多条数据')
			return false
		}
		const params = selectedRowKeys.value.map((m) => {
			return {
				id: m
			}
		})
		reLedgerInfoApi.reLedgerInfoDelete(params).then(() => {
			table.value.clearRefreshSelected()
		})
	}
	// 详情页面
	const handleDetail = (record) => {
		open.value = true
		reLedgerInfoApi.reLedgerInfoDetail({ id: record.id }).then((res) => {
			detailForm.value = res
		})
	}
	//重置
	const onReset = () => {
		buildCodes.value = []
		selectValue.value = {}
		searchFormState.value = {}
		searchFormRef.value.resetFields()
		value1.value = ''
		table.value.refresh(true)
	}

	// 🔧 修复：优先使用后端返回的欠款金额，与详情页面保持一致
	const calculateDebt = (record) => {
		// 优先使用后端返回的欠款金额
		if (record.debt !== undefined && record.debt !== null) {
			return Number(record.debt).toFixed(2);
		}

		// 备用计算逻辑（当后端没有返回debt字段时）
		// 注意：储藏间台账使用contractPrice字段
		const contractPrice = Number(record.contractPrice) || 0;
		const totalPayment = Number(record.totalPayment) || 0;

		const debt = contractPrice - totalPayment;
		return debt >= 0 ? debt.toFixed(2) : '--';
	}

	//获取楼栋列表
	const getBuildingList = () => {
		reBuildingApi.reBuildingPage({ projectId: projectId.value }).then((res) => {
			console.log(res, '[[pores]]')
			budingList.value = res.records
		})
	}

	onMounted(() => {
		getBuildingList()
		if (route.query.phone || route.query.name) {
			searchFormState.value.phone = route.query.phone
			searchFormState.value.name = route.query.name
			setTimeout(() => {
				table.value.refresh(true)
			}, 50)
		}
	})
</script>
<style lang="scss" scoped>
	.drawTitle {
		font-weight: 900;
		font-size: 18px;
	}

	.drawRow {
		padding-left: 50px;
	}
	.custom-table {
		:deep(.ant-table-tbody > tr:hover > td) {
			background-color:#E6F4FF !important;
		}
	}
</style>
