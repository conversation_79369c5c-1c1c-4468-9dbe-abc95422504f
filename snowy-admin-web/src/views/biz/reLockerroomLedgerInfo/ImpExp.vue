<template>
	<xn-form-container title="导入" :width="700" :visible="visible" :destroy-on-close="true" @close="onClose">
		<span
			>导入数据格式严格按照系统模板进行数据录入，请点击
			<a-button type="primary" size="small" @click="downloadImportUserTemplate">下载模板</a-button>
		</span>
		<a-divider dashed />
		<div>
			<a-spin :spinning="impUploadLoading">
				<a-upload-dragger :show-upload-list="false" :custom-request="customRequestLocal" :accept="uploadAccept">
					<p class="ant-upload-drag-icon">
						<inbox-outlined></inbox-outlined>
					</p>
					<p class="ant-upload-text">单击或拖动文件到此区域进行上传</p>
					<p class="ant-upload-hint">仅支持xls、xlsx格式文件</p>
				</a-upload-dragger>
			</a-spin>
		</div>
		<a-alert v-if="impAlertStatus" type="info" :show-icon="false" banner closable @close="onImpClose" class="mt-3">
			<template #description>
				<p>导入总数：{{ impResultData.totalCount }} 条</p>
				<p>导入成功：{{ impResultData.successCount }} 条</p>
				<div v-if="impResultData.errorCount > 0">
					<p><span class="xn-color-red">失败条数：</span>{{ impResultData.errorCount }} 条</p>
					<a-table :dataSource="impResultErrorDataSource" :columns="impErrorColumns" size="small" />
				</div>
			</template>
		</a-alert>
	</xn-form-container>
</template>

<script setup name="userImpExp">
	import { cloneDeep } from 'lodash-es'
	import { message } from 'ant-design-vue'
	import reHouseApi from '@/api/biz/reLedgerInfoApi'
	import reHouseApi2 from '@/api/biz/reHouseApi'
	import downloadUtil from '@/utils/downloadUtil'
	import { useMenuStore } from '@/store/menu'
	const menuStore = useMenuStore()

	// 定义emit事件
	const emit = defineEmits({ successful: null })

	const formData = ref({})
	const impUploadLoading = ref(false)
	const impAlertStatus = ref(false)
	const impResultData = ref({})
	const impResultErrorDataSource = ref([])
	const impAccept = [
		{
			extension: '.xls',
			mimeType: 'application/vnd.ms-excel'
		},
		{
			extension: '.xlsx',
			mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
		}
	]
	// 指定能选择的文件类型
	const uploadAccept = String(
		impAccept.map((item) => {
			return item.mimeType
		})
	)
	// 导入
	const customRequestLocal = (data) => {
		impUploadLoading.value = true
		const fileData = new FormData()
		// 校验上传文件扩展名和文件类型是否为.xls、.xlsx
		const extension = '.'.concat(data.file.name.split('.').slice(-1).toString().toLowerCase())
		const mimeType = data.file.type
		// 提取允许的扩展名
		const extensionArr = impAccept.map((item) => item.extension)
		// 提取允许的MIMEType
		const mimeTypeArr = impAccept.map((item) => item.mimeType)
		if (!extensionArr.includes(extension) || !mimeTypeArr.includes(mimeType)) {
			message.warning('上传文件类型仅支持xls、xlsx格式文件！')
			impUploadLoading.value = false
			return false
		}
		fileData.append('file', data.file)
		fileData.append('projectId', menuStore.projectObj.id)
		fileData.append('buildId', formData.value.buildId)
		fileData.append('houseType', formData.value.houseType)
		return reHouseApi
			.importStoreroom(fileData, { timeout: 1800000 }) // 30分钟超时
			.then((res) => {
				// 解析导入结果
				if (res.code === 200) {
					// 全部成功
					const totalCount = res.total || 0
					impResultData.value = {
						totalCount: totalCount,
						successCount: totalCount,
						errorCount: 0
					}
					impAlertStatus.value = true
					message.success(res.msg)

					// 成功时延迟2秒后自动关闭
					setTimeout(() => {
						onClose()
						emit('successful')
					}, 2000)

				} else if (res.code === 500 && res.errorList && res.errorList.length > 0) {
					// 部分失败
					const totalCount = res.total || 0
					const errorCount = res.errorList.length
					const successCount = totalCount - errorCount
					impResultData.value = {
						totalCount: totalCount,
						errorCount: errorCount,
						successCount: successCount >= 0 ? successCount : 0
					}
					impResultErrorDataSource.value = res.errorList
					// 动态生成错误列表的列配置
					impErrorColumns.value = generateErrorColumns(res.errorList)
					impAlertStatus.value = true
					message.warning(res.msg)

					// 失败时不自动关闭，让用户查看详情
				} else {
					// 其他错误情况
					message.error(res.msg || '导入失败')
					onClose()
					emit('successful')
				}
			})
			.finally(() => {
				impUploadLoading.value = false
			})
	}
	// 关闭导入提示
	const onImpClose = () => {
		impAlertStatus.value = false
	}
	// 动态错误列表列定义
	const impErrorColumns = ref([])

	// 动态生成错误列表的列配置
	const generateErrorColumns = (errorList) => {
		if (!errorList || errorList.length === 0) {
			return []
		}

		const firstError = errorList[0]
		const columns = []

		// 定义字段的优先顺序和宽度配置
		const fieldConfig = {
			'楼号': { width: '80px', order: 1 },
			'楼层': { width: '80px', order: 2 },
			'房号': { width: '80px', order: 3 },
			'项目ID': { width: '100px', order: 4 },
			'房屋类型': { width: '100px', order: 5 },
			'错误信息': { width: null, order: 999 } // 最后一列，自适应宽度
		}

		// 获取所有存在的字段并按优先级排序
		const existingFields = Object.keys(firstError)
			.sort((a, b) => {
				const orderA = fieldConfig[a]?.order || 500
				const orderB = fieldConfig[b]?.order || 500
				return orderA - orderB
			})

		// 生成列配置
		existingFields.forEach(key => {
			const config = fieldConfig[key] || { width: '100px' }

			columns.push({
				title: key,
				dataIndex: key,
				width: config.width,
				align: key === '错误信息' ? 'left' : 'center',
				ellipsis: true
			})
		})

		return columns
	}
	// 默认是关闭状态
	const visible = ref(false)
	const submitLoading = ref(false)

	// 打开抽屉
	const onOpen = (record) => {
		visible.value = true
		let recordData = cloneDeep(record)
		formData.value = Object.assign({}, recordData)
	}
	// 关闭抽屉
	const onClose = () => {
		visible.value = false
		// 关闭导入的提示
		onImpClose()
		// 关闭时触发列表刷新
		emit('successful')
	}
	// 下载用户导入模板
	const downloadImportUserTemplate = () => {
		reHouseApi2.exportlockerroom(formData.value).then((res) => {
			downloadUtil.resultDownload(res)
		})
	}
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>
<style scoped>
	.xn-color-red {
		color: #ff0000;
	}
</style>
