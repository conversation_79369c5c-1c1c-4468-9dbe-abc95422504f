<template>
	<xn-form-container
		:title="formData.id ? '编辑项目管理' : '增加项目管理'"
		:width="900"
		:visible="visible"
		:destroy-on-close="true"
		@close="onClose"
	>
		<a-form ref="formRef" :model="formData" :rules="formRules" layout="vertical">
			<a-row :gutter="16">
				<a-col :span="12">
					<a-form-item label="项目名称" name="name">
						<a-input v-model:value="formData.name" placeholder="请输入项目名称" allow-clear />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="总占地面积" name="totalArea">
						<a-input placeholder="请输入总占地面积" v-model:value="formData.totalArea" suffix="亩" />
					</a-form-item>
				</a-col>

				<a-col :span="12">
					<a-form-item label="总建筑面积:" name="buildArea">
						<a-input placeholder="请输入总建筑面积" v-model:value="formData.buildArea" suffix="m²" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="项目类型:" name="type">
						<a-select placeholder="请选择项目类型" class="xn-wd" ref="select" v-model:value="formData.type">
							<a-select-option :value="item.value" v-for="item in typeOptions">{{ item.label }}</a-select-option>
						</a-select>
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="容积率:" name="plotRatio">
						<a-input placeholder="请输入容积率" v-model:value="formData.plotRatio" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="门牌大号:" name="houseNumber">
						<a-input placeholder="请输入门牌大号" v-model:value="formData.houseNumber" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="土地增值税编号:" name="landValueAddedTaxNum">
						<a-input placeholder="请输入土地增值税编号" v-model:value="formData.landValueAddedTaxNum" />
					</a-form-item>
				</a-col>
				<a-col :span="12">
					<a-form-item label="项目类别：" name="category">
						<a-radio-group v-model:value="formData.category" button-style="solid">
							<a-radio-button :value="item.id" v-for="item in modules" :key="item.id">{{ item.title }}</a-radio-button>
						</a-radio-group>
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="项目定位:">
						<div style="height: 400px">
							<gaode-map
								v-if="visible"
								style="height: 100% !important"
								ref="map"
								api-key="034c7fb6acbb91973d7f253c9e8dae4e"
								@map-click="mapclick"
								:point="point"
							/>
						</div>
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="">
						<div style="display: flex; align-items: center">
							<a-input placeholder="请选择经度" v-model:value="formData.lng" /> <span>&nbsp; &nbsp;</span>
							<a-input placeholder="请选择维度" v-model:value="formData.lat" />
						</div>
					</a-form-item>
				</a-col>
				<a-col :span="24">
					<a-form-item label="项目俯瞰图:" name="overlook">
						<xn-upload v-model:value="formData.overlook" uploadMode="image" />
					</a-form-item>
				</a-col>
			</a-row>
		</a-form>
		<template #footer>
			<a-button class="xn-mr8" @click="onClose">关闭</a-button>
			<a-button type="primary" @click="onSubmit" :loading="submitLoading">保存</a-button>
		</template>
	</xn-form-container>
</template>

<script setup>
	import { cloneDeep } from 'lodash-es'
	import { nextTick } from 'vue'
	import { required } from '@/utils/formRules'
	import reProjectApi from '@/api/biz/reProjectApi'
	import GaodeMap from '@/components/Map/gaodeMap/index.vue'
	import moduleApi from '@/api/sys/resource/moduleApi'
	import tool from '@/utils/tool'

	// 默认是关闭状态
	const emit = defineEmits({ successful: null })
	const visible = ref(false)
	const formRef = ref()
	const treeData = ref([])
	const submitLoading = ref(false)
	const map = ref(null)
	const point = ref([])
	const modules = ref([])
	const categoryOptions = tool.dictList('PROJEXT_CLASS')
	const typeOptions = tool.dictList('PROJECT_TYPE')

	// 表单数据
	const formData = ref({
	})

	// 打开抽屉
	const onOpen = (record) => {
		visible.value = true
		formData.value = {
		}
		if (record) {
			if (record.lonlat) {
				let lonlat = record.lonlat.split(',')
				record.lng = lonlat[0]
				record.lat = lonlat[1]
				point.value = [lonlat[0], lonlat[1]]
				console.log(point.value, '-----')
			}
			formData.value = Object.assign({}, record)
		} else {
			formData.value = {
			}
		}
	}
	const loadModule = (parameter) => {
		moduleApi.modulePage2(parameter).then((res) => {
			res.records.splice(0,1)
			modules.value = res.records
		})
	}
	// 关闭抽屉
	const onClose = () => {
		formRef.value.resetFields()
		visible.value = false
	}

	// 默认要校验的
	const formRules = {
		name: [required('请输入项目名称')],
		category: [required('请选择项目类别')],
	}

	// 验证并提交数据
	const onSubmit = () => {
		formRef.value.validate().then(() => {
			submitLoading.value = true
			const formDataParam = cloneDeep(formData.value)
			reProjectApi
				.reProjectSubmitForm(formDataParam, formDataParam.id)
				.then(() => {
					onClose()
					emit('successful')
				})
				.finally(() => {
					submitLoading.value = false
				})
		})
	}

	//点击地图获取经纬度
	const mapclick = (lng, lat) => {
		formData.value.lng = lng
		formData.value.lat = lat
		formData.value.lonlat = lng + ',' + lat
	}
	onMounted(() => {
		loadModule()
	})
	// 调用这个函数将子组件的一些数据和方法暴露出去
	defineExpose({
		onOpen
	})
</script>
