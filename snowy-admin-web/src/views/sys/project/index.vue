<template>
	<a-card :bordered="false" :body-style="{ 'padding-bottom': '0px' }" class="mb-2">
		<a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
			<a-row :gutter="24">
				<a-col :span="8">
					<a-form-item label="项目名称" name="name">
						<a-input v-model:value="searchFormState.name" placeholder="请输入项目名称" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-button type="primary" @click="tableRef.refresh(true)">查询</a-button>
					<a-button class="xn-mg08" @click="reset">重置</a-button>
				</a-col>
			</a-row>
		</a-form>
	</a-card>
	<a-card :bordered="false">
		<s-table
			ref="tableRef"
			:columns="columns"
			:data="loadData"
			:alert="options.alert.show"
			bordered
			:row-key="(record) => record.id"
			:tool-config="toolConfig"
			:row-selection="options.rowSelection"
		>
			<template #operator class="table-operator">
				<a-space>
					<a-button type="primary" @click="formRef.onOpen()">
						<template #icon><plus-outlined /></template>
						新增项目
					</a-button>
					<xn-batch-delete :selectedRowKeys="selectedRowKeys" @batchDelete="deleteBatchReProject" />
				</a-space>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'name'">
					<div style="color: #1677ff; cursor: pointer" @click="buildLink(record)">
						{{ record.name }}
					</div>
				</template>
				<template v-if="column.dataIndex === 'type' && record.type">
					<a-tag color="cyan">
						{{ $TOOL.dictTypeData('PROJECT_TYPE', record.type) }}
					</a-tag>
				</template>
				<template v-if="column.dataIndex === 'category'">
					<a-tag color="cyan">
						{{ getClass(record.category) }}
					</a-tag>
				</template>
				<template v-if="column.dataIndex === 'action'">
					<a-space>
						<!-- <a @click="buildLink(record)" v-if="hasPerm('buildoff')">楼栋管理</a> -->
						<a @click="openRoleUserSelector(record)" v-if="hasPerm('userOptions')">人员配置</a>
						<a @click="formRef.onOpen(record)" v-if="hasPerm('reProjectEdit')">编辑</a>
						<a-popconfirm title="确定要删除此模块吗？" @confirm="deleteReProject(record)">
							<a-button type="link" danger size="small" v-if="hasPerm('reProjectDelete')">删除</a-button>
						</a-popconfirm>
					</a-space>
				</template>
			</template>
		</s-table>
	</a-card>
	<Form ref="formRef" @successful="tableRef.refresh(true)" />
	<xn-user-selector
		ref="userSelectorPlusRef"
		:org-tree-api="selectorApiFunction.orgTreeApi"
		:user-page-api="selectorApiFunction.userPageApi"
		data-type="object"
		:user-show="false"
		@onBack="userCallBack"
	/>
</template>

<script setup name="sysModule">
	import Form from './form.vue'
	import moduleApi from '@/api/sys/resource/moduleApi'
	import reProjectApi from '@/api/biz/reProjectApi'
	import roleApi from '@/api/sys/roleApi'
	import { useRoute, useRouter } from 'vue-router'
	const searchFormState = ref({})
	const recordCacheData = ref({})
	const modules = ref([])
	const route = useRoute()
	const router = useRouter()
	const formRef = ref()
	const searchFormRef = ref()
	const userSelectorPlusRef = ref()
	const tableRef = ref()
	const toolConfig = { refresh: true, height: true, columnSetting: false, striped: false }
	const columns = [
		{
			title: '项目名称',
			align: 'center',
			dataIndex: 'name'
		},
		{
			title: '项目类型',
			align: 'center',
			dataIndex: 'type'
		},
		{
			title: '项目类别',
			align: 'center',
			dataIndex: 'category'
		},
		{
			title: '门牌大号',
			dataIndex: 'houseNumber',
			align: 'center'
		},
		{
			title: '容积率',
			dataIndex: 'plotRatio',
			align: 'center'
		},
		{
			title: '创建时间',
			dataIndex: 'createTime',
			align: 'center'
		}
	]

	// 操作栏通过权限判断是否显示
	if (hasPerm(['reProjectEdit', 'reProjectDelete'])) {
		columns.push({
			title: '操作',
			dataIndex: 'action',
			align: 'center',
			width: '300px'
		})
	}
	let selectedRowKeys = ref([])
	onMounted(() => {
		loadModule()
	})
	// 列表选择配置
	const options = {
		alert: {
			show: false,
			clear: () => {
				selectedRowKeys = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}
	const loadModule = (parameter) => {
		moduleApi.modulePage2(parameter).then((res) => {
			res.records.splice(0, 1)
			modules.value = res.records
		})
	}

	const getClass = (id) => {
		let i = ''
		modules.value.forEach((el) => {
			if (el.id == id) {
				i = el.title
			}
		})
		return i
	}

	const loadData = (parameter) => {
		let param = Object.assign(parameter, searchFormState.value)
		return reProjectApi.reProjectPage(param).then((data) => {
			return data
		})
	}
	// 重置
	const reset = () => {
		searchFormRef.value.resetFields()
		tableRef.value.refresh(true)
	}
	// 删除
	const deleteReProject = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		reProjectApi.reProjectDelete(params).then(() => {
			tableRef.value.refresh(true)
		})
	}
	// 批量删除
	const deleteBatchReProject = () => {
		if (selectedRowKeys.value.length < 1) {
			message.warning('请选择一条或多条数据')
			return false
		}
		const params = selectedRowKeys.value.map((m) => {
			return {
				id: m
			}
		})
		reProjectApi.reProjectDelete(params).then(() => {
			tableRef.value.clearRefreshSelected()
		})
	}
	const buildLink = (record) => {
		router.push({
			path: '/sys/build',
			query: {
				id: record.id,
				category: record.category
			}
		})
	}
	// 打开用户选择器
	const openRoleUserSelector = (record) => {
		// 打开人员选择器的时候，缓存一个记录数据
		recordCacheData.value = record
		// 查询接口，查到这个角色是多少个用户都有它
		const param = {
			id: record.id
		}
		reProjectApi.reProjectDetail(param).then((data) => {
			let dataArr = []
			if (data.extJson) {
				dataArr = data.extJson.split(',')
			}
			userSelectorPlusRef.value.showUserPlusModal(dataArr)
		})
	}
	// 人员选择器回调
	const userCallBack = (value) => {
		const param = {
			...recordCacheData.value,
			extJson: value.toString()
		}
		reProjectApi.reProjectSubmitForm(param, recordCacheData.value.id).then(() => {})
	}
	// 传递设计器需要的API
	const selectorApiFunction = {
		orgTreeApi: (param) => {
			return roleApi.roleOrgTreeSelector(param).then((data) => {
				return Promise.resolve(data)
			})
		},
		userPageApi: (param) => {
			return roleApi.roleUserSelector(param).then((data) => {
				return Promise.resolve(data)
			})
		}
	}
</script>
