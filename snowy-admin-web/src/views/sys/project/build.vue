<template>
	<!-- <a-card :bordered="false" :body-style="{ 'padding-bottom': '0px' }" class="mb-2">
		<a-form ref="searchFormRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
			<a-row :gutter="24">
				<a-col :span="8">
					<a-form-item label="项目名称" name="searchKey">
						<a-input v-model:value="searchFormState.searchKey" placeholder="请输入项目名称" />
					</a-form-item>
				</a-col>
				<a-col :span="8">
					<a-button type="primary" @click="tableRef.refresh(true)">查询</a-button>
					<a-button class="xn-mg08" @click="reset">重置</a-button>
				</a-col>
			</a-row>
		</a-form>
	</a-card> -->
	<a-card :bordered="false">
		<s-table
			ref="tableRef"
			:columns="columns"
			:data="loadData"
			:expand-row-by-click="true"
			:alert="options.alert.show"
			bordered
			:row-key="(record) => record.id"
			:tool-config="toolConfig"
			:row-selection="options.rowSelection"
		>
			<template #operator class="table-operator">
				<a-space>
					<a-button v-if="hasPerm('rebuildAdd')" type="primary" @click="formRef.onOpen()">
						<template #icon><plus-outlined /></template>
						新增楼栋
					</a-button>
					<a-button type="primary" @click="selectPoint">
						<template #icon><plus-outlined /></template>
						楼栋定位
					</a-button>
					<xn-batch-delete
						v-if="hasPerm('rebuildDelete')"
						:selectedRowKeys="selectedRowKeys"
						@batchDelete="deleteBatchReBuilding"
					/>
				</a-space>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'code'">
					<a-popover title="查看销控" trigger="click">
						<template #content>
							<div class="billItem" v-if="modelType == '社区'" @click="projectHouseBill(record)">住宅销控</div>
							<div class="billItem" v-if="modelType == '社区' && record.type == 1" @click="projectStoreBill(record)">
								商业销控
							</div>
							<div class="billItem" v-if="modelType == '社区'" @click="projectStorageRoomBill(record)">储藏间销控</div>
							<div class="billItem" v-if="modelType == '园区'" @click="projectWorkshop(record)">厂房销控</div>
							<div class="billItem" v-if="modelType == '园区'" @click="projectOffice(record)">办公用房销控</div>
							<div class="billItem" v-if="modelType == '园区'" @click="projectDormitory(record)">宿舍销控</div>
						</template>
						<div style="color: #1677ff; cursor: pointer">
							{{ record.code }}
						</div>
					</a-popover>
				</template>
				<template v-if="column.dataIndex === 'type'">
					<a-tag color="cyan">
						{{ $TOOL.dictTypeData('SYS_YESNO', record.type) }}
					</a-tag>
				</template>
				<template v-if="column.dataIndex === 'action'">
					<a-space>
						<a @click="formRef.onOpen(record)" v-if="hasPerm('reBuildingEdit')">编辑</a>
						<a-popconfirm title="确定要删除此楼栋吗？" @confirm="deleteReBuilding(record)">
							<a-button type="link" danger size="small" v-if="hasPerm('rebuildDelete')">删除</a-button>
						</a-popconfirm>
					</a-space>
				</template>
			</template>
		</s-table>
	</a-card>
	<Form :id="projectId" :modelType="modelType" ref="formRef" @successful="tableRef.refresh(true)" />
	<positionModel
		:pointPosition="mapTable"
		:isTrue="isTrue"
		:num="num"
		:overlook="overlook"
		@zoom="zoom"
		:drag="true"
		ref="clickMap"
		@iconClick="iconClick"
	/>
</template>

<script setup name="sysModule">
	import Form from './buildForm.vue'
	import positionModel from './buildPostion.vue'
	import reBuildingApi from '@/api/biz/reBuildingApi'
	import reProjectApi from '@/api/biz/reProjectApi'
	import { useRoute, useRouter } from 'vue-router'
	import moduleApi from '@/api/sys/resource/moduleApi'

	const route = useRoute()
	const router = useRouter()
	const searchFormState = ref({})
	const modules = ref([])
	const formRef = ref()
	const searchFormRef = ref()
	const clickMap = ref()
	const tableRef = ref()
	const projectId = ref(null)
	const categoryVal = ref(null)
	const overlook = ref('')
	const modelType = ref('')
	const submitLoading = ref(false)
	const isTrue = ref(false) //是否允许拖动
	const checkedTable = ref({}) //保存拖动要打点设备的参数
	const mapTable = ref([]) //打点的坐标数组
	const num = ref(1) //放大缩小倍数
	const toolConfig = { refresh: true, height: true, columnSetting: false, striped: false }
	const columns = [
		{
			title: '楼号',
			align: 'center',
			dataIndex: 'code'
		},
		{
			title: '商业楼栋',
			align: 'center',
			dataIndex: 'type'
		},
		{
			title: '楼栋定位X',
			dataIndex: 'positionX'
		},
		{
			title: '楼栋定位Y',
			dataIndex: 'positionY'
		},
		{
			title: '许可证编号',
			dataIndex: 'extJson'
		},
		{
			title: '创建时间',
			dataIndex: 'createTime',
			align: 'center'
		}
	]
	if (hasPerm(['rebuildDelete', 'reBuildingEdit'])) {
		columns.push({
			title: '操作',
			dataIndex: 'action',
			align: 'center',
			width: '150px'
		})
	}
	let selectedRowKeys = ref([])
	// 列表选择配置
	const options = {
		alert: {
			show: false,
			clear: () => {
				selectedRowKeys = ref([])
			}
		},
		rowSelection: {
			onChange: (selectedRowKey, selectedRows) => {
				selectedRowKeys.value = selectedRowKey
			}
		}
	}
	const loadData = (parameter) => {
		parameter.projectId = route.query.id
		let param = Object.assign(parameter, searchFormState.value)
		return reBuildingApi.reBuildingPage(param).then((res) => {
			return res
		})
	}
	// 删除
	const deleteReBuilding = (record) => {
		let params = [
			{
				id: record.id
			}
		]
		reBuildingApi.reBuildingDelete(params).then(() => {
			tableRef.value.refresh(true)
		})
	}
	// 批量删除
	const deleteBatchReBuilding = () => {
		if (selectedRowKeys.value.length < 1) {
			message.warning('请选择一条或多条数据')
			return false
		}
		const params = selectedRowKeys.value.map((m) => {
			return {
				id: m
			}
		})
		reBuildingApi.reBuildingDelete(params).then(() => {
			tableRef.value.clearRefreshSelected()
		})
	}
	// 重置
	const reset = () => {
		searchFormRef.value.resetFields()
		tableRef.value.refresh(true)
	}

	const loadModule = (parameter) => {
		moduleApi.modulePage2(parameter).then((res) => {
			res.records.splice(0, 1)
			modules.value = res.records
			getClass()
		})
	}

	const getClass = () => {
		modules.value.forEach((el) => {
			if (el.id == route.query.category) {
				modelType.value = el.title
			}
		})
	}

	onMounted(() => {
		loadModule()
		projectId.value = route.query.id
	})

	const selectPoint = () => {
		reBuildingApi.reBuildingPage({ projectId: projectId.value }).then((res) => {
			mapTable.value = []
			res.records.forEach((el, index) => {
				if (!el.positionX) {
					el.positionX = 20
					el.positionY = index * 20
				}
				mapTable.value.push(el)
			})
			reProjectApi.reProjectDetail({ id: projectId.value }).then((res) => {
				clickMap.value.onOpen()
				overlook.value = res.overlook
			})
		})
	}

	const projectHouseBill = (record) => {
		router.push({
			path: '/projectHouseBill',
			query: {
				record: JSON.stringify(record)
			}
		})
	}

	const projectStoreBill = (record) => {
		router.push({
			path: '/projectStoreBill',
			query: {
				record: JSON.stringify(record)
			}
		})
	}

	const projectStorageRoomBill = (record) => {
		router.push({
			path: '/projectStorageRoomBill',
			query: {
				record: JSON.stringify(record)
			}
		})
	}

	const projectWorkshop = (record) => {
		router.push({
			path: '/projectWorkshop',
			query: {
				record: JSON.stringify(record)
			}
		})
	}

	const projectOffice = (record) => {
		router.push({
			path: '/projectOffice',
			query: {
				record: JSON.stringify(record)
			}
		})
	}

	const projectDormitory = (record) => {
		router.push({
			path: '/projectDormitory',
			query: {
				record: JSON.stringify(record)
			}
		})
	}

	// 放大缩小
	const zoom = (value) => {
		if (value == '放大') {
			num.value += 0.1
		} else {
			num.value -= 0.1
		}
	}

	const iconClick = (data) => {
		console.log(data, 'data')
	}

	const dragstart = (data) => {
		checkedTable.value = data
		isTrue.value = false
		console.log('长按拖动中')
	}
	const ondrop = (e) => {
		console.log('长按松开了')
		clickMap.value.ondrop(e)
	}
</script>

<style lang="scss" scoped>
	.billItem {
		color: rgb(22, 119, 255);
		text-align: center;
		cursor: pointer;
		line-height: 30px;
	}
</style>
