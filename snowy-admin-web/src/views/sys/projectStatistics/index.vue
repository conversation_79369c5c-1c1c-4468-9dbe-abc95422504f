<template>
    <div style="text-align: center;height: 50px;align-items: center;">
        <a-affix>
            <span style="font-size: 24px;font-weight: 800">{{ names }}</span>
        </a-affix>
    </div>
    <a-card class="contentAll">

        <div>
            <h3 class="title">住宅统计</h3>
            <div class="header">
                <div class="headerOne">
                    <div class="headerOneColo">{{ housList?.projectTotalNumber ?? '-' }}</div>
                    <div>项目总套数</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ housList?.projectTotalArea ?? '-' }}</div>
                    <div>总面积</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ housList?.projectUnsoldNumber ?? '-' }}</div>
                    <div>未售套数</div>
                </div>

                <div class="headerOne">
                    <div class="headerOneColo">{{ housList?.projectUnsoldArea ?? '-' }}</div>
                    <div>未售面积</div>
                </div>
                <div class="headerOne">
                    <a-popover title="">
                        <template #content>
                            <p>预计回款金额 = 未售房屋总面积 * 房屋市场单价</p>
                        </template>
                        <div class="headerOneColo">{{ housList?.expectedPaymentAmount ?? '-' }}</div>
                        <div>预计回款金额</div>
                    </a-popover>

                </div>
                <div class="headerOne">
                    <!-- <a-popover title=""> -->
                        <!-- <template #content> -->
                            <!-- <p>公式：阿斯顿擦看</p> -->
                        <!-- </template> -->
                        <div class="headerOneColo">{{ housList?.arrearsAmount ?? '-' }}</div>
                        <div>欠款金额</div>
                    <!-- </a-popover> -->
                </div>

            </div>
            <!-- <div class="header">
                <div class="headerOne">
                    <div class="headerOneColo">{{ houseListSale ? houseListSale.soldNumber : '--' }}</div>
                    <div>已销售套数</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{  houseListSale ? houseListSale.soldArea : '--'  }}</div>
                    <div>已销售面积</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{  houseListSale ? houseListSale.paymentAmount : '--'  }}</div>
                    <div>回款金额</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{  houseListSale ? houseListSale.population : '--'  }}</div>
                    <div>安置人口</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{  houseListSale ? houseListSale.signNumber : '--'  }}</div>
                    <div>签约数量</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{  houseListSale ? houseListSale.placementNumber : '--'  }}</div>
                    <div> 安置户数</div>
                </div>
            </div> -->
            <div class="footer">
                <div class="footerOne">
                    <a-button class="footerSpan" @click="taday('4')">今日</a-button>
                    <a-button class="footerSpanOne" @click="mouth('4')">本月</a-button>
                    <a-button class="footerSpanTwo" @click="years('4')">本年</a-button>
                    <a-range-picker v-model:value="value4" @change="sureTime('4')" :format="dateFormat" />
                </div>
                <div class="footerThree">
                    <div class="footerFive">
                        <div class="headerOneColo">{{ houseListSale ? houseListSale.soldNumber : '--' }}</div>
                        <div>已销售套数</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ houseListSale ? houseListSale.soldArea : '--' }}</div>
                        <div>已销售面积</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ houseListSale ? houseListSale.paymentAmount : '--' }}</div>
                        <div>回款金额</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ houseListSale ? houseListSale.population : '--' }}</div>
                        <div>安置人口</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ houseListSale ? houseListSale.placementArea : '--' }}</div>
                        <div>安置面积</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ houseListSale ? houseListSale.signNumber : '--' }}</div>
                        <div>签约数量</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ houseListSale ? houseListSale.placementNumber : '--' }}</div>
                        <div> 安置户数</div>
                    </div>
                </div>
            </div>
            <div class="headers" style="padding: 0 30px;">
                <a-table :scroll="{ y: 500 }" :pagination="false" style="width: 100%;" :customHeaderRow="customRow"
                    :dataSource="houseTypeList" :columns="columns">
                    <template #bodyCell="{ column, text, record, index }">
                        <template v-if="column.dataIndex === 'houseLayout'">
                            <div>
                                {{ $TOOL.dictTypeData('house_layout', record.houseLayout) != '无此字典项'
                                    ? $TOOL.dictTypeData('house_layout', record.houseLayout) : record.houseLayout }}
                            </div>
                        </template>
                    </template>
                </a-table>
            </div>
            <h3 class="title">商业统计</h3>
            <div class="header">
                <div class="headerOne">
                    <div class="headerOneColo">{{ businessList ? businessList.projectTotalNumber : "--" }}</div>
                    <div>项目总套数</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ businessList ? businessList.projectTotalArea : "--" }}</div>
                    <div>总面积</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ businessList ? businessList.expectedPaymentAmount : "--" }}</div>
                    <div>欠款金额</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ businessList ? businessList.projectUnsoldNumber : "--" }}</div>
                    <div>未售套数</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ businessList ? businessList.projectUnsoldArea : "--" }}</div>
                    <div>未售面积</div>
                </div>
                <!-- <div class="headerOne">
                    <div class="headerOneColo">{{ businessList ? businessList.expectedPaymentAmount : "--" }}</div>
                    <div>预计回款金额</div>
                </div> -->
            </div>
            <div class="footer">
                <div class="footerOne">
                    <a-button class="footerSpan" @click="taday('1')">今日</a-button>
                    <a-button class="footerSpanOne" @click="mouth('1')">本月</a-button>
                    <a-button class="footerSpanTwo" @click="years('1')">本年</a-button>
                    <a-range-picker v-model:value="value1" @change="sureTime('1')" :format="dateFormat" />
                </div>

                <div class="business">
                    <div class="businessOne">
                        <div class="businessTwo">
                            <div class="headerOneColo">{{ businessHouseList ? businessHouseList.soldNumber : "--" }}
                            </div>
                            <div>已销售套数</div>
                        </div>
                    </div>
                    <div class="businessOne">
                        <div class="businessTwo">
                            <div class="headerOneColo">{{ businessHouseList ? businessHouseList.soldArea : "--" }}
                            </div>
                            <div>已销售面积</div>
                        </div>
                    </div>
                    <div class="businessOne">
                        <div class="businessTwo">
                            <div class="headerOneColo">{{ businessHouseList ? businessHouseList.paymentAmount : "--"
                                }}</div>
                            <div>回款金额</div>
                        </div>
                    </div>
                    <div class="businessOne">
                        <div class="businessTwo">
                            <div class="headerOneColo">{{ businessHouseList ? businessHouseList.signNumber : "--" }}
                            </div>
                            <div>签约套数</div>
                        </div>
                    </div>
                    <div class="businessOne">
                        <div class="businessTwo">
                            <div class="headerOneColo">{{ businessHouseList ? businessHouseList.rentedNumber : "--" }}
                            </div>
                            <div>已租赁套数</div>
                        </div>
                    </div>
                    <div class="businessOne">
                        <div class="businessTwo">
                            <div class="headerOneColo">{{ businessHouseList ? businessHouseList.rentedArea : "--" }}
                            </div>
                            <div>已租赁面积</div>
                        </div>
                    </div>
                    <div class="businessOne">
                        <div class="businessTwo">
                            <div class="headerOneColo">{{ businessHouseList ? businessHouseList.rentalPaymentAmount :
                                "--" }}</div>
                            <div>租赁回款金额</div>
                        </div>
                    </div>
                    <div class="businessOne">
                        <div class="businessTwo">
                            <div class="headerOneColo">{{ businessHouseList ? businessHouseList.rentalSignNumber :
                                "--" }}</div>
                            <div>租赁签约套数</div>
                        </div>
                    </div>
                </div>
            </div>
            <h3 class="title">车位统计</h3>
            <div class="header">
                <div class="headerOne">
                    <div class="headerOneColo">{{ carList ? carList.projectTotalNumber : "--" }}</div>
                    <div>总套数</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ carList ? carList.totalValue : "--" }}</div>
                    <div>总价值</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ carList ? carList.projectUnsoldNumber : "--" }}</div>
                    <div>未售套数</div>
                </div>
                <div class="headerOne">
                    <a-popover title="">
                        <template #content>
                            <p>未售车位总价总和</p>
                        </template>
                    <div class="headerOneColo">{{ carList ? carList.expectedPaymentAmount : "--" }}</div>
                    <div>预计回款金额</div>
                    </a-popover>
                </div>
            </div>
            <div class="footer">
                <div class="footerOne">
                    <a-button class="footerSpan" @click="taday('2')">今日</a-button>
                    <a-button class="footerSpanOne" @click="mouth('2')">本月</a-button>
                    <a-button class="footerSpanTwo" @click="years('2')">本年</a-button>
                    <a-range-picker v-model:value="value2" @change="sureTime('2')" :format="dateFormat" />
                </div>
                <div class="footerThree">
                    <div class="footerFive">
                        <div class="headerOneColo">{{ carListSale ? carListSale.soldNumber : "--" }}</div>
                        <div>已销售套数</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ carListSale ? carListSale.paymentAmount : "--" }}</div>
                        <div>回款金额</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ carListSale ? carListSale.giftNumber : "--" }}</div>
                        <div>赠送套数</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ carListSale ? carListSale.signNumber : "--" }}</div>
                        <div>签约套数</div>
                    </div>
                </div>
            </div>
            <h3 class="title">储藏室统计</h3>
            <div class="header">
                <div class="headerOne">
                    <div class="headerOneColo">{{ LockerRoomList ? LockerRoomList.projectTotalNumber : "--" }}</div>
                    <div>总套数</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ LockerRoomList ? LockerRoomList.projectTotalArea : "--" }}</div>
                    <div>总面积</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ LockerRoomList ? LockerRoomList.projectUnsoldNumber : "--" }}</div>
                    <div>未售套数</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ LockerRoomList ? LockerRoomList.projectUnsoldArea : "--" }}</div>
                    <div>未售面积</div>
                </div>
                <div class="headerOne">
                    <a-popover title="">
                        <template #content>
                            <p>未售房源总价总和</p>
                        </template>
                    <div class="headerOneColo">{{ LockerRoomList ? LockerRoomList.expectedPaymentAmount : "--" }}
                    </div>
                    <div>预计回款金额</div>
                    </a-popover>
                </div>
            </div>
            <div class="footer">
                <div class="footerOne">
                    <a-button class="footerSpan" @click="taday('3')">今日</a-button>
                    <a-button class="footerSpanOne" @click="mouth('3')">本月</a-button>
                    <a-button class="footerSpanTwo" @click="years('3')">本年</a-button>
                    <a-range-picker v-model:value="value3" @change="sureTime('3')" :format="dateFormat" />
                    <!-- <a-range-picker show-time  class="footerSpanFour" @blur="sureTime">
                        <template #renderExtraFooter></template>
</a-range-picker> -->
                </div>
                <div class="footerThree">
                    <div class="footerFive">
                        <div class="headerOneColo">{{ LockerRoomHouseList ? LockerRoomHouseList.soldNumber : "--" }}
                        </div>
                        <div>已销售套数</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ LockerRoomHouseList ? LockerRoomHouseList.soldArea : "--" }}
                        </div>
                        <div>已销售面积</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ LockerRoomHouseList ? LockerRoomHouseList.paymentAmount : "--"
                            }}</div>
                        <div>回款金额</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ LockerRoomHouseList ? LockerRoomHouseList.signNumber : "--" }}
                        </div>
                        <div>签约套数</div>
                    </div>
                </div>
            </div>
        </div>
    </a-card>
</template>
<script setup name="ProjectStatistics">
import router from '@/router'
import { useMenuStore } from '@/store/menu';
import projectStatisticsApi from "@/api/biz/projectStatisticsApi"
const menuStore = useMenuStore()
const projectId = ref('')
projectId.value = menuStore.projectObj.id
const names = menuStore.projectObj.name
const housList = ref()
const houseTypeList = ref([])

const houseListSale = ref()
const businessList = ref()
const LockerRoomList = ref()
const businessHouseList = ref()
const LockerRoomHouseList = ref()
const carList = ref()
const carListSale = ref()
const value1 = ref([]);
const value2 = ref([]);
const value3 = ref([]);
const value4 = ref([])
const dateFormat = 'YYYY/MM/DD';
const columns = [
    {
        title: '户型',
        dataIndex: 'houseLayout',
        ellipsis: true,
        align: 'center',
        width: 100
    },
    {
        title: '总套数（套）',
        dataIndex: 'totalNumber',
        ellipsis: true,
        align: 'center'
    },
    {
        title: '总面积(㎡)',
        dataIndex: 'totalArea',
        ellipsis: true,
        align: 'center'
    },
    {
        title: '已售套数(套)',
        dataIndex: 'soldNumber',
        ellipsis: true,
        align: 'center'
    },
    {
        title: '已售面积(㎡)',
        dataIndex: 'soldArea',
        ellipsis: true,
        align: 'center'
    },
    {
        title: '未售套数(套)',
        dataIndex: 'unsoldNumber',
        ellipsis: true,
        align: 'center'
    },
    {
        title: '未售面积(㎡)',
        dataIndex: 'unsoldArea',
        ellipsis: true,
        align: 'center'
    },
]
const starTime = ref('')
const endTime = ref('')
//日历选择确认
const sureTime = (t) => {
    if (t === "1") {
        if (!value1.value) {
            projectStatisticsApi.peojectBusinessHouse({ projectId: projectId.value, type: "2" }).then(res => {
                businessHouseList.value = res
            })
        } else {
            starTime.value = getTime(value1.value[0].$d).formattedDate
            endTime.value = getTime(value1.value[1].$d).endTimes
            projectStatisticsApi.peojectBusinessHouse({ projectId: projectId.value, type: "2", startDateTime: starTime.value, endDateTime: endTime.value }).then(res => {
                businessHouseList.value = res
            })
        }

    }
    if (t === "2") {
        if (!value2.value) {
            projectStatisticsApi.projectCarSale({ projectId: projectId.value }).then(res => {
                carListSale.value = res
            })
        } else {
            starTime.value = getTime(value2.value[0].$d).formattedDate
            endTime.value = getTime(value2.value[1].$d).endTimes
            projectStatisticsApi.projectCarSale({ projectId: projectId.value, startDateTime: starTime.value, endDateTime: endTime.value }).then(res => {
                carListSale.value = res
            })
        }

    }
    if (t === "3") {
        if (!value3.value) {
            projectStatisticsApi.peojectLockerHouse({ projectId: projectId.value, type: "3" }).then(res => {
                LockerRoomHouseList.value = res
            })
        } else {
            starTime.value = getTime(value3.value[0].$d).formattedDate
            endTime.value = getTime(value3.value[1].$d).endTimes
            projectStatisticsApi.peojectLockerHouse({ projectId: projectId.value, type: "3", startDateTime: starTime.value, endDateTime: endTime.value }).then(res => {
                LockerRoomHouseList.value = res
            })
        }

    }
    if (t === "4") {
        if (!value4.value) {
            projectStatisticsApi.projectHouseSale({ projectId: projectId.value, type: "1" }).then(res => {
                houseListSale.value = res
            })
        } else {
            starTime.value = getTime(value4.value[0].$d).formattedDate
            endTime.value = getTime(value4.value[1].$d).endTimes
            projectStatisticsApi.projectHouseSale({ projectId: projectId.value, type: "1", startDateTime: starTime.value, endDateTime: endTime.value }).then(res => {
                houseListSale.value = res
            })
        }

    }

}
//将选取的时间进行格式化
const getTime = (time) => {
    const dates = new Date(time);
    const year = dates.getFullYear();
    const month = dates.getMonth() + 1; // 月份从0开始，所以加1
    const day = dates.getDate();

    const hours = dates.getHours();
    const minutes = dates.getMinutes();
    const seconds = dates.getSeconds();

    const formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} 00:00:00`;
    const endTimes = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} 23:59:59`;
    return { formattedDate, endTimes }
}
//获取今天开始到此刻的时间
const taday = (t) => {
    const { start, end } = getToday();

    if (t == "1") {
        projectStatisticsApi.peojectBusinessHouse({ projectId: projectId.value, type: "2", startDateDay: start, endDateDay: end }).then(res => {
            businessHouseList.value = res
        })
    }
    if (t == "2") {
        projectStatisticsApi.projectCarSale({ projectId: projectId.value, startDateTimeMinute: start, endDateTimeMinute: end }).then(res => {
            carListSale.value = res
        })
    }
    if (t == "3") {
        projectStatisticsApi.peojectLockerHouse({ projectId: projectId.value, type: "3", startDateDay: start, endDateDay: end }).then(res => {
            LockerRoomHouseList.value = res
        })
    }
    if (t == "4") {
        projectStatisticsApi.projectHouseSale({ projectId: projectId.value, type: "3", startDateDay: start, endDateDay: end }).then(res => {
            houseListSale.value = res
        })
    }



}
// 获取本月的时间范围
const mouth = (t) => {
    const { start, now } = getMounth();

    if (t == "1") {
        projectStatisticsApi.peojectBusinessHouse({ projectId: projectId.value, type: "2", startDateDay: start, endDateDay: now }).then(res => {
            businessHouseList.value = res
        })
    }
    if (t == "2") {
        projectStatisticsApi.projectCarSale({ projectId: projectId.value, startDateDay: start, endDateDay: now }).then(res => {
            carListSale.value = res
        })
    }
    if (t == "3") {
        projectStatisticsApi.peojectLockerHouse({ projectId: projectId.value, type: "3", startDateDay: start, endDateDay: now }).then(res => {
            LockerRoomHouseList.value = res
        })
    }
    if (t == "4") {
        projectStatisticsApi.projectHouseSale({ projectId: projectId.value, type: "3", startDateDay: start, endDateDay: now }).then(res => {
            houseListSale.value = res
        })

    }

}
//获取本年的时间范围
const years = (t) => {
    const { start, now } = getYears();

    if (t == "1") {
        projectStatisticsApi.peojectBusinessHouse({ projectId: projectId.value, type: "2", startDateDay: start, endDateDay: now }).then(res => {
            businessHouseList.value = res
        })
    }
    if (t == "2") {
        projectStatisticsApi.projectCarSale({ projectId: projectId.value, startDateDay: start, endDateDay: now }).then(res => {
            carListSale.value = res
        })
    }
    if (t == "3") {
        projectStatisticsApi.peojectLockerHouse({ projectId: projectId.value, type: "3", startDateDay: start, endDateDay: now }).then(res => {
            LockerRoomHouseList.value = res
        })
    }
    if (t == "4") {
        projectStatisticsApi.projectHouseSale({ projectId: projectId.value, type: "3", startDateDay: start, endDateDay: now }).then(res => {
            houseListSale.value = res
        })

    }
}
//获取今日时间
const getToday = () => {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = String(currentDate.getMonth() + 1).padStart(2, '0');
    const day = String(currentDate.getDate()).padStart(2, '0');
    // 开始时间
    const start = `${year}-${month}-${day} 00:00:00`;
    // 结束时间
    const end = `${year}-${month}-${day} 23:59:59`;
    return { start, end };
}
// 获取本月开始时间和当前时间
const getMounth = () => {
    const currentDate = new Date();
    // 获取本月的第一天
    const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    // 格式化为 YYYY-MM-DD HH:mm:ss
    const formatDateTime = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，所以要加 1
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };
    return {
        start: formatDateTime(startOfMonth),
        now: formatDateTime(currentDate)
    };
}
//获取今年时间和当前时间
const getYears = () => {
    const currentDate = new Date();
    // 获取今年的开始时间
    const startOfYear = new Date(currentDate.getFullYear(), 0, 1); // 0 表示1月
    // 格式化为 YYYY-MM-DD HH:mm:ss
    const formatDateTime = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，所以要加 1
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };
    return {
        start: formatDateTime(startOfYear),
        now: formatDateTime(currentDate)
    };
}
//房屋统计
const getHouse = () => {
    projectStatisticsApi.projectHouseAll({ projectId: projectId.value}).then(res => {
        housList.value = res
    })
}
// 房屋销售统计
const getSaleHouse = () => {
    projectStatisticsApi.projectHouseSale({ projectId: projectId.value, type: "1" }).then(res => {
        console.log(res, 'sressd4');

        houseListSale.value = res
    })
}
//房屋类型统计
const getHouseType = () => {
    projectStatisticsApi.projectHouseType({ projectId: projectId.value, type: "1" }).then(res => {
        houseTypeList.value = res
    })
}
//商业房屋统计
const getBusinessRoom = () => {
    projectStatisticsApi.projectLockerRoom({ projectId: projectId.value, type: "2" }).then(res => {
        businessList.value = res
    })
}
// 储物间房屋统计
const getLockerRoom = () => {
    projectStatisticsApi.projectLockerRoom({ projectId: projectId.value, type: "3" }).then(res => {
        LockerRoomList.value = res
    })
}
//商业房屋销售统计
const getBusinessHouse = () => {
    projectStatisticsApi.peojectBusinessHouse({ projectId: projectId.value, type: "2" }).then(res => {
        businessHouseList.value = res
    })
}
// 储物间房屋销售统计
const getLockerHouse = () => {
    projectStatisticsApi.peojectLockerHouse({ projectId: projectId.value, type: "3" }).then(res => {
        LockerRoomHouseList.value = res
    })
}
// 车位统计
const getCarList = () => {
    projectStatisticsApi.projectCar({ projectId: projectId.value }).then(res => {
        carList.value = res
    })
}
//车位销售
const getCarListSale = () => {
    projectStatisticsApi.projectCarSale({ projectId: projectId.value }).then(res => {
        carListSale.value = res
    })
}
onMounted(() => {
    getSaleHouse()
    getHouseType()
    getBusinessRoom()
    getLockerRoom()
    getBusinessHouse()
    getLockerHouse()
    getCarList()
    getCarListSale()
    getHouse()
    console.log(menuStore, "[=====]");


})
</script>
<style lang="scss" scoped>
.headers {
    width: 100%;
    border-radius: 10px;
    background-color: rgb(255, 255, 255);
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 20px;
}

.contentAll {
    background-color: rgb(242, 242, 242);
    height: 100vh;
    width: 100%;
    overflow: auto;

    .title {
        font-weight: 800;
        font-size: 24px;
    }

    .header {
        height: 250px;
        width: 100%;
        border-radius: 10px;
        background-color: rgb(255, 255, 255);
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-bottom: 20px;

        .headerOne {
            width: 150px;
            height: 80px;
            border-radius: 10px;
            background-color: beige;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            font-size: 18px;
            font-weight: 700;
        }
    }

    .footer {
        height: 270px;
        width: 100%;
        border-radius: 10px;
        background-color: rgb(255, 255, 255);
        padding: 20px;
        margin-bottom: 20px;

        .footerOne {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .footerSpan {
                margin-left: 15px;
                background-color: rgb(255, 183, 1);
                color: #ffffff;
                width: 120px;
                height: 40px;
            }
        }

        .footerThree {
            display: flex;
            justify-content: space-around;
            margin-top: 50px;

            .footerFive {
                width: 150px;
                height: 80px;
                border-radius: 10px;
                background-color: beige;
                display: flex;
                justify-content: center;
                flex-direction: column;
                align-items: center;
                font-size: 18px;
                font-weight: 700;
            }
        }
    }
}

.headerOneColo {
    color: rgb(0, 0, 255);
}

.business {
    flex-wrap: wrap;
    display: flex;
    justify-content: space-around;
    margin-top: 10px;

    .businessOne {
        width: calc((100% - 300px) / 4);
        height: 80px;
        border-radius: 10px;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        font-size: 18px;
        font-weight: 700;
        margin-top: 10px;

        .businessTwo {
            background-color: beige;
            width: 150px;
            height: 80px;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            -radius: 10px;
        }
    }
}

.footerSpanOne {
    margin-left: 15px;
    background-color: rgb(33, 160, 250);
    color: #ffffff;
    width: 120px;
    height: 40px;
}

.footerSpanTwo {
    margin-left: 15px;
    background-color: rgb(108, 202, 201);
    color: #ffffff;
    width: 120px;
    height: 40px;
    margin-right: 15px;
}

.footerSpanFour {
    margin-left: 15px;
    height: 40px;
}
</style>
