<template>
    <div style="text-align: center;height: 50px;align-items: center;">
        <a-affix style="">
            <span style="font-size: 24px;font-weight: 800;">{{ names }}</span>
        </a-affix>
    </div>
    <a-card class="contentAll">
        <div>
            <h3 class="title">厂房统计</h3>
            <div class="header">
                <div class="headerOne">
                    <div class="headerOneColo">{{ factyList.workshop[0]?.totalHouse ?? '--' }}</div>
                    <div>项目总套数</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ factyList.workshop[0]?.totalArea ?? '--' }}</div>
                    <div>总面积</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ factyList.workshop[0]?.notRentHouse ?? '--' }}</div>
                    <div>未租赁套数</div>
                </div>
            </div>
            <div class="footer">
                <div class="footerOne">
                    <a-button class="footerSpan" @click="getTaday">今日</a-button>
                    <a-button class="footerSpanOne" @click="getMounth">本月</a-button>
                    <a-button class="footerSpanTwo" @click="getYear">本年</a-button>
                    <a-range-picker v-model:value="value1" @change="sureTime('1')" :format="dateFormat" />
                </div>

                <div class="business">
                    <div class="businessOne">
                        <div class="businessTwo">
                            <div class="headerOneColo">{{ factyList.workshop[1]?.rentHouse ?? '--' }}</div>
                            <div>已租赁套数</div>
                        </div>
                    </div>
                    <div class="businessOne">
                        <div class="businessTwo">
                            <div class="headerOneColo">{{ factyList.workshop[1]?.rentArea ?? '--' }}</div>
                            <div>已租赁面积</div>
                        </div>
                    </div>
                    <div class="businessOne">
                        <div class="businessTwo">
                            <div class="headerOneColo">{{ factyList.workshop[1]?.signHouse ?? '--' }}</div>
                            <div>租赁签约套数</div>
                        </div>
                    </div>
                </div>
            </div>
            <h3 class="title">办公室统计</h3>
            <div class="header">
                <div class="headerOne">
                    <div class="headerOneColo">{{ factyList.officebuilding[0]?.totalHouse ?? '--' }}</div>
                    <div>项目总套数</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ factyList.officebuilding[0]?.totalArea ?? '--' }}</div>
                    <div>总面积</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ factyList.officebuilding[0]?.notRentHouse ?? '--' }}</div>
                    <div>未租赁套数</div>
                </div>
            </div>
            <div class="footer">
                <div class="footerOne">
                    <a-button class="footerSpan" @click="getTadayTwo">今日</a-button>
                    <a-button class="footerSpanOne" @click="getMounthTwo">本月</a-button>
                    <a-button class="footerSpanTwo" @click="getYearTwo">本年</a-button>
                    <a-range-picker v-model:value="value2" @change="sureTime('2')" :format="dateFormat" />
                </div>
                <div class="footerThree">
                    <div class="footerFive">
                        <div class="headerOneColo">{{ factyList.officebuilding[1]?.rentHouse ?? '--' }}</div>
                        <div>已租赁套数</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ factyList.officebuilding[1]?.rentArea ?? '--' }}</div>
                        <div>已租赁面积</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ factyList.officebuilding[1]?.signHouse ?? '--' }}</div>
                        <div>签约租赁套数</div>
                    </div>
                </div>
            </div>
            <h3 class="title">宿舍统计</h3>
            <div class="header">
                <div class="headerOne">
                    <div class="headerOneColo">{{ factyList.dormitory[0] ? factyList.dormitory[0].totalHouse : '--' }}
                    </div>
                    <div>项目总套数</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ factyList.dormitory[0] ? factyList.dormitory[0].totalArea : '--' }}
                    </div>
                    <div>总面积</div>
                </div>
                <div class="headerOne">
                    <div class="headerOneColo">{{ factyList.dormitory[0] ? factyList.dormitory[0].totalHouse : '--' }}
                    </div>
                    <div>未租赁套数</div>
                </div>
            </div>
            <div class="footer">
                <div class="footerOne">
                    <a-button class="footerSpan" @click="getTadayThree">今日</a-button>
                    <a-button class="footerSpanOne" @click="getMounthThree">本月</a-button>
                    <a-button class="footerSpanTwo" @click="getYearThree">本年</a-button>
                    <a-range-picker v-model:value="value3" @change="sureTime('3')" :format="dateFormat" />
                </div>
                <div class="footerThree">
                    <div class="footerFive">
                        <div class="headerOneColo">{{ factyList.dormitory[1]?.rentHouse ?? '--' }}</div>
                        <div>已租赁套数</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ factyList.dormitory[1]?.rentArea ?? '--' }}</div>
                        <div>已租赁面积</div>
                    </div>
                    <div class="footerFive">
                        <div class="headerOneColo">{{ factyList.dormitory[1]?.signHouse ?? '--' }}</div>
                        <div>签约租赁套数</div>
                    </div>
                </div>
            </div>
        </div>
    </a-card>
</template>
<script setup name="ParkProjectStatistics">
import { useMenuStore } from '@/store/menu';
import parkProjectStatisticsApi from "@/api/biz/parkProjectStatisticsApi"
const menuStore = useMenuStore()
const projectId = ref('')
projectId.value = menuStore.projectObj.id
const names = menuStore.projectObj.name
const value1 = ref([]);
const value2 = ref([]);
const value3 = ref([]);

//统计数组
const factyList = ref({
    dormitory: [
        {},
        {}
    ],
    workshop: [
        {},
        {}
    ],
    officebuilding: [
        {},
        {}
    ]
})

//厂区的本年月日
const getTaday = () => {
    const { getStarDays, getEndDays } = getTime()
    parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: getStarDays, endTime: getEndDays }).then(res => {
        if (res.dormitory) {
            factyList.value.dormitory[0].signHouse = res.dormitory[0].signHouse
            factyList.value.dormitory[0].rentArea = res.dormitory[0].rentArea
            factyList.value.dormitory[0].rentHouse = res.dormitory[0].rentHouse
        } else {
            factyList.value.dormitory[0].signHouse = '0'
            factyList.value.dormitory[0].rentArea = '0'
            factyList.value.dormitory[0].rentHouse = '0'
        }
    })
}
const getMounth = () => {
    const { getMounth, getEndDays } = getTime()
    parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: getMounth, endTime: getEndDays }).then(res => {
        if (res.dormitory) {
            factyList.value.dormitory[0].signHouse = res.dormitory[0].signHouse
            factyList.value.dormitory[0].rentArea = res.dormitory[0].rentArea
            factyList.value.dormitory[0].rentHouse = res.dormitory[0].rentHouse
        } else {
            factyList.value.dormitory[0].signHouse = '0'
            factyList.value.dormitory[0].rentArea = '0'
            factyList.value.dormitory[0].rentHouse = '0'
        }
    })
}
const getYear = () => {
    const { getYears, getEndDays } = getTime()
    parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: getYears, endTime: getEndDays }).then(res => {
        if (res.dormitory) {
            factyList.value.dormitory[0].signHouse = res.dormitory[0].signHouse
            factyList.value.dormitory[0].rentArea = res.dormitory[0].rentArea
            factyList.value.dormitory[0].rentHouse = res.dormitory[0].rentHouse
        } else {
            factyList.value.dormitory[0].signHouse = '0'
            factyList.value.dormitory[0].rentArea = '0'
            factyList.value.dormitory[0].rentHouse = '0'
        }
    })
}
//办公室的年月日
const getTadayTwo = () => {
    const { getStarDays, getEndDays } = getTime()
    parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: getStarDays, endTime: getEndDays }).then(res => {
        if (res.officebuilding) {
            factyList.value.officebuilding[0].signHouse = res.officebuilding[0].signHouse
            factyList.value.officebuilding[0].rentArea = res.officebuilding[0].rentArea
            factyList.value.officebuilding[0].rentHouse = res.officebuilding[0].rentHouse
        } else {
            factyList.value.officebuilding[0].signHouse = '0'
            factyList.value.officebuilding[0].rentArea = '0'
            factyList.value.officebuilding[0].rentHouse = '0'
        }
    })
}
const getMounthTwo = () => {
    const { getMounth, getEndDays } = getTime()
    parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: getMounth, endTime: getEndDays }).then(res => {
        if (res.officebuilding) {
            factyList.value.officebuilding[0].signHouse = res.officebuilding[0].signHouse
            factyList.value.officebuilding[0].rentArea = res.officebuilding[0].rentArea
            factyList.value.officebuilding[0].rentHouse = res.officebuilding[0].rentHouse
        } else {
            factyList.value.officebuilding[0].signHouse = '0'
            factyList.value.officebuilding[0].rentArea = '0'
            factyList.value.officebuilding[0].rentHouse = '0'
        }
    })
}
const getYearTwo = () => {
    const { getYears, getEndDays } = getTime()
    parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: getYears, endTime: getEndDays }).then(res => {
        if (res.officebuilding) {
            factyList.value.officebuilding[0].signHouse = res.officebuilding[0].signHouse
            factyList.value.officebuilding[0].rentArea = res.officebuilding[0].rentArea
            factyList.value.officebuilding[0].rentHouse = res.officebuilding[0].rentHouse
        } else {
            factyList.value.officebuilding[0].signHouse = '0'
            factyList.value.officebuilding[0].rentArea = '0'
            factyList.value.officebuilding[0].rentHouse = '0'
        }
    })
}
//宿舍的年月日
const getTadayThree = () => {
    const { getStarDays, getEndDays } = getTime()
    parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: getStarDays, endTime: getEndDays }).then(res => {
        if (res.workshop) {
            factyList.value.workshop[0].signHouse = res.workshop[0].signHouse
            factyList.value.workshop[0].rentArea = res.workshop[0].rentArea
            factyList.value.workshop[0].rentHouse = res.workshop[0].rentHouse
        } else {
            factyList.value.workshop[0].signHouse = '0'
            factyList.value.workshop[0].rentArea = '0'
            factyList.value.workshop[0].rentHouse = '0'
        }
    })
}
const getMounthThree = () => {
    const { getMounth, getEndDays } = getTime()
    parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: getMounth, endTime: getEndDays }).then(res => {
        if (res.workshop) {
            factyList.value.workshop[0].signHouse = res.workshop[0].signHouse
            factyList.value.workshop[0].rentArea = res.workshop[0].rentArea
            factyList.value.workshop[0].rentHouse = res.workshop[0].rentHouse
        } else {
            factyList.value.workshop[0].signHouse = '0'
            factyList.value.workshop[0].rentArea = '0'
            factyList.value.workshop[0].rentHouse = '0'
        }
    })
}
const getYearThree = () => {
    const { getYears, getEndDays } = getTime()
    parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: getYears, endTime: getEndDays }).then(res => {
        if (res.workshop) {
            factyList.value.workshop[0].signHouse = res.workshop[0].signHouse
            factyList.value.workshop[0].rentArea = res.workshop[0].rentArea
            factyList.value.workshop[0].rentHouse = res.workshop[0].rentHouse
        } else {
            factyList.value.workshop[0].signHouse = '0'
            factyList.value.workshop[0].rentArea = '0'
            factyList.value.workshop[0].rentHouse = '0'
        }
    })
}

const starTime = ref('')
const endTime = ref('')
//日历选择确认
const sureTime = (t) => {
    if (t === "1") {
        if (!value1.value) {
            const { getYears, getEndDays } = getTime()
            parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: getYears, endTime: getEndDays }).then(res => {
                factyList.value.workshop[0] = res.workshop[0]
            })
        } else {
            starTime.value = getTimes(value1.value[0].$d).formattedDate
            endTime.value = getTimes(value1.value[1].$d).endTimes
            parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: starTime.value, endTime: endTime.value }).then(res => {
                if (res.workshop) {
                    factyList.value.workshop[0].signHouse = res.workshop[0].signHouse
                    factyList.value.workshop[0].rentArea = res.workshop[0].rentArea
                    factyList.value.workshop[0].rentHouse = res.workshop[0].rentHouse
                } else {
                    factyList.value.workshop[0].signHouse = '0'
                    factyList.value.workshop[0].rentArea = '0'
                    factyList.value.workshop[0].rentHouse = '0'
                }
            })
        }

    }
    if (t === "2") {
        if (!value2.value) {
            const { getYears, getEndDays } = getTime()
            parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: getYears, endTime: getEndDays }).then(res => {
                factyList.value.officebuilding[0] = res.officebuilding[0]
            })
        } else {
            starTime.value = getTimes(value2.value[0].$d).formattedDate
            endTime.value = getTimes(value2.value[1].$d).endTimes
            parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: starTime.value, endTime: endTime.value }).then(res => {
                if (res.officebuilding) {
                    factyList.value.officebuilding[0].signHouse = res.officebuilding[0].signHouse
                    factyList.value.officebuilding[0].rentArea = res.officebuilding[0].rentArea
                    factyList.value.officebuilding[0].rentHouse = res.officebuilding[0].rentHouse
                } else {
                    factyList.value.officebuilding[0].signHouse = '0'
                    factyList.value.officebuilding[0].rentArea = '0'
                    factyList.value.officebuilding[0].rentHouse = '0'
                }
            })
        }

    }
    if (t === "3") {
        if (!value3.value) {
            const { getYears, getEndDays } = getTime()
            parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: getYears, endTime: getEndDays }).then(res => {
                factyList.value.dormitory[0] = res.dormitory[0]
            })
        } else {
            starTime.value = getTimes(value3.value[0].$d).formattedDate
            endTime.value = getTimes(value3.value[1].$d).endTimes
            parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: starTime.value, endTime: endTime.value }).then(res => {
                if (res.dormitory) {
                    factyList.value.dormitory[0].signHouse = res.dormitory[0].signHouse
                    factyList.value.dormitory[0].rentArea = res.dormitory[0].rentArea
                    factyList.value.dormitory[0].rentHouse = res.dormitory[0].rentHouse
                } else {
                    factyList.value.dormitory[0].signHouse = '0'
                    factyList.value.dormitory[0].rentArea = '0'
                    factyList.value.dormitory[0].rentHouse = '0'
                }
            })
        }

    }

}

//将选取的时间进行格式化
const getTimes = (time) => {
    const dates = new Date(time);
    const year = dates.getFullYear();
    const month = dates.getMonth() + 1; // 月份从0开始，所以加1  
    const day = dates.getDate();

    const hours = dates.getHours();
    const minutes = dates.getMinutes();
    const seconds = dates.getSeconds();

    const formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} 00:00:00`;
    const endTimes = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} 23:59:59`;
    return { formattedDate, endTimes }
}

//获取厂区总数
const getfacty = () => {
    const { getYears, getendYears } = getTime()
    parkProjectStatisticsApi.factoryAreaAll({ ProjectId: projectId.value, startTime: getYears, endTime: getendYears }).then(res => {
        if (res.workshop) {
            for (let i = 0; i < res.workshop.length; i++) {
                if (res.workshop[i].flag == 'up') {
                    factyList.value.workshop[0] = res.workshop[i]
                } else {
                    factyList.value.workshop[1] = res.workshop[i]
                }
            }
        }
        if (res.dormitory) {
            for (let i = 0; i < res.dormitory.length; i++) {
                if (res.dormitory[i].flag == 'up') {
                    factyList.value.dormitory[0] = res.dormitory[i]
                } else {
                    factyList.value.dormitory[1] = res.dormitory[i]
                }
            }
        }
        if (res.officebuilding) {
            for (let i = 0; i < res.officebuilding.length; i++) {
                if (res.officebuilding[i].flag == 'up') {
                    factyList.value.officebuilding[0] = res.officebuilding[i]
                } else {
                    factyList.value.officebuilding[1] = res.officebuilding[i]
                }
            }
        }
    })
}

//获取时间
const getTime = () => {
    // 获取当前日期  
    const now = new Date();

    // 获取今年的开始时间  
    const startOfYear = new Date(now.getFullYear(), 0, 1); // 0 表示一月  

    const currentYear = new Date().getFullYear();


    const endOfYear = new Date(currentYear, 11, 31, 23, 59, 59);
    // 获取本月的开始时间  
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1); // getMonth() 返回当前月份，从 0 开始  

    // 格式化日期为 "YYYY-MM-DD HH:mm:ss"  
    function formatDate(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需加 1  
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        const nowDayStar = `${year}-${month}-${day} 00:00:00`
        const nowDayEnd = `${year}-${month}-${day} 23:59:59`
        const nowDayTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
        const nowYear = `${year}-${month}-${day}`
        const endYear = `${year}-${month}-${day} `
        return { nowDayStar, nowDayEnd, nowDayTime, nowYear, endYear };
    }

    //获取今年开始时间
    const getYears = formatDate(startOfYear).nowDayTime
    const getendYears = formatDate(endOfYear).nowDayTime
    //获取当前日期
    const getNow = formatDate(now).nowDayTime
    //"本月开始时间
    const getMounth = formatDate(startOfMonth).nowDayTime
    //获取今天开始时间
    const getStarDays = formatDate(now).nowDayStar
    // 获取今天结束时间
    const getEndDays = formatDate(now).nowDayEnd




    return { getYears, getNow, getMounth, getStarDays, getEndDays, getendYears }
}

onMounted(() => {
    getfacty()
    // getTime();

})
</script>
<style lang="scss" scoped>
.contentAll {
    background-color: rgb(242, 242, 242);
    height: 100vh;
    width: 100%;
    overflow: auto;

    .title {
        font-weight: 800;
        font-size: 24px;
    }

    .header {
        height: 250px;
        width: 100%;
        border-radius: 10px;
        background-color: rgb(255, 255, 255);
        display: flex;
        justify-content: space-around;
        align-items: center;
        margin-bottom: 20px;

        .headerOne {
            width: 150px;
            height: 80px;
            border-radius: 10px;
            background-color: beige;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            font-size: 18px;
            font-weight: 700;
        }
    }

    .footer {
        height: 270px;
        width: 100%;
        border-radius: 10px;
        background-color: rgb(255, 255, 255);
        padding: 20px;
        margin-bottom: 20px;

        .footerOne {
            display: flex;
            justify-content: flex-end;
            align-items: center;

            .footerSpan {
                margin-left: 15px;
                background-color: rgb(255, 183, 1);
                color: #ffffff;
                width: 120px;
                height: 40px;
            }
        }

        .footerThree {
            display: flex;
            justify-content: space-around;
            margin-top: 50px;

            .footerFive {
                width: 150px;
                height: 80px;
                border-radius: 10px;
                background-color: beige;
                display: flex;
                justify-content: center;
                flex-direction: column;
                align-items: center;
                font-size: 18px;
                font-weight: 700;
            }
        }
    }
}

.headerOneColo {
    color: rgb(0, 0, 255);
}

.business {
    flex-wrap: wrap;
    display: flex;
    justify-content: space-around;
    margin-top: 50px;

    .businessOne {
        width: calc((100% - 300px) / 4);
        height: 80px;
        border-radius: 10px;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        font-size: 18px;
        font-weight: 700;
        margin-top: 10px;

        .businessTwo {
            background-color: beige;
            width: 150px;
            height: 80px;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            -radius: 10px;
        }
    }
}

.footerSpanOne {
    margin-left: 15px;
    background-color: rgb(33, 160, 250);
    color: #ffffff;
    width: 120px;
    height: 40px;
}

.footerSpanTwo {
    margin-left: 15px;
    background-color: rgb(108, 202, 201);
    color: #ffffff;
    width: 120px;
    height: 40px;
}

.footerSpanFour {
    margin-left: 15px;
    height: 40px;
}
</style>