<template>
	<div style="height: 100%" v-if="typeStyle == 1">
		<gaode-map
			style="height: 100% !important"
			ref="map"
			api-key="034c7fb6acbb91973d7f253c9e8dae4e"
			:point="point"
			:data="mapData"
			@goDetail="goDetail"
		/>
	</div>
	<a-card v-if="typeStyle == 2">
		<div style="color: #a7a7a7; font-size: 20px; opacity: 1">社区项目</div>
		<a-space wrap>
			<a-card v-for="item in listData">
				<div style="width: 350px">
					<div class="outsideContainerOne">
						<img
							class="outsideContainerTwo"
							:src="
								item.overlook
									? item.overlook
									: 'http://192.168.10.182:48003/prod-api/dev/file/download?id=1833399121465368576'
							"
							alt=""
						/>
						<div class="outsideContainerThree">
							<div>{{ item.name }}</div>
						</div>
						<div class="outsideContainerFour">
							<div style="display: flex; justify-content: space-around">
								<div style="text-align: center; color: aliceblue">
									<div>{{ $TOOL.dictTypeData('house_type_home', item.type) }}</div>
									<div>住宅类型</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>{{ item.totalArea ? item.totalArea : 0 }}㎡</div>
									<div>占地面积</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>{{ item.buildNum ? item.buildNum : 0 }}栋</div>
									<div>楼栋总数</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>{{ item.buildArea ? item.buildArea : 0 }}㎡</div>
									<div>建筑面积</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>{{ item.plotRatio ? item.plotRatio : 0 }}</div>
									<div>容积率</div>
								</div>
							</div>
						</div>
					</div>
					<div>
						<div class="tabBgc" style="display: flex; justify-content: space-around">
							<div class="th" style="width: 120px">类型</div>
							<div class="th">住宅</div>
							<div class="th">商业</div>
							<div class="th">车位</div>
							<div class="th">储藏室</div>
						</div>
						<div>
							<div
								style="display: flex; justify-content: space-around"
								class="table-c"
								:class="index % 2 !== 0 ? 'tabBgc' : ''"
								v-for="(items, index) in item.lastData"
							>
								<div class="th" style="width: 120px">{{ items.names }}</div>
								<div class="th">{{ items.totalHouse }}</div>
								<div class="th">{{ items.totalHouse2 }}</div>
								<div class="th">{{ items.totalHouse4 }}</div>
								<div class="th">{{ items.totalHouse3 }}</div>
							</div>
						</div>
					</div>
				</div>
			</a-card>
		</a-space>
		<div style="color: #a7a7a7; font-size: 20px; opacity: 1; margin: 12px 0px">园区项目</div>
		<a-space wrap>
			<a-card v-for="item in listData2">
				<div style="width: 350px">
					<div class="outsideContainerOne">
						<img
							class="outsideContainerTwo"
							:src="
								item[0].overlook
									? item[0].overlook
									: 'http://192.168.10.182:48003/prod-api/dev/file/download?id=1833399121465368576'
							"
							alt=""
						/>
						<div class="outsideContainerThree">
							<div>{{ item[0].name }}</div>
						</div>
						<div class="outsideContainerFour">
							<div style="display: flex; justify-content: space-around">
								<div style="text-align: center; color: aliceblue">
									<div>{{ $TOOL.dictTypeData('house_type_home', item[0].type) }}</div>
									<div>住宅类型</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>{{ item[0].totalArea ? item[0].totalArea : 0 }}㎡</div>
									<div>占地面积</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>{{ item[0].buildNum ? item[0].buildNum : 0 }}栋</div>
									<div>楼栋总数</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>{{ item[0].totalArea ? item[0].totalArea : 0 }}㎡</div>
									<div>建筑面积</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>{{ item[0].plotRatio ? item[0].plotRatio : 0 }}</div>
									<div>容积率</div>
								</div>
							</div>
						</div>
					</div>
					<div>
						<div class="tabBgc" style="display: flex; justify-content: space-around">
							<div class="th" style="width: 120px">类型</div>
							<div class="th">厂房</div>
							<div class="th">办公室</div>
							<div class="th">宿舍</div>
							<!-- <div class="th">储藏室</div> -->
						</div>
						<div>
							<div
								style="display: flex; justify-content: space-around"
								class="table-c"
								:class="index % 2 !== 0 ? 'tabBgc' : ''"
								v-for="(items, index) in item.lastData"
							>
								<div class="th" style="width: 120px">{{ items.names }}</div>
								<div class="th">{{ items.totalHouse3 }}</div>
								<div class="th">{{ items.totalHouse2 }}</div>
								<div class="th">{{ items.totalHouse }}</div>
							</div>
						</div>
					</div>
				</div>
			</a-card>
		</a-space>
	</a-card>
	<div class="typeStyle" v-if="checkTabBtn">
		<a-radio-group v-model:value="typeStyle" button-style="solid" @change="typeChange">
			<a-radio-button value="1">地图形式</a-radio-button>
			<a-radio-button value="2">列表形式</a-radio-button>
		</a-radio-group>
	</div>
	<positionModel
		:pointPosition="mapTable"
		:isTrue="isTrue"
		:num="num"
		:overlook="overlook"
		:detail="true"
		@zoom="zoom"
		:drag="true"
		ref="clickMap"
		@iconClick="iconClick"
		@close="positionClose"
	/>
</template>

<script setup name="indexHome">
	// import peojectDetailApi from '@/api/biz/peojectDetailApi.js'
	import moduleApi from '@/api/sys/resource/moduleApi'

	import GaodeMap from '@/components/Map/gaodeMap/projectMap.vue'
	import projectDetailApi from '@/api/biz/projectDetailApi'
	import parkDetailApi from '@/api/biz/parkDetailApi'
	import reBuildingApi from '@/api/biz/reBuildingApi'
	import reProjectApi from '@/api/biz/reProjectApi'
	import positionModel from '../sys/project/buildPostion.vue'

	import { P } from '@antv/g2plot'
	const houseList = ref()
	const mapTable = ref([]) //打点的坐标数组
	const isTrue = ref(false) //是否允许拖动
	const num = ref(1) //放大缩小倍数
	const overlook = ref('')
	const clickMap = ref()
	const checkTabBtn = ref(true)
	const listData = ref([])
	const listData2 = ref([])
	const point = ref([])
	const mapData = ref([])
	const modules = ref([])

	const typeStyle = ref('1')

	const getList = () => {
		projectDetailApi.peojectDetailPage({ withDetail: true }).then((res) => {
			let recordList = res.records
			recordList.forEach((el, index) => {
				el.tables = []
				let temp = {
					type1: {
						totalHouse: 0,
						totalArea: 0,
						soldHouse: 0,
						soldArea: 0,
						unsoldHouse: 0,
						unsoldArea: 0
					},
					type2: {
						totalHouse2: 0,
						totalArea2: 0,
						soldHouse2: 0,
						soldArea2: 0,
						unsoldHouse2: 0,
						unsoldArea2: 0
					},

					type3: {
						totalHouse3: 0,
						totalArea3: 0,
						soldHouse3: 0,
						soldArea3: 0,
						unsoldHouse3: 0,
						unsoldArea3: 0
					},
					type4: {
						totalHouse4: 0,
						totalArea4: 0,
						soldHouse4: 0,
						soldArea4: 0,
						unsoldHouse4: 0,
						unsoldArea4: 0
					}
				}
				el.projectDetails.forEach((ed) => {
					if (ed.type == 1) {
						temp.type1.totalHouse = ed.totalHouse ? ed.totalHouse : 0
						temp.type1.totalArea = ed.totalArea ? ed.totalArea : 0
						temp.type1.soldHouse = ed.soldHouse ? ed.soldHouse : 0
						temp.type1.soldArea = ed.soldArea ? ed.soldArea : 0
						temp.type1.unsoldHouse = ed.unsoldHouse ? ed.unsoldHouse : 0
						temp.type1.unsoldArea = ed.unsoldArea ? ed.unsoldArea : 0
					}
					if (ed.type == 2) {
						temp.type2.totalHouse2 = ed.totalHouse ? ed.totalHouse : 0
						temp.type2.totalArea2 = ed.totalArea ? ed.totalArea : 0
						temp.type2.soldHouse2 = ed.soldHouse ? ed.soldHouse : 0
						temp.type2.soldArea2 = ed.soldArea ? ed.soldArea : 0
						temp.type2.unsoldHouse2 = ed.unsoldHouse ? ed.unsoldHouse : 0
						temp.type2.unsoldArea2 = ed.unsoldArea ? ed.unsoldArea : 0
					}
					if (ed.type == 3) {
						temp.type3.totalHouse3 = ed.totalHouse ? ed.totalHouse : 0
						temp.type3.totalArea3 = ed.totalArea ? ed.totalArea : 0
						temp.type3.soldHouse3 = ed.soldHouse ? ed.soldHouse : 0
						temp.type3.soldArea3 = ed.soldArea ? ed.soldArea : 0
						temp.type3.unsoldHouse3 = ed.unsoldHouse ? ed.unsoldHouse : 0
						temp.type3.unsoldArea3 = ed.unsoldArea ? ed.unsoldArea : 0
					}
					if (ed.type == 4) {
						temp.type4.totalHouse4 = ed.totalHouse ? ed.totalHouse : 0
						temp.type4.totalArea4 = ed.totalArea ? ed.totalArea : 0
						temp.type4.soldHouse4 = ed.soldHouse ? ed.soldHouse : 0
						temp.type4.soldArea4 = ed.soldArea ? ed.soldArea : 0
						temp.type4.unsoldHouse4 = ed.unsoldHouse ? ed.unsoldHouse : 0
						temp.type4.unsoldArea4 = ed.unsoldArea ? ed.unsoldArea : 0
					}
				})
				el.tables.push(temp)
			})
			recordList.forEach((el) => {
				el.tables.forEach((es) => {
					el.lastData = [
						{
							names: '总量(套)',
							totalHouse: es.type1.totalHouse,
							totalHouse2: es.type2.totalHouse2,
							totalHouse3: es.type3.totalHouse3,
							totalHouse4: es.type4.totalHouse4
						},
						{
							names: '总面积(㎡)',
							totalHouse: es.type1.totalArea,
							totalHouse2: es.type2.totalArea2,
							totalHouse3: es.type3.totalArea3,
							totalHouse4: es.type4.totalArea4
						},
						{
							names: '已售量(套)',
							totalHouse: es.type1.soldHouse,
							totalHouse2: es.type2.soldHouse2,
							totalHouse3: es.type3.soldHouse3,
							totalHouse4: es.type4.soldHouse4
						},
						{
							names: '已售面积(㎡)',
							totalHouse: es.type1.soldArea,
							totalHouse2: es.type2.soldArea2,
							totalHouse3: es.type3.soldArea3,
							totalHouse4: es.type4.soldArea4
						},
						{
							names: '未售量(套)',
							totalHouse: es.type1.unsoldHouse,
							totalHouse2: es.type2.unsoldHouse2,
							totalHouse3: es.type3.unsoldHouse3,
							totalHouse4: es.type4.unsoldHouse4
						},
						{
							names: '未售面积(㎡)',
							totalHouse: es.type1.unsoldArea,
							totalHouse2: es.type2.unsoldArea2,
							totalHouse3: es.type3.unsoldArea3,
							totalHouse4: es.type4.unsoldArea4
						}
					]
				})
				if (el.category == modules.value[0].id) {
					listData.value.push(el)
				}
				if (el.type == 2) {
					// listData2.value.push(el)
				}
			})
		})
	}
	const getParkLIst = () => {
		parkDetailApi.parDetailPage().then((res) => {
			// listData2.value = res
			// console.log(res, "初始");
			let result = res
			let arr = []
			for (let a in result) {
				arr.push(result[a])
			}
			arr.forEach((el) => {
				el[0].tables = []
				let temp = {
					type1: {
						totalHouse: 0,
						totalArea: 0,
						rentHouse: 0,
						rentArea: 0,
						notRentHouse: 0,
						notRentArea: 0
					},
					type2: {
						totalHouse2: 0,
						totalArea2: 0,
						rentHouse2: 0,
						rentArea2: 0,
						notRentHouse2: 0,
						notRentArea2: 0
					},

					type3: {
						totalHouse3: 0,
						totalArea3: 0,
						rentHouse3: 0,
						rentArea3: 0,
						notRentHouse3: 0,
						notRentArea3: 0
					}
				}
				el[0].list.forEach((ed) => {
					if (ed.houseType == 'dormitory') {
						temp.type1.totalHouse = ed.totalHouse ? ed.totalHouse : 0
						temp.type1.totalArea = ed.totalArea ? ed.totalArea : 0
						temp.type1.rentHouse = ed.rentHouse ? ed.rentHouse : 0
						temp.type1.rentArea = ed.rentArea ? ed.rentArea : 0
						temp.type1.notRentHouse = ed.notRentHouse ? ed.notRentHouse : 0
						temp.type1.notRentArea = ed.notRentArea ? ed.notRentArea : 0
					}
					if (ed.houseType == 'officebuilding') {
						temp.type2.totalHouse2 = ed.totalHouse ? ed.totalHouse : 0
						temp.type2.totalArea2 = ed.totalArea ? ed.totalArea : 0
						temp.type2.rentHouse2 = ed.rentHouse ? ed.rentHouse : 0
						temp.type2.rentArea2 = ed.rentArea ? ed.rentArea : 0
						temp.type2.notRentHouse2 = ed.notRentHouse ? ed.notRentHouse : 0
						temp.type2.notRentArea2 = ed.notRentArea ? ed.notRentArea : 0
					}
					if (ed.houseType == 'workshop') {
						temp.type3.totalHouse3 = ed.totalHouse ? ed.totalHouse : 0
						temp.type3.totalArea3 = ed.totalArea ? ed.totalArea : 0
						temp.type3.rentHouse3 = ed.rentHouse ? ed.rentHouse : 0
						temp.type3.rentArea3 = ed.rentArea ? ed.rentArea : 0
						temp.type3.notRentHouse3 = ed.notRentHouse ? ed.notRentHouse : 0
						temp.type3.notRentArea3 = ed.notRentArea ? ed.notRentArea : 0
					}
				})

				el[0].tables.push(temp)
			})

			arr.forEach((el) => {
				el[0].tables.forEach((es) => {
					el.lastData = [
						{
							names: '总量(套)',
							totalHouse: es.type1.totalHouse,
							totalHouse2: es.type2.totalHouse2,
							totalHouse3: es.type3.totalHouse3
						},
						{
							names: '总面积(㎡)',
							totalHouse: es.type1.totalArea,
							totalHouse2: es.type2.totalArea2,
							totalHouse3: es.type3.totalArea3
						},
						{
							names: '已租量(套)',
							totalHouse: es.type1.rentHouse,
							totalHouse2: es.type2.rentHouse2,
							totalHouse3: es.type3.rentHouse3
						},
						{
							names: '已租面积(㎡)',
							totalHouse: es.type1.rentArea,
							totalHouse2: es.type2.rentArea2,
							totalHouse3: es.type3.rentArea3
						},
						{
							names: '未租量(套)',
							totalHouse: es.type1.notRentHouse,
							totalHouse2: es.type2.notRentHouse2,
							totalHouse3: es.type3.notRentHouse3
						}
					]
				})
				listData2.value.push(el)
			})
		})
	}

	const goDetail = (item) => {
		reBuildingApi.reBuildingPage({ projectId: item.id || item[0].id }).then((res) => {
			checkTabBtn.value = false
			mapTable.value = []
			res.records.forEach((el, index) => {
				if (!el.positionX) {
					el.positionX = 20
					el.positionY = index * 20
				}
				mapTable.value.push(el)
			})
			clickMap.value.onOpen(item)
			overlook.value = item.overlook
		})
	}

	const iconClick = (data) => {
		console.log(data, 'data')
	}

	const positionClose = () => {
		checkTabBtn.value = true
	}

	// 放大缩小
	const zoom = (value) => {
		if (value == '放大') {
			num.value += 0.1
		} else {
			num.value -= 0.1
		}
	}

	const typeChange = () => {
		if (typeStyle.value == 2) {
			listData.value = []
			listData2.value = []
			getList()
			getParkLIst()
		}
	}
	const loadModule = (parameter) => {
		moduleApi.modulePage2(parameter).then((res) => {
			res.records.splice(0, 1)
			modules.value = res.records
		})
	}
	onMounted(() => {
		loadModule()
		// getList()
		// getParkLIst()
	})
</script>
<style lang="scss" scoped>
	.outsideContainerOne {
		width: 350px;
		height: 209px;
		position: relative;
		display: flex;
		justify-content: space-between;
		flex-wrap: nowrap;

		.outsideContainerTwo {
			width: 100%;
			height: 100%;
			object-fit: cover;
			position: absolute;
			top: 0;
			left: 0;
			z-index: 1;
		}

		.outsideContainerThree {
			background-color: rgba(0, 0, 0, 0.5);
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			padding: 10px;
			box-sizing: border-box;
			z-index: 2;
			display: flex;
			justify-content: center;
			color: aliceblue;
		}

		.outsideContainerFour {
			background-color: rgba(0, 0, 0, 0.5);
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			padding: 10px;
			box-sizing: border-box;
			z-index: 2;
		}
	}
	.th {
		width: 80px;
		line-height: 32px;
		text-align: center;
	}

	.tabBgc {
		background-color: #eff6ff !important;
	}

	.typeStyle {
		position: fixed;
		left: 1%;
		bottom: 6%;
		z-index: 9;
	}
</style>
