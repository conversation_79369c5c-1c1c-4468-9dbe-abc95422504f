<template>
	<a-row :gutter="10" class="mb-2">
		<a-col :span="16">
			<a-card :bordered="false" title="周统计">
				<lineChart ref="lineChartRef" />
			</a-card>
		</a-col>
		<a-col :span="8">
			<a-card :bordered="false" title="总比例">
				<pieChart ref="pieChartRef" />
			</a-card>
		</a-col>
	</a-row>

	<a-card :bordered="false">
		<s-table ref="tableRef" :columns="columns" :data="loadData" bordered :row-key="(record) => record.id">
			<template #operator class="table-operator">
				<a-form ref="formRef" name="advanced_search" :model="searchFormState" class="ant-advanced-search-form">
					<a-space>
						<a-radio-group v-model:value="visLogType" button-style="solid">
							<a-radio-button v-for="visLog in visLogTypeList" :key="visLog.value" :value="visLog.value"
								@click="visLogTypeClock(visLog.value)">
								{{ visLog.label }}
							</a-radio-button>
						</a-radio-group>

						<a-input-search v-model:value="searchFormState.searchKey" placeholder="请输入名称关键词" enter-button
							allowClear @search="onSearch" />
							<a-input-search v-model:value="searchFormState.opUser" placeholder="请输入名称关键词" enter-button
							allowClear @search="onSearchName" />
						<a-range-picker v-model:value="times" @change="sureTiem" />

						<a-popconfirm title="确定清空登录登出日志吗？" @confirm="deleteBatchVisLog()">
							<a-button danger v-if="hasPerm('clearAllAccessInfo')">清空</a-button>
						</a-popconfirm>
					</a-space>
				</a-form>
			</template>
			<template #bodyCell="{ column, record }">
				<template v-if="column.dataIndex === 'action'">
					<a-space>
						<a @click="detailRef.onOpen(record)">详情</a>
					</a-space>
				</template>
			</template>
		</s-table>
	</a-card>
	<detail ref="detailRef" />
</template>

<script setup name="devVislog">
import logApi from '@/api/dev/logApi'
import LineChart from './lineChart.vue'
import PieChart from './pieChart.vue'
import Detail from './detail.vue'
const searchFormState = ref({})
const times = ref([])
const formRef = ref()
const tableRef = ref()
const detailRef = ref()
const lineChartRef = ref()
const pieChartRef = ref()

const visLogType = ref('LOGIN')
const visLogTypeList = ref([
	{
		label: '登录日志',
		value: 'LOGIN'
	},
	{
		label: '登出日志',
		value: 'LOGOUT'
	}
])
const columns = [
	{
		title: '名称',
		dataIndex: 'name'
	},
	{
		title: 'IP地址',
		dataIndex: 'opIp'
	},
	{
		title: '地址',
		dataIndex: 'opAddress'
	},
	{
		title: '浏览器',
		dataIndex: 'opBrowser'
	},
	{
		title: '设备',
		dataIndex: 'opOs'
	},
	{
		title: '时间',
		dataIndex: 'opTime',
		sorter: true
	},
	{
		title: '用户',
		dataIndex: 'opUser'
	},
	{
		title: '操作',
		dataIndex: 'action',
		align: 'center',
		width: '100px'
	}
]
// 切换应用标签查询
const visLogTypeClock = (value) => {
	searchFormState.value.category = value
	tableRef.value.refresh(true)
}
// 查询
const onSearch = () => {
	if (searchFormState.value.searchKey) {
		tableRef.value.refresh(true)
	} else {
		delete searchFormState.value.beginTime
		delete searchFormState.value.endTime
		delete searchFormState.value.searchKey
		delete searchFormState.value.opUser
		times.value = []
		tableRef.value.refresh(true)
	}
}
//用户名称查询
const onSearchName = () => {
	if (searchFormState.value.opUser) {
		tableRef.value.refresh(true)
	} else {
		delete searchFormState.value.beginTime
		delete searchFormState.value.endTime
		delete searchFormState.value.searchKey
		delete searchFormState.value.opUser
		times.value = []
		tableRef.value.refresh(true)
	}
}
const loadData = (parameter) => {
	if (searchFormState.value.beginTime && searchFormState.value.searchKey && searchFormState.value.opUser) {
		return logApi.logPage(Object.assign(parameter, searchFormState.value)).then((data) => {
			return data
		})
	}
	if (!searchFormState.value.beginTime) {
		searchFormState.value.beginTime = getToday()
	}
	if (searchFormState.value.searchKey) {
		delete searchFormState.value.beginTime
		delete searchFormState.value.endTime
		delete searchFormState.value.opUser
	}
	if (searchFormState.value.opUser) {
		delete searchFormState.value.beginTime
		delete searchFormState.value.endTime
		delete searchFormState.value.searchKey
	}
	searchFormState.value.sortOrder = 'descend'
	searchFormState.value.sortField = 'opTime'
	searchFormState.value.category = searchFormState.value.category ? searchFormState.value.category : visLogType.value
	return logApi.logPage(Object.assign(parameter, searchFormState.value)).then((data) => {
		delete searchFormState.value.beginTime
		delete searchFormState.value.endTime
		return data
	})
}
const sureTiem = (date) => {
	if (date) {
		searchFormState.value.beginTime = getTime(date[0].$d).starTime
		searchFormState.value.endTime = getTime(date[1].$d).formattedDate
		console.log(getTime(date[0].$d).starTime, "getTime(date[0].$d).starTime")
		console.log(getTime(date[1].$d).formattedDate, "getTime(date[1].$d).formattedDate");
		tableRef.value.refresh(true)
	}else{
		delete searchFormState.value.searchKey
		delete searchFormState.value.beginTime
		delete searchFormState.value.endTime
		delete searchFormState.value.opUser
		tableRef.value.refresh(true)
	}


}
//将选取的时间进行格式化
const getTime = (time) => {
	const originalDate = new Date(time);
	const year = originalDate.getFullYear();
	const month = String(originalDate.getMonth() + 1).padStart(2, '0'); // 月份是0-11  
	const day = String(originalDate.getDate()).padStart(2, '0');
	const hours = String(originalDate.getHours()).padStart(2, '0');
	const minutes = String(originalDate.getMinutes()).padStart(2, '0');
	const seconds = String(originalDate.getSeconds()).padStart(2, '0');
	const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
	const starTime = `${year}-${month}-${day} 00:00:00`;
	return { formattedDate, starTime }
}
//获取今日时间
const getToday = () => {
	const currentDate = new Date();
	const year = currentDate.getFullYear();
	const month = String(currentDate.getMonth() + 1).padStart(2, '0');
	const day = String(currentDate.getDate()).padStart(2, '0');
	// 开始时间
	const start = `${year}-${month}-${day} 00:00:00`;
	// 结束时间
	const end = `${year}-${month}-${day} 23:59:59`;
	return `${year}-${month}-${day}`;
}
// 清空
const deleteBatchVisLog = () => {
	const param = {
		category: searchFormState.value.category ? searchFormState.value.category : visLogType.value
	}
	logApi.logDelete(param).then(() => {
		tableRef.value.refresh(true)
	})
}
</script>
