<template>
	<div class="custom-list" @scroll="onScroll">
		<a-spin :spinning="loading" size="large" v-if="loading" />

		<div v-if="pullDownRefresh" class="refresh-indicator">
			<span v-if="refreshing">刷新中...</span>
			<span v-else-if="canRefresh">下拉刷新</span>
			<span v-else>释放刷新</span>
		</div>

		<slot></slot>

		<!-- <div v-if="noMore" class="no-more-tip">{{ finishedText }}</div> -->
	</div>
</template>

<script setup>
	import { ref, watch, onMounted } from 'vue'
	import { defineEmits, defineProps } from 'vue'
	import { Spin } from 'ant-design-vue'

	const emit = defineEmits(['load', 'refresh'])
	const props = defineProps({
		immediateCheck: {
			type: Boolean,
			default: false
		},
		finished: {
			type: Boolean,
			default: false
		},
		finishedText: {
			type: String,
			default: '没有更多数据了'
		},
		loading: {
			type: Boolean,
			default: false
		},
		loadingText: {
			type: String,
			default: '加载中...'
		}
	})

	const refreshing = ref(false)
	const canRefresh = ref(true)
	const pullDownRefresh = ref(false)
	const noMore = ref(props.finished)
	const scrollTop = ref(0)
	const lastScrollTop = ref(0)
	const threshold = 50
	const timer = ref(null)

	const onScroll = (event) => {
		const { scrollTop: currentScrollTop, scrollHeight, clientHeight } = event.target
		scrollTop.value = currentScrollTop

		// 下拉刷新
		if (currentScrollTop < threshold && !refreshing.value) {
			canRefresh.value = true
		} else {
			canRefresh.value = false
		}

		// 上拉加载更多
		if (currentScrollTop + clientHeight >= scrollHeight - 5) {
			if (!noMore.value) {
				emit('load')
			}
		}

		clearTimeout(timer.value)
		timer.value = setTimeout(() => {
			pullDownRefresh.value = false
			canRefresh.value = false
		}, 1000)
	}

	const refresh = (done) => {
		refreshing.value = true
		emit('refresh', () => {
			setTimeout(() => {
				refreshing.value = false
				pullDownRefresh.value = false
				done()
			}, 1000)
		})
	}

	watch(
		() => props.loading,
		(newVal) => {
			loading.value = newVal
		}
	)

	watch(
		() => props.finished,
		(newVal) => {
			noMore.value = newVal
		}
	)

	onMounted(() => {
		// 初始化时执行一些操作
	})
</script>

<style scoped>
	.custom-list {
		height: 95%;
		overflow-y: auto;

		&::-webkit-scrollbar {
			width: 4px;
		}
		&::-webkit-scrollbar-thumb {
			background-color: #888;
			border-radius: 10px;
			opacity: 0.5;
		}
	}

	.refresh-indicator {
		position: relative;
		top: -40px;
		background-color: #fff;
		padding: 10px;
		text-align: center;
	}

	.refresh-indicator span {
		display: block;
	}

	.no-more-tip {
		text-align: center;
		padding: 10px;
		position: absolute;
		bottom: 0;
		width: 100%;
		background-color: #fff;
	}
</style>
