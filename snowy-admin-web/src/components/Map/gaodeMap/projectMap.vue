<template>
	<div class="gaodeMap" :style="{ height: `${height}px` }">
		<div :id="`container-${mid}`" class="xn-wh">地图资源加载中...</div>
	</div>
</template>
<!--AMap官网：https://lbs.amap.com/api/javascript-api-v2/summary-->
<script setup name="GaodeMap">
	import { onMounted, onUnmounted, shallowRef } from 'vue'
	import AMapLoader from '@amap/amap-jsapi-loader'
	import projectDetailApi from '@/api/biz/projectDetailApi'
	import parkDetailApi from '@/api/biz/parkDetailApi'
	import moduleApi from '@/api/sys/resource/moduleApi'
import { center } from '@antv/g2plot/lib/plots/sankey/sankey'

	const marker = ref(null)
	const listData = ref([])
	const listData2 = ref([])
	const markerData = ref({})
	const modules = ref([])
	const props = defineProps({
		mid: {
			type: Number,
			default: new Date().getTime()
		},
		height: {
			type: Number,
			default: 800
		},
		apiKey: {
			type: String,
			required: true
		},
		center: {
			type: Array
		},
		point: {
			type: Array,
			default: []
		},
		data: {
			type: Array,
			default: []
		},
		plugins: {
			type: Array,
			// eslint-disable-next-line vue/require-valid-default-prop
			default: ['AMap.ToolBar', 'AMap.Scale', 'AMap.HawkEye', 'AMap.MapType', 'AMap.Geolocation', 'AMap.MarkerCluster']
		},
		viewMode: {
			type: String,
			default: '3D',
			validator(value) {
				return ['2D', '3D'].includes(value)
			}
		},
		zoom: {
			type: Number,
			default: 12
		},
		pitch: {
			type: Number,
			default: 50
		},
		mapStyle: {
			type: String,
			default: 'normal',
			validator(value) {
				return ['normal', 'macaron', 'dark', 'fresh', 'grey'].includes(value)
			}
		},
		markerCluster: {
			type: Boolean,
			default: true
		}
	})

	const emits = defineEmits(['complete', 'markerClick', 'map-click', 'goDetail'])
	const gaodeMap = shallowRef(null)
	const gaodeMapMarkerArr = ref([])
	const gaodeMapInfoWindowObj = ref({})

	const init = () => {
		AMapLoader.load({
			key: props.apiKey,
			version: '2.0',
			plugins: props.plugins,
			AMapUI: {
				version: '1.1',
				plugins: ['overlay/SimpleMarker', 'overlay/AwesomeMarker']
			}
		})
			.then(() => {
				initMap()
			})
			.catch((e) => {
				console.error(e)
			})
	}

	// 初始化 地图
	const initMap = () => {
		gaodeMap.value = new AMap.Map(`container-${props.mid}`, {
			viewMode: props.viewMode,
			zoom: 15,
			// 地图俯仰角度
			pitch: 0,
			mapStyle: `amap://styles/${props.mapStyle}`,
			center:[114.06413, 35.290401]
		})

		// 中心点
		// if (props.center) {
		// }

		// 控件
		props.plugins.length > 0 && initControlPlugin()

		// 地图初始化完成
		gaodeMap.value.on('complete', () => {
			// gaodeMap.value.setCenter([114.051213, 35.293401])
			getList()
			getParkLIst()
		})
	}

	//加载marker点位
	// var markerContent =
	// 		'' +
	// 		'<div class="custom-content-marker">' +
	// 		'   <img src="//a.amap.com/jsapi_demos/static/demo-center/icons/poi-marker-red.png">' +
	// 		'   <div class="close-btn">12</div>' +
	// 		'</div>'
	function initMarker(lng, lat, el) {
		if (marker.value) {
			gaodeMap.value.remove(marker.value)
		}
		let markers = new AMap.Marker({
			//点位经纬度坐标
			position: [lng, lat],
			//此处若不传则是官方默认的点位图标，也可以替换成你的点位图标url
			icon: '',
			// content: markerContent,
			//若设置此属性，则鼠标滑过点位时将显示此信息，不设置则不会显示 title
			zIndex: 5,
			//自定义属性，属性名可替换成其他，只要是官方文档中没有的即可
			id: el.id
		})
		markers.setLabel({
			offset: new AMap.Pixel(0, -35), //设置文本标注偏移量
			content: el.name || el[0].name, //设置文本标注内容
			direction: 'center' //设置文本标注方位
		})
		let ele = el

		markers.on('click', function () {
			const infoWindow = new AMap.InfoWindow({
				content: createPopupContent(ele),
				offset: new AMap.Pixel(0, -30),
				closeWhenClickMap: true
			})
			infoWindow.open(gaodeMap.value, markers.getPosition())
			markerData.value = el.id ? el : el[0]
			if (el.id) {
				markerData.value.projectType = 1
			} else {
				markerData.value.projectType = 2
			}
		})
		//将点标记添加到地图中
		gaodeMap.value.add(markers)
		// gaodeMap.value.setCenter([lng, lat])
	}

	const createPopupContent = (item) => {
		let cont1 = ``
		let cont2 = ``
		let htmlText1 = ``
		let htmlText2 = ``

		item.lastData.forEach((el, index) => {
			cont1 += `<div
								style="display: flex; justify-content: space-around"
								class="table-c"
								class="${index % 2 !== 0 ? 'tabBgc' : ''}"
								v-for="(items, index) in item.lastData"
							>
								<div class="th" style="width: 120px">${el.names}</div>
								<div class="th">${el.totalHouse}</div>
								<div class="th">${el.totalHouse2}</div>
								<div class="th">${el.totalHouse4}</div>
								<div class="th">${el.totalHouse3}</div>
							</div>`
			cont2 += `<div style="display: flex; justify-content: space-around" class="table-c"
								:class="index % 2 !== 0 ? 'tabBgc' : ''" v-for="(items, index) in item.lastData">
								<div class="th" style="width: 120px">${el.names}</div>
								<div class="th">${el.totalHouse}</div>
								<div class="th">${el.totalHouse2}</div>
								<div class="th">${el.totalHouse3}</div>
							</div>`
		})
		if (item.id) {
			htmlText1 = `
	       <div style="width: 350px">
					<div class="outsideContainerOne">
						<img
							class="outsideContainerTwo"
							src="${item.overlook}"
							alt=""
						/>
						<div class="outsideContainerThree">
							<div>${item.name}</div>
						</div>
						<div class="outsideContainerFour">
							<div style="display: flex; justify-content: space-around">
								<div style="text-align: center; color: aliceblue">
								    <div>${item.type == 1 ? '住宅' : ''}</div>
								    <div>${item.type == 2 ? '商业' : ''}</div>
								    <div>${item.type == 3 ? '储藏间' : ''}</div>
								    <div>${item.type == 4 ? '车位' : ''}</div>
									<div>住宅类型</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>${item.totalArea ? item.totalArea : 0}㎡</div>
									<div>占地面积</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>${item.buildNum ? item.buildNum : 0}栋</div>
									<div>楼栋总数</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>${item.buildArea ? item.buildArea : 0}㎡</div>
									<div>建筑面积</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>${item.plotRatio ? item.plotRatio : 0}</div>
									<div>容积率</div>
								</div>
							</div>
						</div>
					</div>
					<div>
						<div class="tabBgc" style="display: flex; justify-content: space-around">
							<div class="th" style="width: 120px">类型</div>
							<div class="th">住宅</div>
							<div class="th">商业</div>
							<div class="th">车位</div>
							<div class="th">储藏室</div>
						</div>
						<div>
	                           ${cont1}
						</div>
					</div>
	                   <div class="detail-btn" onclick="goDetail()">查看详情</div>
				</div>
	      `
		} else {
			htmlText2 = `<div style="width: 350px">
					<div class="outsideContainerOne">
						<img class="outsideContainerTwo" src="${item[0].overlook}" alt="" />
						<div class="outsideContainerThree">
							<div>${item[0].name}</div>
						</div>
						<div class="outsideContainerFour">
							<div style="display: flex; justify-content: space-around">
								<div style="text-align: center; color: aliceblue">
									 <div>${item[0].type == 1 ? '厂房' : ''}</div>
								    <div>${item[0].type == 2 ? '办公用房' : ''}</div>
								    <div>${item[0].type == 3 ? '宿舍楼' : ''}</div>
									<div>类型</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>${item[0].totalArea ? item[0].totalArea : 0}㎡</div>
									<div>占地面积</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>${item[0].buildNum ? item[0].buildNum : 0}栋</div>
									<div>楼栋总数</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>${item[0].totalArea ? item[0].totalArea : 0}㎡</div>
									<div>建筑面积</div>
								</div>
								<div style="text-align: center; color: aliceblue">
									<div>${item[0].plotRatio ? item[0].plotRatio : 0}</div>
									<div>容积率</div>
								</div>
							</div>
						</div>
					</div>
					<div>
						<div class="tabBgc" style="display: flex; justify-content: space-around">
							<div class="th" style="width: 120px">类型</div>
							<div class="th">厂房</div>
							<div class="th">办公室</div>
							<div class="th">宿舍</div>
							<!-- <div class="th">储藏室</div> -->
						</div>
						<div>
							${cont2}
						</div>
					</div>
					<div class="detail-btn" onclick="goDetail()">查看详情</div>
				</div>`
		}

		if (item.id) {
			return htmlText1
		} else {
			return htmlText2
		}
	}

	const handleBreak = () => {
		emits('goDetail', markerData.value)
	}

	// 初始化 控制控件
	const initControlPlugin = () => {
		// 工具条，控制地图的缩放、平移等
		props.plugins.includes('AMap.ToolBar') && gaodeMap.value.addControl(new AMap.ToolBar({}))
		// 比例尺
		props.plugins.includes('AMap.Scale') && gaodeMap.value.addControl(new AMap.Scale())
		// 鹰眼，显示缩略图
		props.plugins.includes('AMap.HawkEye') && gaodeMap.value.addControl(new AMap.HawkEye({ isOpen: true }))
		// 图层切换
		props.plugins.includes('AMap.MapType') && gaodeMap.value.addControl(new AMap.MapType({}))
		// 定位
		props.plugins.includes('AMap.Geolocation') && gaodeMap.value.addControl(new AMap.Geolocation({}))
	}

	// 渲染 点标记
	const renderMarker = (dataArr) => {
		dataArr.forEach((d) => {
			const marker = new AMap.Marker({
				map: gaodeMap.value,
				position: d.position,
				// 鼠标滑过点标记时的文字提示
				title: d.title,
				// 显示内容：content有效时，icon属性将被覆盖
				content: d.content,
				// 图标
				icon: d.icon ? d.icon : null,
				// 文本标注
				label: d.label
			})
			marker.on('click', () => {
				emits('markerClick', d.position)
			})
			gaodeMapMarkerArr.value.push(marker)
		})

		setFitView()
	}

	// 渲染 圆点标记
	const renderCircleMarker = (dataArr) => {
		dataArr.forEach((d) => {
			const marker = new AMap.CircleMarker({
				map: gaodeMap.value,
				// 圆心位置
				center: d.position,
				// 圆点半径
				radius: d.radius ? d.radius : 20,
				// 线条颜色
				strokeColor: d.strokeColor ? d.strokeColor : '#006600',
				// 轮廓线透明度
				strokeOpacity: 0.5,
				// 轮廓线宽度
				strokeWeight: 2,
				// 填充颜色
				fillColor: d.fillColor ? d.fillColor : '#006600',
				// 填充透明度
				fillOpacity: 0.5,
				cursor: 'pointer'
			})
			marker.on('click', () => {
				emits('markerClick', d.position)
			})
			gaodeMapMarkerArr.value.push(marker)
		})

		setFitView()
	}

	// 渲染 简单点标记
	const renderSimpleMarker = (dataArr, theme = 'default') => {
		dataArr.forEach((d) => {
			const marker = new AMapUI.SimpleMarker({
				map: gaodeMap.value,
				position: d.position,
				// 前景文字
				iconLabel: {
					// 文本
					innerHTML: d.label,
					// 字体的样式，比如颜色，大小等
					style: d.labelStyle
						? d.labelStyle
						: {
								color: '#333',
								fontSize: '12px'
						  }
				},
				// 图标主题：default，fresh，numv1，numv2
				iconTheme: theme,
				// 背景图标样式
				iconStyle: d.style
			})
			marker.on('click', () => {
				emits('markerClick', d.position)
			})
			gaodeMapMarkerArr.value.push(marker)
		})

		setFitView()
	}

	// 渲染 字体点标记
	const renderAwesomeMarker = (dataArr) => {
		dataArr.forEach((d) => {
			const marker = new AMapUI.AwesomeMarker({
				map: gaodeMap.value,
				position: d.position,
				// 图标，参见：http://fontawesome.io/icons/
				awesomeIcon: d.awesomeIcon,
				// 字体的样式，比如颜色，大小等
				iconLabel: {
					style: d.labelStyle
						? d.labelStyle
						: {
								color: '#333',
								fontSize: '12px'
						  }
				},
				// 背景图标的样式
				iconStyle: d.style
			})
			marker.on('click', () => {
				emits('markerClick', d.position)
			})
			gaodeMapMarkerArr.value.push(marker)
		})

		setFitView()
	}

	// 设置 视图级别
	const setFitView = () => {
		// 点聚合
		props.markerCluster && new AMap.MarkerCluster(gaodeMap.value, gaodeMapMarkerArr.value)

		// 根据地图上添加的覆盖物分布情况，自动缩放地图到合适的视野级别
		gaodeMap.value.setFitView(gaodeMapMarkerArr.value)
	}

	// 渲染 线
	const renderPolyline = (dataArr, option = {}) => {
		const path = []
		dataArr.forEach((d) => {
			path.push(new AMap.LngLat(d.position[0], d.position[1]))
		})

		const polyline = new AMap.Polyline({
			path: path,
			strokeColor: option.strokeColor || 'blue',
			strokeWeight: option.strokeWeight || 2,
			strokeOpacity: option.strokeOpacity || 0.5,
			isOutline: option.isOutline || false,
			borderWeight: option.borderWeight || 1,
			// 折线拐点连接处样式
			lineJoin: 'round'
		})
		gaodeMap.value.add(polyline)

		gaodeMap.value.setFitView([polyline])
	}

	// 渲染 圆
	const renderCircle = (position, radius, option) => {
		const circle = new AMap.Circle({
			center: new AMap.LngLat(position[0], position[1]),
			radius: radius,
			strokeColor: option.strokeColor || 'blue',
			strokeWeight: option.strokeWeight || 2,
			strokeOpacity: option.strokeOpacity || 0.5,
			fillColor: option.fillColor || 'blue',
			fillOpacity: option.fillOpacity || 0.5,
			strokeStyle: 'solid'
		})
		gaodeMap.value.add(circle)

		gaodeMap.value.setFitView([circle])
	}

	// 渲染 面
	const renderPolygon = (dataArr, option = {}) => {
		const path = []
		dataArr.forEach((d) => {
			path.push(new AMap.LngLat(d.position[0], d.position[1]))
		})

		const polygon = new AMap.Polygon({
			path: path,
			strokeColor: option.strokeColor || 'blue',
			strokeWeight: option.strokeWeight || 2,
			strokeOpacity: option.strokeOpacity || 0.5,
			fillColor: option.fillColor || 'blue',
			fillOpacity: option.fillOpacity || 0.5,
			strokeStyle: 'solid'
		})
		gaodeMap.value.add(polygon)

		gaodeMap.value.setFitView([polygon])
	}

	// 渲染 信息窗体
	const renderInfoWindow = (dataArr) => {
		dataArr.forEach((d) => {
			gaodeMapInfoWindowObj.value[d.position] = new AMap.InfoWindow({
				// 显示内容
				content: d.content.join('<br>'),
				// 位置偏移量
				offset: new AMap.Pixel(0, -20),
				// 点击地图后关闭信息窗体
				closeWhenClickMap: true
			})
		})
	}

	// 打开 信息窗体
	const openInfoWindow = (position) => {
		const infoWindow = gaodeMapInfoWindowObj.value[position]
		if (infoWindow) {
			infoWindow.open(gaodeMap.value, position)
		}
	}

	// 清理 覆盖物
	const clearOverlay = () => {
		gaodeMap.value.clearMap()
	}

	const getList = () => {
		projectDetailApi.peojectDetailPage({ withDetail: true }).then((res) => {
			let recordList = res.records
			recordList.forEach((el, index) => {
				el.tables = []
				let temp = {
					type1: {
						totalHouse: 0,
						totalArea: 0,
						soldHouse: 0,
						soldArea: 0,
						unsoldHouse: 0,
						unsoldArea: 0
					},
					type2: {
						totalHouse2: 0,
						totalArea2: 0,
						soldHouse2: 0,
						soldArea2: 0,
						unsoldHouse2: 0,
						unsoldArea2: 0
					},

					type3: {
						totalHouse3: 0,
						totalArea3: 0,
						soldHouse3: 0,
						soldArea3: 0,
						unsoldHouse3: 0,
						unsoldArea3: 0
					},
					type4: {
						totalHouse4: 0,
						totalArea4: 0,
						soldHouse4: 0,
						soldArea4: 0,
						unsoldHouse4: 0,
						unsoldArea4: 0
					}
				}
				el.projectDetails.forEach((ed) => {
					if (ed.type == 1) {
						temp.type1.totalHouse = ed.totalHouse ? ed.totalHouse : 0
						temp.type1.totalArea = ed.totalArea ? ed.totalArea : 0
						temp.type1.soldHouse = ed.soldHouse ? ed.soldHouse : 0
						temp.type1.soldArea = ed.soldArea ? ed.soldArea : 0
						temp.type1.unsoldHouse = ed.unsoldHouse ? ed.unsoldHouse : 0
						temp.type1.unsoldArea = ed.unsoldArea ? ed.unsoldArea : 0
					}
					if (ed.type == 2) {
						temp.type2.totalHouse2 = ed.totalHouse ? ed.totalHouse : 0
						temp.type2.totalArea2 = ed.totalArea ? ed.totalArea : 0
						temp.type2.soldHouse2 = ed.soldHouse ? ed.soldHouse : 0
						temp.type2.soldArea2 = ed.soldArea ? ed.soldArea : 0
						temp.type2.unsoldHouse2 = ed.unsoldHouse ? ed.unsoldHouse : 0
						temp.type2.unsoldArea2 = ed.unsoldArea ? ed.unsoldArea : 0
					}
					if (ed.type == 3) {
						temp.type3.totalHouse3 = ed.totalHouse ? ed.totalHouse : 0
						temp.type3.totalArea3 = ed.totalArea ? ed.totalArea : 0
						temp.type3.soldHouse3 = ed.soldHouse ? ed.soldHouse : 0
						temp.type3.soldArea3 = ed.soldArea ? ed.soldArea : 0
						temp.type3.unsoldHouse3 = ed.unsoldHouse ? ed.unsoldHouse : 0
						temp.type3.unsoldArea3 = ed.unsoldArea ? ed.unsoldArea : 0
					}
					if (ed.type == 4) {
						temp.type4.totalHouse4 = ed.totalHouse ? ed.totalHouse : 0
						temp.type4.totalArea4 = ed.totalArea ? ed.totalArea : 0
						temp.type4.soldHouse4 = ed.soldHouse ? ed.soldHouse : 0
						temp.type4.soldArea4 = ed.soldArea ? ed.soldArea : 0
						temp.type4.unsoldHouse4 = ed.unsoldHouse ? ed.unsoldHouse : 0
						temp.type4.unsoldArea4 = ed.unsoldArea ? ed.unsoldArea : 0
					}
				})
				el.tables.push(temp)
			})
			recordList.forEach((el) => {
				el.tables.forEach((es) => {
					el.lastData = [
						{
							names: '总量(套)',
							totalHouse: es.type1.totalHouse,
							totalHouse2: es.type2.totalHouse2,
							totalHouse3: es.type3.totalHouse3,
							totalHouse4: es.type4.totalHouse4
						},
						{
							names: '总面积(㎡)',
							totalHouse: es.type1.totalArea,
							totalHouse2: es.type2.totalArea2,
							totalHouse3: es.type3.totalArea3,
							totalHouse4: es.type4.totalArea4
						},
						{
							names: '已售量(套)',
							totalHouse: es.type1.soldHouse,
							totalHouse2: es.type2.soldHouse2,
							totalHouse3: es.type3.soldHouse3,
							totalHouse4: es.type4.soldHouse4
						},
						{
							names: '已售面积(㎡)',
							totalHouse: es.type1.soldArea,
							totalHouse2: es.type2.soldArea2,
							totalHouse3: es.type3.soldArea3,
							totalHouse4: es.type4.soldArea4
						},
						{
							names: '未售量(套)',
							totalHouse: es.type1.unsoldHouse,
							totalHouse2: es.type2.unsoldHouse2,
							totalHouse3: es.type3.unsoldHouse3,
							totalHouse4: es.type4.unsoldHouse4
						},
						{
							names: '未售面积(㎡)',
							totalHouse: es.type1.unsoldArea,
							totalHouse2: es.type2.unsoldArea2,
							totalHouse3: es.type3.unsoldArea3,
							totalHouse4: es.type4.unsoldArea4
						}
					]
				})

				modules.value.forEach((mo) => {
					if (el.category == mo.id) {
						if (mo.title == '社区') {
							listData.value.push(el)
						}
					}
				})
			})
			setTimeout(() => {
				if (listData.value.length > 0) {
					listData.value.forEach((el) => {
						if (el.lonlat) {
							let itemLonlat = el.lonlat.split(',')
							initMarker(itemLonlat[0], itemLonlat[1], el)
						}
					})
				}
			}, 10)
		})
	}

	const getParkLIst = () => {
		parkDetailApi.parDetailPage().then((res) => {
			// listData2.value = res
			// console.log(res, "初始");
			let result = res
			let arr = []
			for (let a in result) {
				arr.push(result[a])
			}
			arr.forEach((el) => {
				el[0].tables = []
				let temp = {
					type1: {
						totalHouse: 0,
						totalArea: 0,
						rentHouse: 0,
						rentArea: 0,
						notRentHouse: 0,
						notRentArea: 0
					},
					type2: {
						totalHouse2: 0,
						totalArea2: 0,
						rentHouse2: 0,
						rentArea2: 0,
						notRentHouse2: 0,
						notRentArea2: 0
					},

					type3: {
						totalHouse3: 0,
						totalArea3: 0,
						rentHouse3: 0,
						rentArea3: 0,
						notRentHouse3: 0,
						notRentArea3: 0
					}
				}
				el[0].list.forEach((ed) => {
					if (ed.houseType == 'dormitory') {
						temp.type1.totalHouse = ed.totalHouse ? ed.totalHouse : 0
						temp.type1.totalArea = ed.totalArea ? ed.totalArea : 0
						temp.type1.rentHouse = ed.rentHouse ? ed.rentHouse : 0
						temp.type1.rentArea = ed.rentArea ? ed.rentArea : 0
						temp.type1.notRentHouse = ed.notRentHouse ? ed.notRentHouse : 0
						temp.type1.notRentArea = ed.notRentArea ? ed.notRentArea : 0
					}
					if (ed.houseType == 'officebuilding') {
						temp.type2.totalHouse2 = ed.totalHouse ? ed.totalHouse : 0
						temp.type2.totalArea2 = ed.totalArea ? ed.totalArea : 0
						temp.type2.rentHouse2 = ed.rentHouse ? ed.rentHouse : 0
						temp.type2.rentArea2 = ed.rentArea ? ed.rentArea : 0
						temp.type2.notRentHouse2 = ed.notRentHouse ? ed.notRentHouse : 0
						temp.type2.notRentArea2 = ed.notRentArea ? ed.notRentArea : 0
					}
					if (ed.houseType == 'workshop') {
						temp.type3.totalHouse3 = ed.totalHouse ? ed.totalHouse : 0
						temp.type3.totalArea3 = ed.totalArea ? ed.totalArea : 0
						temp.type3.rentHouse3 = ed.rentHouse ? ed.rentHouse : 0
						temp.type3.rentArea3 = ed.rentArea ? ed.rentArea : 0
						temp.type3.notRentHouse3 = ed.notRentHouse ? ed.notRentHouse : 0
						temp.type3.notRentArea3 = ed.notRentArea ? ed.notRentArea : 0
					}
				})

				el[0].tables.push(temp)
			})

			arr.forEach((el) => {
				el[0].tables.forEach((es) => {
					el.lastData = [
						{
							names: '总量(套)',
							totalHouse: es.type1.totalHouse,
							totalHouse2: es.type2.totalHouse2,
							totalHouse3: es.type3.totalHouse3
						},
						{
							names: '总面积(㎡)',
							totalHouse: es.type1.totalArea,
							totalHouse2: es.type2.totalArea2,
							totalHouse3: es.type3.totalArea3
						},
						{
							names: '已租量(套)',
							totalHouse: es.type1.rentHouse,
							totalHouse2: es.type2.rentHouse2,
							totalHouse3: es.type3.rentHouse3
						},
						{
							names: '已租面积(㎡)',
							totalHouse: es.type1.rentArea,
							totalHouse2: es.type2.rentArea2,
							totalHouse3: es.type3.rentArea3
						},
						{
							names: '未租量(套)',
							totalHouse: es.type1.notRentHouse,
							totalHouse2: es.type2.notRentHouse2,
							totalHouse3: es.type3.notRentHouse3
						}
					]
				})
				listData2.value.push(el)
			})

			setTimeout(() => {
				if (listData2.value.length > 0) {
					listData2.value.forEach((el) => {
						if (el[0].lonlat && el[0].type == 2) {
							let itemLonlat = el[0].lonlat.split(',')
							initMarker(itemLonlat[0], itemLonlat[1], el)
						}
					})
				}
			}, 10)
		})
	}

	const loadModule = (parameter) => {
		moduleApi.modulePage2(parameter).then((res) => {
			res.records.splice(0, 1)
			modules.value = res.records
		})
	}

	onMounted(() => {
		init()
		loadModule()

		window.goDetail = (e) => {
			handleBreak(e)
		}
	})

	onUnmounted(() => {
		gaodeMap.value && gaodeMap.value.destroy()
	})

	defineExpose({
		renderMarker,
		renderCircleMarker,
		renderSimpleMarker,
		renderAwesomeMarker,
		renderPolyline,
		renderCircle,
		renderPolygon,
		renderInfoWindow,
		openInfoWindow,
		clearOverlay,
		initMarker
	})
</script>

<style lang="less">
	.xn-wh {
		width: 100%;
		height: 100%;
	}
	.gaodeMap {
		padding: 0;
		margin: 0;
		width: 100%;

		input[type='radio'] {
			-webkit-appearance: radio;
		}

		input[type='checkbox'] {
			-webkit-appearance: checkbox;
		}
	}

	.amap-marker-label {
		position: absolute;
		z-index: 2;
		border: 1px solid #ccc;
		background-color: white;
		white-space: nowrap;
		cursor: default;
		padding: 6px;
		font-size: 12px;
		line-height: 14px;
	}

	.outsideContainerOne {
		width: 350px;
		height: 209px;
		position: relative;
		display: flex;
		justify-content: space-between;
		flex-wrap: nowrap;

		.outsideContainerTwo {
			width: 100%;
			height: 100%;
			object-fit: cover;
			position: absolute;
			top: 0;
			left: 0;
			z-index: 1;
		}

		.outsideContainerThree {
			background-color: rgba(0, 0, 0, 0.5);
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			padding: 10px;
			box-sizing: border-box;
			z-index: 2;
			display: flex;
			justify-content: center;
			color: aliceblue;
		}

		.outsideContainerFour {
			background-color: rgba(0, 0, 0, 0.5);
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			padding: 10px;
			box-sizing: border-box;
			z-index: 2;
		}
	}
	.th {
		width: 80px;
		line-height: 32px;
		text-align: center;
	}

	.tabBgc {
		background-color: #eff6ff !important;
	}

	.amap-info-close {
		display: none !important;
	}

	.amap-info-content {
		padding: 10px !important;
	}

	.detail-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 150px;
		height: 30px;
		color: #fff;
		background: #1677ff;
		box-shadow: 0px 4px 7px 0px rgba(0, 0, 0, 0.09);
		border-radius: 6px;
		margin: 0 auto;
		margin-top: 10px;
		cursor: pointer;
	}
</style>
